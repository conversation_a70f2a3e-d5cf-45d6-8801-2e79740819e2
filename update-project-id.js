#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update Reown Project ID in the frontend
 * Usage: node update-project-id.js YOUR_PROJECT_ID_HERE
 */

const fs = require('fs');
const path = require('path');

// Get project ID from command line argument
const projectId = process.argv[2];

if (!projectId) {
    console.log('❌ Please provide your Project ID');
    console.log('');
    console.log('Usage: node update-project-id.js YOUR_PROJECT_ID_HERE');
    console.log('');
    console.log('📋 Steps to get your Project ID:');
    console.log('1. Go to https://cloud.reown.com');
    console.log('2. Create account/login');
    console.log('3. Create new project named "Werant"');
    console.log('4. Copy your Project ID');
    console.log('5. Run: node update-project-id.js YOUR_PROJECT_ID');
    console.log('');
    process.exit(1);
}

// Validate project ID format (should be 32 characters)
if (projectId.length !== 32) {
    console.log('⚠️  Warning: Project ID should be 32 characters long');
    console.log('   Your ID:', projectId);
    console.log('   Length:', projectId.length);
    console.log('');
    console.log('Please double-check your Project ID from https://cloud.reown.com');
    console.log('');
}

// Update the frontend file
const frontendFile = path.join(__dirname, 'reown-appkit-frontend.html');

try {
    // Read the file
    let content = fs.readFileSync(frontendFile, 'utf8');
    
    // Replace the project ID
    const oldPattern = /const projectId = '[^']*'/;
    const newValue = `const projectId = '${projectId}'`;
    
    if (content.match(oldPattern)) {
        content = content.replace(oldPattern, newValue);
        
        // Write back to file
        fs.writeFileSync(frontendFile, content);
        
        console.log('✅ Project ID updated successfully!');
        console.log('');
        console.log('📁 Updated file: reown-appkit-frontend.html');
        console.log('🆔 Project ID:', projectId);
        console.log('');
        console.log('🚀 Next steps:');
        console.log('1. Open reown-appkit-frontend.html in your browser');
        console.log('2. Click "Connect Wallet" to test');
        console.log('3. You should see 300+ wallets available');
        console.log('');
        console.log('🌐 Your app will now work on any domain you configure in Reown Cloud');
        
    } else {
        console.log('❌ Could not find project ID pattern in file');
        console.log('Please manually update the projectId variable in reown-appkit-frontend.html');
    }
    
} catch (error) {
    console.log('❌ Error updating file:', error.message);
    console.log('');
    console.log('Manual update instructions:');
    console.log('1. Open reown-appkit-frontend.html');
    console.log('2. Find: const projectId = \'YOUR_PROJECT_ID_HERE\'');
    console.log('3. Replace with: const projectId = \'' + projectId + '\'');
    console.log('4. Save the file');
}
