<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - Working WalletConnect</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 3rem;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .gradient-btn {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 8px;
            padding: 18px 36px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.2s ease;
            transform: scale(1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
            border: none;
            cursor: pointer;
            font-size: 18px;
            background: linear-gradient(to right, #9333ea 20%, #ec4899 80%);
            color: white;
            width: 100%;
        }
        
        .gradient-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
            background: linear-gradient(to right, #7c3aed, #db2777);
        }
        
        .wallet-widget {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 24px;
            backdrop-filter: blur(10px);
            color: #1a1b23;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid rgba(76, 175, 80, 0.5); }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid rgba(244, 67, 54, 0.5); }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid rgba(33, 150, 243, 0.5); }
        .warning { background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.5); }
        
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fee-info h3 {
            color: #ff6b6b;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Werant</h1>
            <p>Working WalletConnect Integration</p>
        </div>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <div id="connectSection">
                <button id="connectBtn" class="gradient-btn">
                    <i class="fas fa-wallet"></i>
                    Connect Wallet
                </button>
            </div>
            
            <div id="walletWidget" class="wallet-widget" style="display: none;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="width: 12px; height: 12px; background: #10b981; border-radius: 50%; display: inline-block;"></span>
                        <span style="font-weight: 600;">Connected</span>
                    </div>
                    <button id="disconnectBtn" style="background: none; border: none; color: #ef4444; cursor: pointer; padding: 6px;">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                
                <div>
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Address:</span>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span id="walletAddress" style="font-family: monospace; font-size: 14px;"></span>
                            <button id="copyBtn" style="background: none; border: none; color: #9333ea; cursor: pointer; padding: 4px;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="font-size: 14px; color: #6b7280;">Balance:</span>
                        <span id="walletBalance" style="font-size: 14px;">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" class="status info">
            Loading WalletConnect...
        </div>
    </div>

    <!-- Load WalletConnect Web3Modal -->
    <script type="module">
        import { createWeb3Modal, defaultConfig } from 'https://unpkg.com/@web3modal/ethers@4.1.11/dist/index.js'

        // Your project ID from https://cloud.reown.com
        const projectId = '3203e3196ca7682fb5394a53b725d357'

        // Define Monad Testnet
        const monadTestnet = {
            chainId: 10143,
            name: 'Monad Testnet',
            currency: 'MON',
            explorerUrl: 'https://testnet-explorer.monad.xyz',
            rpcUrl: 'https://testnet-rpc.monad.xyz'
        }

        // Create a metadata object
        const metadata = {
            name: 'Werant',
            description: 'Blockchain voting platform on Monad',
            url: 'https://werant.app',
            icons: ['https://avatars.githubusercontent.com/u/37784886']
        }

        // Create Ethers config
        const ethersConfig = defaultConfig({
            metadata,
            enableEIP6963: true,
            enableInjected: true,
            enableCoinbase: true,
            rpcUrl: 'https://testnet-rpc.monad.xyz',
            defaultChainId: 10143
        })

        // Create a Web3Modal instance
        const modal = createWeb3Modal({
            ethersConfig,
            chains: [monadTestnet],
            projectId,
            enableAnalytics: true,
            themeMode: 'light',
            themeVariables: {
                '--w3m-accent': '#9333ea'
            }
        })

        // Global variables
        let userAddress = null;
        let isConnected = false;

        // Utility functions
        function shortenAddr(addr) {
            if (!addr) return '';
            return addr.slice(0, 5) + "..." + addr.slice(-5);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateWalletUI() {
            const connectSection = document.getElementById('connectSection');
            const walletWidget = document.getElementById('walletWidget');

            if (isConnected && userAddress) {
                connectSection.style.display = 'none';
                walletWidget.style.display = 'block';
                
                document.getElementById('walletAddress').textContent = shortenAddr(userAddress);
                loadWalletBalance();
            } else {
                connectSection.style.display = 'block';
                walletWidget.style.display = 'none';
            }
        }

        async function loadWalletBalance() {
            try {
                if (userAddress) {
                    const provider = new ethers.providers.JsonRpcProvider('https://testnet-rpc.monad.xyz');
                    const balance = await provider.getBalance(userAddress);
                    const balanceInMON = ethers.utils.formatEther(balance);
                    document.getElementById('walletBalance').textContent = `${parseFloat(balanceInMON).toFixed(4)} MON`;
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('walletBalance').textContent = 'Error loading balance';
            }
        }

        // Event listeners
        modal.subscribeAccount((account) => {
            if (account.isConnected && account.address) {
                userAddress = account.address;
                isConnected = true;
                updateWalletUI();
                updateStatus('Wallet connected successfully! 🎉', 'success');
            } else {
                userAddress = null;
                isConnected = false;
                updateWalletUI();
                updateStatus('Wallet disconnected', 'info');
            }
        });

        // Setup event listeners
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('connectBtn').addEventListener('click', () => {
                modal.open();
            });

            document.getElementById('disconnectBtn').addEventListener('click', () => {
                modal.disconnect();
            });

            document.getElementById('copyBtn').addEventListener('click', async () => {
                if (userAddress) {
                    try {
                        await navigator.clipboard.writeText(userAddress);
                        updateStatus('Address copied to clipboard! 📋', 'success');
                    } catch (error) {
                        updateStatus('Failed to copy address', 'error');
                    }
                }
            });

            updateStatus('Ready! Click "Connect Wallet" to see 300+ wallet options.', 'success');
        });

        // Make modal available globally for debugging
        window.web3Modal = modal;
        window.debugModal = () => {
            console.log('Web3Modal:', modal);
            console.log('Connected:', isConnected);
            console.log('Address:', userAddress);
        };
    </script>
</body>
</html>
