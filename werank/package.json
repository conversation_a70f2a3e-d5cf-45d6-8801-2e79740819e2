{"name": "react-ethers-appkit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reown/appkit": "1.7.15", "@reown/appkit-adapter-ethers": "1.7.15", "ethers": "^6.13.2", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.2.0"}}