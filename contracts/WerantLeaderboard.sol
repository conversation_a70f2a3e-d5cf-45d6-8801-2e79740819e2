// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title WerantLeaderboard
 * @dev Smart contract for voting system with gas fee of 0.1 MON per vote
 */
contract WerantLeaderboard is Ownable, ReentrancyGuard {
    // Constants
    uint256 public constant VOTING_FEE = 0.1 ether; // 0.1 MON

    // Structs
    struct Project {
        uint256 id;
        string name;
        string description;
        address creator;
        uint256 totalVotes;
        uint256 totalScore;
        bool isActive;
        uint256 createdAt;
    }
    
    struct Vote {
        uint256 projectId;
        address voter;
        uint256 score; // 1-5 rating
        string comment;
        uint256 timestamp;
    }
    
    // State variables
    uint256 private _projectIds;
    uint256 private _voteIds;
    
    mapping(uint256 => Project) public projects;
    mapping(uint256 => Vote) public votes;
    mapping(address => mapping(uint256 => bool)) public hasVoted; // voter => projectId => hasVoted
    mapping(uint256 => uint256[]) public projectVotes; // projectId => voteIds[]
    
    uint256[] public activeProjects;
    
    // Events
    event ProjectCreated(
        uint256 indexed projectId,
        string name,
        address indexed creator,
        uint256 timestamp
    );
    
    event VoteCast(
        uint256 indexed voteId,
        uint256 indexed projectId,
        address indexed voter,
        uint256 score,
        string comment,
        uint256 feePaid,
        uint256 timestamp
    );
    
    event ProjectStatusChanged(
        uint256 indexed projectId,
        bool isActive
    );
    
    event FeesWithdrawn(
        address indexed owner,
        uint256 amount
    );
    
    // Modifiers
    modifier projectExists(uint256 projectId) {
        require(projectId > 0 && projectId <= _projectIds, "Project does not exist");
        _;
    }
    
    modifier projectActive(uint256 projectId) {
        require(projects[projectId].isActive, "Project is not active");
        _;
    }
    
    modifier hasNotVoted(uint256 projectId) {
        require(!hasVoted[msg.sender][projectId], "Already voted for this project");
        _;
    }
    
    modifier validScore(uint256 score) {
        require(score >= 1 && score <= 5, "Score must be between 1 and 5");
        _;
    }
    
    constructor() Ownable(msg.sender) {}
    
    /**
     * @dev Create a new project
     */
    function createProject(
        string memory name,
        string memory description
    ) external returns (uint256) {
        require(bytes(name).length > 0, "Project name cannot be empty");
        
        _projectIds++;
        uint256 newProjectId = _projectIds;
        
        projects[newProjectId] = Project({
            id: newProjectId,
            name: name,
            description: description,
            creator: msg.sender,
            totalVotes: 0,
            totalScore: 0,
            isActive: true,
            createdAt: block.timestamp
        });
        
        activeProjects.push(newProjectId);
        
        emit ProjectCreated(newProjectId, name, msg.sender, block.timestamp);
        
        return newProjectId;
    }
    
    /**
     * @dev Vote for a project with 0.1 MON fee
     */
    function vote(
        uint256 projectId,
        uint256 score,
        string memory comment
    ) 
        external 
        payable 
        nonReentrant
        projectExists(projectId)
        projectActive(projectId)
        hasNotVoted(projectId)
        validScore(score)
    {
        require(msg.value >= VOTING_FEE, "Insufficient voting fee");
        
        _voteIds++;
        uint256 newVoteId = _voteIds;
        
        // Record the vote
        votes[newVoteId] = Vote({
            projectId: projectId,
            voter: msg.sender,
            score: score,
            comment: comment,
            timestamp: block.timestamp
        });
        
        // Update project stats
        projects[projectId].totalVotes++;
        projects[projectId].totalScore += score;
        
        // Mark as voted
        hasVoted[msg.sender][projectId] = true;
        
        // Add vote to project's vote list
        projectVotes[projectId].push(newVoteId);
        
        emit VoteCast(
            newVoteId,
            projectId,
            msg.sender,
            score,
            comment,
            msg.value,
            block.timestamp
        );
        
        // Refund excess payment
        if (msg.value > VOTING_FEE) {
            payable(msg.sender).transfer(msg.value - VOTING_FEE);
        }
    }
    
    /**
     * @dev Get project details
     */
    function getProject(uint256 projectId) 
        external 
        view 
        projectExists(projectId)
        returns (Project memory) 
    {
        return projects[projectId];
    }
    
    /**
     * @dev Get project average score
     */
    function getProjectAverageScore(uint256 projectId) 
        external 
        view 
        projectExists(projectId)
        returns (uint256) 
    {
        if (projects[projectId].totalVotes == 0) {
            return 0;
        }
        return (projects[projectId].totalScore * 100) / projects[projectId].totalVotes; // Multiply by 100 for precision
    }
    
    /**
     * @dev Get leaderboard (top projects by average score)
     */
    function getLeaderboard(uint256 limit) 
        external 
        view 
        returns (uint256[] memory projectIds, uint256[] memory averageScores) 
    {
        uint256 activeCount = activeProjects.length;
        if (limit > activeCount) {
            limit = activeCount;
        }
        
        projectIds = new uint256[](limit);
        averageScores = new uint256[](limit);
        
        // Simple sorting - for production, consider more efficient sorting
        uint256[] memory tempIds = new uint256[](activeCount);
        uint256[] memory tempScores = new uint256[](activeCount);
        
        // Calculate average scores
        for (uint256 i = 0; i < activeCount; i++) {
            uint256 projectId = activeProjects[i];
            if (projects[projectId].isActive) {
                tempIds[i] = projectId;
                if (projects[projectId].totalVotes > 0) {
                    tempScores[i] = (projects[projectId].totalScore * 100) / projects[projectId].totalVotes;
                }
            }
        }
        
        // Simple bubble sort (consider more efficient sorting for production)
        for (uint256 i = 0; i < activeCount - 1; i++) {
            for (uint256 j = 0; j < activeCount - i - 1; j++) {
                if (tempScores[j] < tempScores[j + 1]) {
                    // Swap scores
                    uint256 tempScore = tempScores[j];
                    tempScores[j] = tempScores[j + 1];
                    tempScores[j + 1] = tempScore;
                    
                    // Swap IDs
                    uint256 tempId = tempIds[j];
                    tempIds[j] = tempIds[j + 1];
                    tempIds[j + 1] = tempId;
                }
            }
        }
        
        // Return top results
        for (uint256 i = 0; i < limit; i++) {
            projectIds[i] = tempIds[i];
            averageScores[i] = tempScores[i];
        }
        
        return (projectIds, averageScores);
    }
    
    /**
     * @dev Get total projects count
     */
    function getTotalProjects() external view returns (uint256) {
        return _projectIds;
    }

    /**
     * @dev Get total votes count
     */
    function getTotalVotes() external view returns (uint256) {
        return _voteIds;
    }
    
    /**
     * @dev Toggle project active status (only owner)
     */
    function toggleProjectStatus(uint256 projectId) 
        external 
        onlyOwner 
        projectExists(projectId) 
    {
        projects[projectId].isActive = !projects[projectId].isActive;
        emit ProjectStatusChanged(projectId, projects[projectId].isActive);
    }
    
    /**
     * @dev Get contract balance
     */
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev Withdraw collected fees (only owner)
     */
    function withdrawFees() external onlyOwner nonReentrant {
        uint256 balance = address(this).balance;
        require(balance > 0, "No fees to withdraw");
        
        payable(owner()).transfer(balance);
        
        emit FeesWithdrawn(owner(), balance);
    }
    
    /**
     * @dev Emergency withdraw (only owner)
     */
    function emergencyWithdraw() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
}
