// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title WerantWalletConnect
 * @dev Smart contract for handling wallet connections with gas fee of 0.25 MON
 */
contract WerantWalletConnect is Ownable, ReentrancyGuard {
    // Constants
    uint256 public constant CONNECTION_FEE = 0.25 ether; // 0.25 MON

    // State variables
    uint256 private _connectionIds;
    mapping(address => bool) public connectedWallets;
    mapping(address => uint256) public connectionTimestamp;
    mapping(address => uint256) public connectionId;
    
    // Events
    event WalletConnected(
        address indexed wallet,
        uint256 indexed connectionId,
        uint256 timestamp,
        uint256 feePaid
    );
    
    event WalletDisconnected(
        address indexed wallet,
        uint256 timestamp
    );
    
    event FeeWithdrawn(
        address indexed owner,
        uint256 amount
    );
    
    // Modifiers
    modifier onlyConnectedWallet() {
        require(connectedWallets[msg.sender], "Wallet not connected");
        _;
    }
    
    modifier notConnected() {
        require(!connectedWallets[msg.sender], "Wallet already connected");
        _;
    }
    
    constructor() Ownable(msg.sender) {}
    
    /**
     * @dev Connect wallet by paying the connection fee
     */
    function connectWallet() external payable nonReentrant notConnected {
        require(msg.value >= CONNECTION_FEE, "Insufficient connection fee");
        
        _connectionIds++;
        uint256 newConnectionId = _connectionIds;
        
        connectedWallets[msg.sender] = true;
        connectionTimestamp[msg.sender] = block.timestamp;
        connectionId[msg.sender] = newConnectionId;
        
        emit WalletConnected(
            msg.sender,
            newConnectionId,
            block.timestamp,
            msg.value
        );
        
        // Refund excess payment
        if (msg.value > CONNECTION_FEE) {
            payable(msg.sender).transfer(msg.value - CONNECTION_FEE);
        }
    }
    
    /**
     * @dev Disconnect wallet
     */
    function disconnectWallet() external onlyConnectedWallet {
        connectedWallets[msg.sender] = false;
        connectionTimestamp[msg.sender] = 0;
        connectionId[msg.sender] = 0;
        
        emit WalletDisconnected(msg.sender, block.timestamp);
    }
    
    /**
     * @dev Check if wallet is connected
     */
    function isWalletConnected(address wallet) external view returns (bool) {
        return connectedWallets[wallet];
    }
    
    /**
     * @dev Get wallet connection details
     */
    function getConnectionDetails(address wallet) 
        external 
        view 
        returns (
            bool isConnected,
            uint256 timestamp,
            uint256 id
        ) 
    {
        return (
            connectedWallets[wallet],
            connectionTimestamp[wallet],
            connectionId[wallet]
        );
    }
    
    /**
     * @dev Get total connected wallets count
     */
    function getTotalConnections() external view returns (uint256) {
        return _connectionIds;
    }
    
    /**
     * @dev Get contract balance
     */
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev Withdraw collected fees (only owner)
     */
    function withdrawFees() external onlyOwner nonReentrant {
        uint256 balance = address(this).balance;
        require(balance > 0, "No fees to withdraw");
        
        payable(owner()).transfer(balance);
        
        emit FeeWithdrawn(owner(), balance);
    }
    
    /**
     * @dev Emergency withdraw (only owner)
     */
    function emergencyWithdraw() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
    
    /**
     * @dev Update connection fee (only owner)
     */
    function updateConnectionFee(uint256 newFee) external onlyOwner {
        // Note: This would require updating the constant, 
        // so this function is for future upgradeable versions
        revert("Connection fee is fixed at 0.25 MON");
    }
}
