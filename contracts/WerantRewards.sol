// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title WerantRewards
 * @dev Smart contract for batch reward distribution to top voters
 */
contract WerantRewards is Ownable, ReentrancyGuard {
    
    // Events
    event BatchRewardSent(
        address[] recipients,
        uint256[] amounts,
        uint256 totalAmount,
        uint256 timestamp
    );
    
    event SingleRewardSent(
        address indexed recipient,
        uint256 amount,
        uint256 timestamp
    );
    
    event FundsDeposited(
        address indexed depositor,
        uint256 amount,
        uint256 timestamp
    );
    
    // State variables
    uint256 public totalRewardsDistributed;
    uint256 public totalRewardRecipients;
    mapping(address => uint256) public totalRewardsReceived;
    mapping(address => uint256) public rewardCount;
    
    constructor() Ownable(msg.sender) {}
    
    /**
     * @dev Deposit funds to the contract for reward distribution
     */
    function depositFunds() external payable {
        require(msg.value > 0, "Must deposit some MON");
        
        emit FundsDeposited(msg.sender, msg.value, block.timestamp);
    }
    
    /**
     * @dev Batch send rewards to multiple addresses
     * @param recipients Array of recipient addresses
     * @param amounts Array of reward amounts (in wei)
     */
    function batchSendRewards(
        address[] calldata recipients,
        uint256[] calldata amounts
    ) external onlyOwner nonReentrant {
        require(recipients.length > 0, "No recipients provided");
        require(recipients.length == amounts.length, "Arrays length mismatch");
        require(recipients.length <= 100, "Too many recipients (max 100)");
        
        uint256 totalAmount = 0;
        
        // Calculate total amount needed
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "Reward amount must be greater than 0");
            totalAmount += amounts[i];
        }
        
        require(address(this).balance >= totalAmount, "Insufficient contract balance");
        
        // Send rewards
        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "Invalid recipient address");
            
            // Update recipient stats
            if (totalRewardsReceived[recipients[i]] == 0) {
                totalRewardRecipients++;
            }
            totalRewardsReceived[recipients[i]] += amounts[i];
            rewardCount[recipients[i]]++;
            
            // Send reward
            payable(recipients[i]).transfer(amounts[i]);
        }
        
        totalRewardsDistributed += totalAmount;
        
        emit BatchRewardSent(recipients, amounts, totalAmount, block.timestamp);
    }
    
    /**
     * @dev Send reward to a single address
     * @param recipient Recipient address
     * @param amount Reward amount (in wei)
     */
    function sendSingleReward(
        address recipient,
        uint256 amount
    ) external onlyOwner nonReentrant {
        require(recipient != address(0), "Invalid recipient address");
        require(amount > 0, "Reward amount must be greater than 0");
        require(address(this).balance >= amount, "Insufficient contract balance");
        
        // Update recipient stats
        if (totalRewardsReceived[recipient] == 0) {
            totalRewardRecipients++;
        }
        totalRewardsReceived[recipient] += amount;
        rewardCount[recipient]++;
        totalRewardsDistributed += amount;
        
        // Send reward
        payable(recipient).transfer(amount);
        
        emit SingleRewardSent(recipient, amount, block.timestamp);
    }
    
    /**
     * @dev Get contract balance
     */
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev Get reward statistics for an address
     */
    function getRewardStats(address recipient) external view returns (
        uint256 totalReceived,
        uint256 rewardTimes
    ) {
        return (
            totalRewardsReceived[recipient],
            rewardCount[recipient]
        );
    }
    
    /**
     * @dev Get overall reward statistics
     */
    function getOverallStats() external view returns (
        uint256 totalDistributed,
        uint256 totalRecipients,
        uint256 contractBalance
    ) {
        return (
            totalRewardsDistributed,
            totalRewardRecipients,
            address(this).balance
        );
    }
    
    /**
     * @dev Emergency withdraw (only owner)
     */
    function emergencyWithdraw() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
    
    /**
     * @dev Withdraw specific amount (only owner)
     */
    function withdraw(uint256 amount) external onlyOwner {
        require(amount <= address(this).balance, "Insufficient balance");
        payable(owner()).transfer(amount);
    }
}
