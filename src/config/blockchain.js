const { ethers } = require('ethers');

// Monad Testnet configuration
const MONAD_TESTNET_CONFIG = {
  chainId: parseInt(process.env.MONAD_TESTNET_CHAIN_ID) || 41454,
  name: 'Monad Testnet',
  rpcUrl: process.env.MONAD_TESTNET_RPC_URL || 'https://testnet-rpc.monad.xyz',
  explorerUrl: process.env.MONAD_TESTNET_EXPLORER || 'https://testnet-explorer.monad.xyz',
  nativeCurrency: {
    name: 'M<PERSON>',
    symbol: 'MON',
    decimals: 18
  }
};

// Gas prices in wei
const GAS_PRICES = {
  WALLET_CONNECT: process.env.WALLET_CONNECT_GAS_PRICE || '250000000000000000', // 0.25 MON
  VOTING: process.env.VOTING_GAS_PRICE || '100000000000000000', // 0.1 MON
  DEFAULT_GAS_LIMIT: process.env.DEFAULT_GAS_LIMIT || '500000'
};

// Contract addresses (will be set after deployment)
const CONTRACT_ADDRESSES = {
  WALLET_CONNECT: process.env.WALLET_CONNECT_CONTRACT_ADDRESS || '',
  LEADERBOARD: process.env.LEADERBOARD_CONTRACT_ADDRESS || ''
};

// Create provider
const provider = new ethers.providers.JsonRpcProvider(MONAD_TESTNET_CONFIG.rpcUrl);

// Create wallet for backend operations (if private key is provided)
let backendWallet = null;
if (process.env.ADMIN_PRIVATE_KEY) {
  backendWallet = new ethers.Wallet(process.env.ADMIN_PRIVATE_KEY, provider);
}

// Contract ABIs (simplified for essential functions)
const WALLET_CONNECT_ABI = [
  "function connectWallet() external payable",
  "function disconnectWallet() external",
  "function isWalletConnected(address wallet) external view returns (bool)",
  "function getConnectionDetails(address wallet) external view returns (bool isConnected, uint256 timestamp, uint256 id)",
  "function getTotalConnections() external view returns (uint256)",
  "function getContractBalance() external view returns (uint256)",
  "event WalletConnected(address indexed wallet, uint256 indexed connectionId, uint256 timestamp, uint256 feePaid)",
  "event WalletDisconnected(address indexed wallet, uint256 timestamp)"
];

const LEADERBOARD_ABI = [
  "function createProject(string memory name, string memory description) external returns (uint256)",
  "function vote(uint256 projectId, uint256 score, string memory comment) external payable",
  "function getProject(uint256 projectId) external view returns (tuple(uint256 id, string name, string description, address creator, uint256 totalVotes, uint256 totalScore, bool isActive, uint256 createdAt))",
  "function getProjectAverageScore(uint256 projectId) external view returns (uint256)",
  "function getLeaderboard(uint256 limit) external view returns (uint256[] memory projectIds, uint256[] memory averageScores)",
  "function getTotalProjects() external view returns (uint256)",
  "function getTotalVotes() external view returns (uint256)",
  "function getContractBalance() external view returns (uint256)",
  "event ProjectCreated(uint256 indexed projectId, string name, address indexed creator, uint256 timestamp)",
  "event VoteCast(uint256 indexed voteId, uint256 indexed projectId, address indexed voter, uint256 score, string comment, uint256 feePaid, uint256 timestamp)"
];

// Helper functions
const formatMON = (wei) => {
  return ethers.utils.formatEther(wei);
};

const parseMON = (mon) => {
  return ethers.utils.parseEther(mon.toString());
};

const getContractInstance = (contractAddress, abi, signerOrProvider = provider) => {
  if (!contractAddress) {
    throw new Error('Contract address not provided');
  }
  return new ethers.Contract(contractAddress, abi, signerOrProvider);
};

// Validation functions
const isValidAddress = (address) => {
  return ethers.utils.isAddress(address);
};

const isValidChainId = (chainId) => {
  return chainId === MONAD_TESTNET_CONFIG.chainId;
};

module.exports = {
  MONAD_TESTNET_CONFIG,
  GAS_PRICES,
  CONTRACT_ADDRESSES,
  provider,
  backendWallet,
  WALLET_CONNECT_ABI,
  LEADERBOARD_ABI,
  formatMON,
  parseMON,
  getContractInstance,
  isValidAddress,
  isValidChainId
};
