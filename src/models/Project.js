const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
  projectId: {
    type: Number,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  creator: {
    type: String,
    required: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return /^0x[a-fA-F0-9]{40}$/.test(v);
      },
      message: 'Invalid creator address format'
    }
  },
  totalVotes: {
    type: Number,
    default: 0,
    min: 0
  },
  totalScore: {
    type: Number,
    default: 0,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  // Blockchain data
  transactionHash: {
    type: String,
    required: true
  },
  blockNumber: {
    type: Number,
    required: true
  },
  // Additional metadata
  category: {
    type: String,
    enum: ['DeFi', 'NFT', 'Gaming', 'Infrastructure', 'Social', 'Other'],
    default: 'Other'
  },
  website: String,
  github: String,
  twitter: String,
  discord: String,
  logo: String,
  tags: [String]
}, {
  timestamps: true
});

// Indexes
projectSchema.index({ projectId: 1 });
projectSchema.index({ creator: 1 });
projectSchema.index({ isActive: 1 });
projectSchema.index({ totalVotes: -1 });
projectSchema.index({ createdAt: -1 });

// Virtual for average score
projectSchema.virtual('averageScore').get(function() {
  if (this.totalVotes === 0) return 0;
  return (this.totalScore / this.totalVotes).toFixed(2);
});

// Virtual for average score percentage (out of 5)
projectSchema.virtual('averageScorePercentage').get(function() {
  if (this.totalVotes === 0) return 0;
  return ((this.totalScore / this.totalVotes) / 5 * 100).toFixed(1);
});

// Methods
projectSchema.methods.addVote = function(score) {
  this.totalVotes += 1;
  this.totalScore += score;
  return this.save();
};

projectSchema.methods.toggleActive = function() {
  this.isActive = !this.isActive;
  return this.save();
};

// Static methods
projectSchema.statics.getLeaderboard = function(limit = 10) {
  return this.find({ isActive: true })
    .sort({ totalVotes: -1, totalScore: -1 })
    .limit(limit);
};

projectSchema.statics.getTopRated = function(limit = 10) {
  return this.aggregate([
    { $match: { isActive: true, totalVotes: { $gt: 0 } } },
    {
      $addFields: {
        averageScore: { $divide: ['$totalScore', '$totalVotes'] }
      }
    },
    { $sort: { averageScore: -1, totalVotes: -1 } },
    { $limit: limit }
  ]);
};

projectSchema.statics.getProjectStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalProjects: { $sum: 1 },
        activeProjects: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        totalVotes: { $sum: '$totalVotes' },
        averageVotesPerProject: { $avg: '$totalVotes' }
      }
    }
  ]);
};

projectSchema.statics.getProjectsByCategory = function() {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 },
        totalVotes: { $sum: '$totalVotes' },
        averageScore: {
          $avg: {
            $cond: [
              { $gt: ['$totalVotes', 0] },
              { $divide: ['$totalScore', '$totalVotes'] },
              0
            ]
          }
        }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

module.exports = mongoose.model('Project', projectSchema);
