const mongoose = require('mongoose');

const walletConnectionSchema = new mongoose.Schema({
  walletAddress: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return /^0x[a-fA-F0-9]{40}$/.test(v);
      },
      message: 'Invalid wallet address format'
    }
  },
  connectionId: {
    type: Number,
    required: true
  },
  isConnected: {
    type: Boolean,
    default: true
  },
  connectionTimestamp: {
    type: Date,
    default: Date.now
  },
  disconnectionTimestamp: {
    type: Date,
    default: null
  },
  transactionHash: {
    type: String,
    required: true
  },
  feePaid: {
    type: String, // Store as string to avoid precision issues
    required: true
  },
  blockNumber: {
    type: Number,
    required: true
  },
  gasUsed: {
    type: String,
    required: true
  },
  // Additional metadata
  userAgent: String,
  ipAddress: String,
  sessionId: String
}, {
  timestamps: true
});

// Indexes
walletConnectionSchema.index({ walletAddress: 1 });
walletConnectionSchema.index({ connectionTimestamp: -1 });
walletConnectionSchema.index({ isConnected: 1 });

// Virtual for connection duration
walletConnectionSchema.virtual('connectionDuration').get(function() {
  if (!this.isConnected && this.disconnectionTimestamp) {
    return this.disconnectionTimestamp - this.connectionTimestamp;
  }
  return Date.now() - this.connectionTimestamp;
});

// Methods
walletConnectionSchema.methods.disconnect = function() {
  this.isConnected = false;
  this.disconnectionTimestamp = new Date();
  return this.save();
};

// Static methods
walletConnectionSchema.statics.getActiveConnections = function() {
  return this.find({ isConnected: true }).sort({ connectionTimestamp: -1 });
};

walletConnectionSchema.statics.getTotalConnections = function() {
  return this.countDocuments();
};

walletConnectionSchema.statics.getConnectionStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalConnections: { $sum: 1 },
        activeConnections: {
          $sum: { $cond: [{ $eq: ['$isConnected', true] }, 1, 0] }
        },
        totalFeesCollected: { $sum: { $toDouble: '$feePaid' } }
      }
    }
  ]);
};

module.exports = mongoose.model('WalletConnection', walletConnectionSchema);
