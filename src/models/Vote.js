const mongoose = require('mongoose');

const voteSchema = new mongoose.Schema({
  voteId: {
    type: Number,
    required: true,
    unique: true
  },
  projectId: {
    type: Number,
    required: true,
    ref: 'Project'
  },
  voter: {
    type: String,
    required: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return /^0x[a-fA-F0-9]{40}$/.test(v);
      },
      message: 'Invalid voter address format'
    }
  },
  score: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  comment: {
    type: String,
    trim: true,
    maxlength: 500
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  // Blockchain data
  transactionHash: {
    type: String,
    required: true
  },
  blockNumber: {
    type: Number,
    required: true
  },
  feePaid: {
    type: String, // Store as string to avoid precision issues
    required: true
  },
  gasUsed: {
    type: String,
    required: true
  },
  // Additional metadata
  userAgent: String,
  ipAddress: String,
  sessionId: String
}, {
  timestamps: true
});

// Compound indexes
voteSchema.index({ projectId: 1, voter: 1 }, { unique: true }); // One vote per voter per project
voteSchema.index({ voter: 1 });
voteSchema.index({ projectId: 1 });
voteSchema.index({ timestamp: -1 });
voteSchema.index({ score: 1 });

// Virtual for score percentage
voteSchema.virtual('scorePercentage').get(function() {
  return (this.score / 5 * 100).toFixed(1);
});

// Static methods
voteSchema.statics.getVotesByProject = function(projectId) {
  return this.find({ projectId }).sort({ timestamp: -1 });
};

voteSchema.statics.getVotesByVoter = function(voter) {
  return this.find({ voter }).sort({ timestamp: -1 });
};

voteSchema.statics.getVoteStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalVotes: { $sum: 1 },
        averageScore: { $avg: '$score' },
        totalFeesCollected: { $sum: { $toDouble: '$feePaid' } },
        scoreDistribution: {
          $push: '$score'
        }
      }
    },
    {
      $addFields: {
        scoreDistribution: {
          score1: {
            $size: {
              $filter: {
                input: '$scoreDistribution',
                cond: { $eq: ['$$this', 1] }
              }
            }
          },
          score2: {
            $size: {
              $filter: {
                input: '$scoreDistribution',
                cond: { $eq: ['$$this', 2] }
              }
            }
          },
          score3: {
            $size: {
              $filter: {
                input: '$scoreDistribution',
                cond: { $eq: ['$$this', 3] }
              }
            }
          },
          score4: {
            $size: {
              $filter: {
                input: '$scoreDistribution',
                cond: { $eq: ['$$this', 4] }
              }
            }
          },
          score5: {
            $size: {
              $filter: {
                input: '$scoreDistribution',
                cond: { $eq: ['$$this', 5] }
              }
            }
          }
        }
      }
    }
  ]);
};

voteSchema.statics.getRecentVotes = function(limit = 10) {
  return this.find()
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('projectId', 'name');
};

voteSchema.statics.getTopVoters = function(limit = 10) {
  return this.aggregate([
    {
      $group: {
        _id: '$voter',
        totalVotes: { $sum: 1 },
        averageScore: { $avg: '$score' },
        totalFeesContributed: { $sum: { $toDouble: '$feePaid' } }
      }
    },
    { $sort: { totalVotes: -1 } },
    { $limit: limit }
  ]);
};

voteSchema.statics.hasVoted = function(voter, projectId) {
  return this.findOne({ voter, projectId });
};

module.exports = mongoose.model('Vote', voteSchema);
