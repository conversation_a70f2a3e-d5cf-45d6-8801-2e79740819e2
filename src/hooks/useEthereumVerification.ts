'use client'

import { useState, useEffect } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import { 
  checkEthereumTransactionHistory, 
  cacheTransactionHistory, 
  getCachedTransactionHistory,
  type TransactionHistory 
} from '@/services/ethereumVerification'

export const useEthereumVerification = () => {
  const { address, isConnected } = useAppKitAccount()
  const [verificationStatus, setVerificationStatus] = useState<TransactionHistory | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Check verification status when wallet connects
  useEffect(() => {
    const checkVerification = async () => {
      if (!isConnected || !address) {
        setVerificationStatus(null)
        setIsLoading(false)
        setError(null)
        return
      }

      try {
        setIsLoading(true)
        setError(null)

        // Check cache first
        const cached = getCachedTransactionHistory(address)
        if (cached) {
          setVerificationStatus(cached)
          setIsLoading(false)
          return
        }

        // Check Ethereum mainnet transaction history
        console.log(`🔍 Verifying Ethereum history for: ${address}`)
        const history = await checkEthereumTransactionHistory(address)
        
        setVerificationStatus(history)
        
        // Cache the result
        cacheTransactionHistory(history)
        
        console.log(`✅ Verification complete:`, history)
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Verification failed'
        setError(errorMessage)
        console.error('❌ Ethereum verification error:', err)
        
        // Set failed verification status
        setVerificationStatus({
          address,
          transactionCount: 0,
          hasMinimumTxs: false,
          isEligible: false,
          checkedAt: Date.now(),
          error: errorMessage
        })
      } finally {
        setIsLoading(false)
      }
    }

    checkVerification()
  }, [address, isConnected])

  // Force refresh verification
  const refreshVerification = async () => {
    if (!address) return

    try {
      setIsLoading(true)
      setError(null)

      // Clear cache
      const cacheKey = `eth_tx_history_${address.toLowerCase()}`
      localStorage.removeItem(cacheKey)

      // Re-check
      const history = await checkEthereumTransactionHistory(address)
      setVerificationStatus(history)
      cacheTransactionHistory(history)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Verification failed'
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Clear verification status
  const clearVerification = () => {
    setVerificationStatus(null)
    setError(null)
    setIsLoading(false)
  }

  return {
    verificationStatus,
    isLoading,
    error,
    isEligible: verificationStatus?.isEligible || false,
    transactionCount: verificationStatus?.transactionCount || 0,
    hasMinimumTxs: verificationStatus?.hasMinimumTxs || false,
    refreshVerification,
    clearVerification
  }
}
