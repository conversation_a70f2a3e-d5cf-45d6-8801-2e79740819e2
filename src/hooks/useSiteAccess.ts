'use client'

import { useState, useEffect } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import { usePaymentStatus } from './usePaymentStatus'
import { useEthereumVerification } from './useEthereumVerification'

export type AccessStage = 'loading' | 'connect_wallet' | 'payment_required' | 'eth_verification_required' | 'access_granted' | 'access_denied'

export const useSiteAccess = () => {
  const { address, isConnected } = useAppKitAccount()
  const { hasPaid, isLoading: paymentLoading } = usePaymentStatus()
  const { isEligible: isEthEligible, isLoading: ethLoading } = useEthereumVerification()
  
  const [accessStage, setAccessStage] = useState<AccessStage>('loading')
  const [isLoading, setIsLoading] = useState(true)
  const [accessGranted, setAccessGranted] = useState(false)

  useEffect(() => {
    const checkAccess = () => {
      // Step 1: Check if wallet is connected
      if (!isConnected || !address) {
        setAccessStage('connect_wallet')
        setAccessGranted(false)
        setIsLoading(false)
        return
      }

      // Step 2: Check if still loading payment or ETH verification
      if (paymentLoading || ethLoading) {
        setAccessStage('loading')
        setIsLoading(true)
        return
      }

      // Step 3: Check payment status first (primary requirement)
      if (!hasPaid) {
        setAccessStage('payment_required')
        setAccessGranted(false)
        setIsLoading(false)
        return
      }

      // Step 4: Check Ethereum verification (secondary requirement)
      if (!isEthEligible) {
        setAccessStage('eth_verification_required')
        setAccessGranted(false)
        setIsLoading(false)
        return
      }

      // Step 5: All requirements met - grant access
      setAccessStage('access_granted')
      setAccessGranted(true)
      setIsLoading(false)
    }

    checkAccess()
  }, [isConnected, address, hasPaid, paymentLoading, isEthEligible, ethLoading])

  // Force refresh all verifications
  const refreshAccess = () => {
    setIsLoading(true)
    setAccessStage('loading')
    // The useEffect will automatically re-run and check access
  }

  // Get access stage message
  const getAccessMessage = (): string => {
    switch (accessStage) {
      case 'loading':
        return 'Checking access requirements...'
      case 'connect_wallet':
        return 'Please connect your wallet to access Werant'
      case 'payment_required':
        return 'Payment required: Pay 0.25 MON activation fee'
      case 'eth_verification_required':
        return 'Ethereum verification required: Need 5+ transactions on ETH mainnet'
      case 'access_granted':
        return 'Access granted! Welcome to Werant'
      case 'access_denied':
        return 'Access denied: Requirements not met'
      default:
        return 'Checking access...'
    }
  }

  // Get access stage icon
  const getAccessIcon = (): string => {
    switch (accessStage) {
      case 'loading':
        return '⏳'
      case 'connect_wallet':
        return '🔗'
      case 'payment_required':
        return '💳'
      case 'eth_verification_required':
        return '🔍'
      case 'access_granted':
        return '✅'
      case 'access_denied':
        return '❌'
      default:
        return '⏳'
    }
  }

  // Check if user can proceed to next step
  const canProceedToPayment = (): boolean => {
    return isConnected && !!address
  }

  const canProceedToEthVerification = (): boolean => {
    return isConnected && !!address && hasPaid
  }

  return {
    accessStage,
    accessGranted,
    isLoading,
    accessMessage: getAccessMessage(),
    accessIcon: getAccessIcon(),
    canProceedToPayment: canProceedToPayment(),
    canProceedToEthVerification: canProceedToEthVerification(),
    refreshAccess,
    
    // Individual status checks
    isConnected,
    hasPaid,
    isEthEligible,
    paymentLoading,
    ethLoading,
    
    // Requirements summary
    requirements: {
      walletConnected: isConnected,
      paymentCompleted: hasPaid,
      ethVerificationPassed: isEthEligible
    }
  }
}
