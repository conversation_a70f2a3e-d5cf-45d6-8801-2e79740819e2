'use client'

import { useState, useEffect } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import { useReadContract } from 'wagmi'

export const usePaymentStatus = () => {
  const { address, isConnected } = useAppKitAccount()
  const [hasPaid, setHasPaid] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(true)

  // WerantWalletConnect contract address and ABI
  const WALLET_CONNECT_CONTRACT = '******************************************'
  const WALLET_CONNECT_ABI = [
    {
      "inputs": [],
      "name": "connectWallet",
      "outputs": [],
      "stateMutability": "payable",
      "type": "function"
    },
    {
      "inputs": [{"internalType": "address", "name": "wallet", "type": "address"}],
      "name": "isWalletConnected",
      "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
      "stateMutability": "view",
      "type": "function"
    }
  ] as const

  // Check smart contract for payment status
  const { data: contractPaidStatus, isError, isLoading: contractLoading } = useReadContract({
    address: WALLET_CONNECT_CONTRACT as `0x${string}`,
    abi: WALLET_CONNECT_ABI,
    functionName: 'isWalletConnected',
    args: address ? [address] : undefined,
    query: {
      enabled: !!address && isConnected,
      refetchInterval: 5000, // Refetch every 5 seconds
    }
  })

  useEffect(() => {
    const checkPaymentStatus = () => {
      if (!isConnected || !address) {
        setHasPaid(false)
        setIsLoading(false)
        return
      }

      // If contract data is available, use it as the primary source
      if (contractPaidStatus !== undefined && !contractLoading) {
        setHasPaid(Boolean(contractPaidStatus))
        setIsLoading(false)

        // Update localStorage to match contract state
        const paymentKey = `werant_paid_${address.toLowerCase()}`
        if (contractPaidStatus) {
          localStorage.setItem(paymentKey, 'true')
        } else {
          localStorage.removeItem(paymentKey)
        }
        return
      }

      // Fallback to localStorage while contract is loading
      if (!contractLoading && isError) {
        const paymentKey = `werant_paid_${address.toLowerCase()}`
        const paidStatus = localStorage.getItem(paymentKey)
        setHasPaid(paidStatus === 'true')
        setIsLoading(false)
        return
      }

      // Still loading contract data
      setIsLoading(contractLoading)
    }

    checkPaymentStatus()
  }, [address, isConnected, contractPaidStatus, contractLoading, isError])

  const markAsPaid = () => {
    if (address) {
      const paymentKey = `werant_paid_${address.toLowerCase()}`
      localStorage.setItem(paymentKey, 'true')
      setHasPaid(true)
    }
  }

  const resetPaymentStatus = () => {
    if (address) {
      const paymentKey = `werant_paid_${address.toLowerCase()}`
      localStorage.removeItem(paymentKey)
      setHasPaid(false)
    }
  }

  // Force refresh payment status from contract
  const refreshPaymentStatus = () => {
    if (address && isConnected) {
      setIsLoading(true)
      // The useReadContract will automatically refetch
    }
  }

  return {
    hasPaid,
    isLoading,
    markAsPaid,
    resetPaymentStatus,
    refreshPaymentStatus,
    contractPaidStatus: Boolean(contractPaidStatus),
    isContractLoading: contractLoading
  }
}
