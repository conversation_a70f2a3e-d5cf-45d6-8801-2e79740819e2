import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'
import { mainnet, arbitrum, polygon, base } from '@reown/appkit/networks'

// Define Monad Testnet
const monadTestnet = {
  id: 10143,
  name: 'Monad Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
  },
  rpcUrls: {
    default: {
      http: ['https://testnet-rpc.monad.xyz']
    }
  },
  blockExplorers: {
    default: {
      name: 'Monad Explorer',
      url: 'https://testnet-explorer.monad.xyz'
    }
  },
  testnet: true
}

// 1. Get projectId from https://dashboard.reown.com
const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || '3203e3196ca7682fb5394a53b725d357'

// 2. Create a metadata object - optional
const metadata = {
  name: 'We<PERSON>',
  description: 'Smart wallet connection with auto-detection and network switching',
  url: 'https://werant.app', // origin must match your domain & subdomain
  icons: ['https://avatars.githubusercontent.com/u/37784886']
}

// 3. Set the networks
const networks = [monadTestnet, mainnet, arbitrum, polygon, base]

// 4. Create Wagmi Adapter
const wagmiAdapter = new WagmiAdapter({
  networks,
  projectId,
  ssr: true
})

// 5. Create modal with 600+ wallet support and smart network handling
createAppKit({
  adapters: [wagmiAdapter],
  networks,
  defaultNetwork: monadTestnet,
  projectId,
  metadata,
  features: {
    analytics: true, // Optional - defaults to your Cloud configuration
    email: true, // Enable email wallets
    socials: ['google', 'x', 'github', 'discord', 'apple'], // Enable social logins
    emailShowWallets: true, // Show wallet options in email flow
    onramp: true, // Enable on-ramp
    swaps: true, // Enable swaps
    smartSessions: true, // Enable smart sessions
    allWallets: true // Show all 600+ wallets
  },
  allowUnsupportedChain: false, // Force network switching
  themeMode: 'light',
  themeVariables: {
    '--w3m-accent': '#3b82f6',
    '--w3m-border-radius-master': '16px',
    '--w3m-font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  }
})

export { wagmiAdapter, projectId }
