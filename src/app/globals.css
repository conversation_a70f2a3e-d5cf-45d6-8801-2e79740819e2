/* Global Styles for Next.js App */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

a {
  color: inherit;
  text-decoration: none;
}

/* App Container */
.app {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
}

/* Header Styles */
.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 3.5rem;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Fee Info */
.fee-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fee-info h3 {
  color: #ff6b6b;
  margin-bottom: 16px;
  font-size: 1.4rem;
}

.fee-info ul {
  list-style: none;
}

.fee-info li {
  margin-bottom: 8px;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Wallet Section */
.wallet-section {
  text-align: center;
  margin: 50px 0;
}

/* Modern Gradient Buttons */
.gradient-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.gradient-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
}

.gradient-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(59, 130, 246, 0.35);
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.gradient-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.gradient-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Connected Wallet */
.connected-wallet {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 30px;
  max-width: 400px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.wallet-info {
  margin-bottom: 20px;
}

.connection-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #4ade80;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse 2s infinite;
}

.wallet-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.wallet-network {
  font-size: 14px;
  opacity: 0.8;
}

/* Features Grid */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin: 60px 0;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 1.4rem;
  margin-bottom: 12px;
  color: #4ecdc4;
}

.feature-card p {
  opacity: 0.9;
  line-height: 1.6;
}

/* Stats Grid */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-top: 60px;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ff6b6b;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.8;
}

.icon {
  font-size: 20px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 20px;
    margin: 10px;
  }
  
  .header h1 {
    font-size: 2.5rem;
  }
  
  .features {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .gradient-btn {
    padding: 16px 28px;
    font-size: 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}
