'use client'

import { useState } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import CustomWalletModal from '@/components/CustomWalletModal'
import ReownWalletButton from '@/components/ReownWalletButton'
import VotingComponent from '@/components/VotingComponent'
import styles from './demo.module.css'

export default function DemoPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { address, isConnected } = useAppKitAccount()

  const shortenAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  return (
    <div className={styles.demoContainer}>
      <div className={styles.demoContent}>
        <div className={styles.header}>
          <h1>🌐 Werant Demo</h1>
          <p>Complete blockchain voting platform: 0.25 MON activation + 0.1 MON per vote</p>
        </div>

        <div className={styles.features}>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>✨</div>
            <h3>Clean Design</h3>
            <p>White modal with subtle shadows and modern typography</p>
          </div>
          
          <div className={styles.feature}>
            <div className={styles.featureIcon}>🎯</div>
            <h3>Smart Detection</h3>
            <p>Automatically detects installed wallets with status tags</p>
          </div>
          
          <div className={styles.feature}>
            <div className={styles.featureIcon}>📱</div>
            <h3>Mobile Ready</h3>
            <p>Responsive design that works perfectly on all devices</p>
          </div>
        </div>

        <div className={styles.demoSection}>
          <h2>Payment System Demo</h2>
          <p>Connect your wallet to see the 0.25 MON activation payment for first-time users</p>

          <div style={{ marginBottom: '32px' }}>
            <ReownWalletButton />
          </div>

          <h3>Custom Modal Demo</h3>
          <p>Or try our custom Dribbble-style modal design</p>

          <button
            className={styles.demoButton}
            onClick={() => setIsModalOpen(true)}
            style={{ background: 'linear-gradient(135deg, #f59e0b, #d97706)' }}
          >
            <span>🎨</span>
            Custom Modal
          </button>

          <div style={{ marginTop: '48px' }}>
            <h3>Voting Demo</h3>
            <p>Try the voting system with 0.1 MON gas fee</p>

            <VotingComponent
              projectId={2}
              projectName="Demo Project"
            />
          </div>
        </div>

        <div className={styles.designNotes}>
          <h3>Design Features</h3>
          <div className={styles.notesList}>
            <div className={styles.note}>
              <strong>Color Palette:</strong> Clean whites with subtle grays and blue accents
            </div>
            <div className={styles.note}>
              <strong>Typography:</strong> Modern font weights with proper letter spacing
            </div>
            <div className={styles.note}>
              <strong>Interactions:</strong> Smooth hover effects with scale and shadow transitions
            </div>
            <div className={styles.note}>
              <strong>Layout:</strong> Generous padding and spacing for better readability
            </div>
          </div>
        </div>

        <CustomWalletModal 
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      </div>
    </div>
  )
}
