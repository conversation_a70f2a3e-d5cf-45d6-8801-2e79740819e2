/* Demo Page Styles */
.demoContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 20px;
}

.demoContent {
  max-width: 1000px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 24px;
  padding: 60px;
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.header {
  text-align: center;
  margin-bottom: 60px;
}

.header h1 {
  font-size: 3rem;
  font-weight: 800;
  color: #0f172a;
  margin-bottom: 16px;
  letter-spacing: -0.025em;
}

.header p {
  font-size: 1.25rem;
  color: #64748b;
  font-weight: 400;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  margin-bottom: 60px;
}

.feature {
  text-align: center;
  padding: 32px;
  background: #f8fafc;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

.featureIcon {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.feature h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 12px;
}

.feature p {
  color: #64748b;
  line-height: 1.6;
}

.demoSection {
  text-align: center;
  margin: 60px 0;
  padding: 40px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 20px;
  border: 1px solid #cbd5e1;
}

.demoSection h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 12px;
}

.demoSection p {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 32px;
}

.demoButton {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
}

.demoButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(59, 130, 246, 0.35);
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.connectedDemo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.connectionInfo {
  background: #ffffff;
  padding: 24px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.statusBadge {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #10b981;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 12px;
  justify-content: center;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.addressDisplay {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 600;
  color: #0f172a;
  background: #f8fafc;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.designNotes {
  margin-top: 60px;
  padding: 40px;
  background: #f8fafc;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
}

.designNotes h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 24px;
  text-align: center;
}

.notesList {
  display: grid;
  gap: 16px;
}

.note {
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  color: #475569;
  line-height: 1.6;
}

.note strong {
  color: #0f172a;
  font-weight: 600;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .demoContent {
    padding: 40px 24px;
  }
  
  .header h1 {
    font-size: 2.5rem;
  }
  
  .features {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .feature {
    padding: 24px;
  }
  
  .demoSection {
    padding: 32px 24px;
  }
  
  .designNotes {
    padding: 32px 24px;
  }
}
