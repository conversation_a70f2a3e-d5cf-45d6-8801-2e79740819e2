import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import SiteGatekeeper from '@/components/SiteGatekeeper'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Werant - Blockchain Voting Platform',
  description: 'Secure blockchain voting with on-chain verification and smart contracts',
  keywords: ['blockchain', 'voting', 'wallet', 'web3', 'monad', 'ethereum', 'verification'],
  authors: [{ name: 'Werant Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#667eea',
  openGraph: {
    title: 'Werant - Blockchain Voting Platform',
    description: 'Secure blockchain voting with on-chain verification',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Werant - Blockchain Voting Platform',
    description: 'Secure blockchain voting with on-chain verification',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      </head>
      <body className={inter.className}>
        <Providers>
          <SiteGatekeeper>
            {children}
          </SiteGatekeeper>
        </Providers>
      </body>
    </html>
  )
}
