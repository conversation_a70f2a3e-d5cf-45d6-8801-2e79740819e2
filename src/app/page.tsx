'use client'

import { useState } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import CustomWalletModal from '@/components/CustomWalletModal'
import ReownWalletButton from '@/components/ReownWalletButton'
import VotingComponent from '@/components/VotingComponent'
import DebugPanel from '@/components/DebugPanel'

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { address, isConnected } = useAppKitAccount()

  const shortenAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  return (
    <div className="app">
      <div className="container">
        <div className="header">
          <h1>🚀 Werant</h1>
          <p>Blockchain Voting Platform with On-Chain Verification</p>
          <div style={{
            marginTop: '20px',
            padding: '16px',
            background: 'rgba(99, 102, 241, 0.1)',
            borderRadius: '12px',
            border: '1px solid rgba(99, 102, 241, 0.2)'
          }}>
            <p style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              fontWeight: '600',
              color: '#6366f1'
            }}>
              🔍 Requirements for Account Creation:
            </p>
            <p style={{
              margin: '0 0 4px 0',
              fontSize: '13px',
              color: '#64748b'
            }}>
              • Minimum 5 transactions on Ethereum mainnet
            </p>
            <p style={{
              margin: '0',
              fontSize: '13px',
              color: '#64748b'
            }}>
              • One-time activation fee: 0.25 MON
            </p>
          </div>
          <div style={{ marginTop: '20px' }}>
            <a
              href="/demo"
              style={{
                color: '#3b82f6',
                textDecoration: 'none',
                fontWeight: '600',
                fontSize: '16px'
              }}
            >
              🎨 View Demo Page →
            </a>
          </div>
        </div>

        <div className="fee-info">
          <h3>💰 Fee Structure</h3>
          <ul>
            <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
            <li><strong>Voting:</strong> 0.1 MON per vote</li>
            <li><strong>Project Creation:</strong> Gas fees only</li>
          </ul>
        </div>

        <div className="wallet-section">
          <ReownWalletButton />

          <div style={{ marginTop: '32px' }}>
            <button
              className="gradient-btn secondary"
              onClick={() => setIsModalOpen(true)}
              style={{ fontSize: '14px', padding: '12px 24px' }}
            >
              🎨 Custom Modal Demo
            </button>
          </div>
        </div>

        {/* Voting Section */}
        <div style={{ marginTop: '48px' }}>
          <VotingComponent
            projectId={1}
            projectName="Werant Platform"
          />
        </div>

        <div className="features">
          <div className="feature-card">
            <div className="feature-icon">🗳️</div>
            <h3>Decentralized Voting</h3>
            <p>Vote on projects and proposals with transparent blockchain technology</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">🏆</div>
            <h3>Leaderboard</h3>
            <p>Track top projects and contributors in the ecosystem</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">💎</div>
            <h3>Rewards</h3>
            <p>Earn MON tokens for participating in governance</p>
          </div>
        </div>

        <div className="stats">
          <div className="stat-item">
            <div className="stat-number">1,234</div>
            <div className="stat-label">Total Votes</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">567</div>
            <div className="stat-label">Active Projects</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">89</div>
            <div className="stat-label">Contributors</div>
          </div>
        </div>

        <CustomWalletModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />

        {/* Debug Panel for testing */}
        <DebugPanel />
      </div>
    </div>
  )
}
