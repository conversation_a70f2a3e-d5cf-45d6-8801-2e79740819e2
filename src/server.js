const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const connectDB = require('./config/database');
const walletRoutes = require('./routes/wallet');
const leaderboardRoutes = require('./routes/leaderboard');
const contractRoutes = require('./routes/contracts');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Connect to database
connectDB();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'Werant Backend',
    version: '1.0.0',
    network: 'Monad Testnet'
  });
});

// API routes
app.use('/api/wallet', walletRoutes);
app.use('/api/leaderboard', leaderboardRoutes);
app.use('/api/contracts', contractRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Werant Backend API',
    version: '1.0.0',
    network: 'Monad Testnet',
    endpoints: {
      health: '/health',
      wallet: '/api/wallet',
      leaderboard: '/api/leaderboard',
      contracts: '/api/contracts'
    },
    documentation: 'https://docs.werant.io'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `The endpoint ${req.method} ${req.originalUrl} does not exist.`,
    availableEndpoints: [
      'GET /health',
      'GET /api/wallet/status',
      'POST /api/wallet/connect',
      'GET /api/leaderboard',
      'POST /api/leaderboard/vote',
      'GET /api/contracts/info'
    ]
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  res.status(err.status || 500).json({
    error: err.message || 'Internal server error',
    timestamp: new Date().toISOString(),
    path: req.path
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Werant backend server running on port ${PORT}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Network: Monad Testnet (Chain ID: ${process.env.MONAD_TESTNET_CHAIN_ID || 41454})`);
  console.log(`📡 RPC URL: ${process.env.MONAD_TESTNET_RPC_URL || 'https://testnet-rpc.monad.xyz'}`);
  console.log(`💰 Wallet Connect Fee: 0.25 MON`);
  console.log(`🗳️  Voting Fee: 0.1 MON`);
  console.log(`🔍 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
