'use client'

import { createPublicClient, http } from 'viem'
import { mainnet } from 'viem/chains'

// Create Ethereum mainnet client
const ethereumClient = createPublicClient({
  chain: mainnet,
  transport: http('https://eth-mainnet.g.alchemy.com/v2/demo') // Using Alchemy's demo endpoint
})

// Alternative RPC endpoints (fallbacks)
const ETHEREUM_RPC_ENDPOINTS = [
  'https://eth-mainnet.g.alchemy.com/v2/demo',
  'https://mainnet.infura.io/v3/********************************', // Public Infura
  'https://ethereum.publicnode.com',
  'https://rpc.ankr.com/eth',
  'https://eth.llamarpc.com'
]

interface TransactionHistory {
  address: string
  transactionCount: number
  hasMinimumTxs: boolean
  isEligible: boolean
  checkedAt: number
  error?: string
}

interface EtherscanResponse {
  status: string
  message: string
  result: Array<{
    blockNumber: string
    timeStamp: string
    hash: string
    from: string
    to: string
    value: string
    gas: string
    gasPrice: string
    isError: string
  }>
}

// Minimum transaction requirement
const MINIMUM_TX_COUNT = 5

/**
 * Check if wallet has minimum transactions on Ethereum mainnet using Etherscan API
 */
export const checkEthereumTransactionHistory = async (address: string): Promise<TransactionHistory> => {
  try {
    // Validate address format
    if (!address || !address.startsWith('0x') || address.length !== 42) {
      throw new Error('Invalid Ethereum address format')
    }

    console.log(`🔍 Checking Ethereum mainnet history for: ${address}`)

    // Try Etherscan API first (most reliable)
    const etherscanResult = await checkWithEtherscan(address)
    if (etherscanResult.transactionCount >= 0) {
      return etherscanResult
    }

    // Fallback to RPC method
    console.log('📡 Etherscan failed, trying RPC method...')
    const rpcResult = await checkWithRPC(address)
    return rpcResult

  } catch (error) {
    console.error('❌ Error checking Ethereum history:', error)
    
    return {
      address,
      transactionCount: 0,
      hasMinimumTxs: false,
      isEligible: false,
      checkedAt: Date.now(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Check transaction history using Etherscan API
 */
async function checkWithEtherscan(address: string): Promise<TransactionHistory> {
  try {
    // Using Etherscan's free API (no key required for basic queries)
    const etherscanUrl = `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&page=1&offset=10&sort=desc&apikey=YourApiKeyToken`
    
    const response = await fetch(etherscanUrl)
    const data: EtherscanResponse = await response.json()

    if (data.status === '1' && Array.isArray(data.result)) {
      const transactionCount = data.result.length
      const hasMinimumTxs = transactionCount >= MINIMUM_TX_COUNT
      
      console.log(`✅ Etherscan: Found ${transactionCount} transactions`)
      
      return {
        address,
        transactionCount,
        hasMinimumTxs,
        isEligible: hasMinimumTxs,
        checkedAt: Date.now()
      }
    } else {
      throw new Error(`Etherscan API error: ${data.message}`)
    }
  } catch (error) {
    console.error('❌ Etherscan check failed:', error)
    throw error
  }
}

/**
 * Check transaction count using RPC method (fallback)
 */
async function checkWithRPC(address: string): Promise<TransactionHistory> {
  try {
    // Get transaction count (nonce) from Ethereum mainnet
    const transactionCount = await ethereumClient.getTransactionCount({
      address: address as `0x${string}`,
      blockTag: 'latest'
    })

    const txCount = Number(transactionCount)
    const hasMinimumTxs = txCount >= MINIMUM_TX_COUNT
    
    console.log(`✅ RPC: Found ${txCount} transactions (nonce)`)
    
    return {
      address,
      transactionCount: txCount,
      hasMinimumTxs,
      isEligible: hasMinimumTxs,
      checkedAt: Date.now()
    }
  } catch (error) {
    console.error('❌ RPC check failed:', error)
    throw error
  }
}

/**
 * Cache transaction history in localStorage
 */
export const cacheTransactionHistory = (history: TransactionHistory): void => {
  try {
    const cacheKey = `eth_tx_history_${history.address.toLowerCase()}`
    const cacheData = {
      ...history,
      cachedAt: Date.now()
    }
    localStorage.setItem(cacheKey, JSON.stringify(cacheData))
    console.log(`💾 Cached transaction history for ${history.address}`)
  } catch (error) {
    console.error('❌ Failed to cache transaction history:', error)
  }
}

/**
 * Get cached transaction history from localStorage
 */
export const getCachedTransactionHistory = (address: string): TransactionHistory | null => {
  try {
    const cacheKey = `eth_tx_history_${address.toLowerCase()}`
    const cached = localStorage.getItem(cacheKey)
    
    if (!cached) return null
    
    const cacheData = JSON.parse(cached)
    const cacheAge = Date.now() - cacheData.cachedAt
    const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours
    
    if (cacheAge > CACHE_DURATION) {
      localStorage.removeItem(cacheKey)
      return null
    }
    
    console.log(`📋 Using cached transaction history for ${address}`)
    return cacheData
  } catch (error) {
    console.error('❌ Failed to get cached transaction history:', error)
    return null
  }
}

/**
 * Format address for display
 */
export const formatAddress = (address: string): string => {
  if (!address) return ''
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

/**
 * Get eligibility message
 */
export const getEligibilityMessage = (history: TransactionHistory): string => {
  if (history.error) {
    return `❌ Error checking eligibility: ${history.error}`
  }
  
  if (history.isEligible) {
    return `✅ Eligible: ${history.transactionCount} transactions on Ethereum mainnet`
  } else {
    const needed = MINIMUM_TX_COUNT - history.transactionCount
    return `❌ Not eligible: Need ${needed} more transactions on Ethereum mainnet (currently ${history.transactionCount})`
  }
}
