'use client'

import { useState } from 'react'
import { useAppKit } from '@reown/appkit/react'
import { useSiteAccess } from '@/hooks/useSiteAccess'
import PaymentModal from './PaymentModal'
import EthereumVerificationModal from './EthereumVerificationModal'
import { usePaymentStatus } from '@/hooks/usePaymentStatus'
import styles from './SiteGatekeeper.module.css'

interface SiteGatekeeperProps {
  children: React.ReactNode
}

const SiteGatekeeper: React.FC<SiteGatekeeperProps> = ({ children }) => {
  const { open } = useAppKit()
  const { markAsPaid } = usePaymentStatus()
  const {
    accessStage,
    accessGranted,
    isLoading,
    accessMessage,
    accessIcon,
    canProceedToPayment,
    canProceedToEthVerification,
    refreshAccess,
    requirements
  } = useSiteAccess()

  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showEthVerificationModal, setShowEthVerificationModal] = useState(false)

  // If access is granted, render the main site
  if (accessGranted) {
    return <>{children}</>
  }

  // Handle payment success
  const handlePaymentSuccess = () => {
    markAsPaid()
    setShowPaymentModal(false)
    refreshAccess()
  }

  // Handle Ethereum verification success
  const handleEthVerificationSuccess = () => {
    setShowEthVerificationModal(false)
    refreshAccess()
  }

  // Handle connect wallet
  const handleConnectWallet = async () => {
    try {
      await open()
    } catch (error) {
      console.error('Failed to open wallet modal:', error)
    }
  }

  // Handle payment button click
  const handlePaymentClick = () => {
    if (canProceedToPayment) {
      setShowPaymentModal(true)
    } else {
      handleConnectWallet()
    }
  }

  // Handle Ethereum verification button click
  const handleEthVerificationClick = () => {
    if (canProceedToEthVerification) {
      setShowEthVerificationModal(true)
    } else if (!requirements.walletConnected) {
      handleConnectWallet()
    } else {
      setShowPaymentModal(true)
    }
  }

  return (
    <div className={styles.gatekeeper}>
      <div className={styles.gatekeeperContent}>
        <div className={styles.logo}>
          <h1>🚀 Werant</h1>
          <p>Blockchain Voting Platform</p>
        </div>

        <div className={styles.accessCard}>
          <div className={styles.accessHeader}>
            <div className={styles.accessIcon}>{accessIcon}</div>
            <h2>Site Access Control</h2>
            <p>{accessMessage}</p>
          </div>

          {isLoading && (
            <div className={styles.loadingState}>
              <div className={styles.spinner}></div>
              <p>Verifying access requirements...</p>
            </div>
          )}

          {!isLoading && (
            <div className={styles.requirementsList}>
              <h3>Access Requirements:</h3>
              
              <div className={styles.requirement}>
                <div className={`${styles.requirementStatus} ${requirements.walletConnected ? styles.completed : styles.pending}`}>
                  {requirements.walletConnected ? '✅' : '⏳'}
                </div>
                <div className={styles.requirementText}>
                  <h4>1. Connect Wallet</h4>
                  <p>Connect your wallet using 600+ supported options</p>
                </div>
                {!requirements.walletConnected && (
                  <button 
                    className={`${styles.actionBtn} ${styles.primary}`}
                    onClick={handleConnectWallet}
                  >
                    Connect Wallet
                  </button>
                )}
              </div>

              <div className={styles.requirement}>
                <div className={`${styles.requirementStatus} ${requirements.paymentCompleted ? styles.completed : requirements.walletConnected ? styles.pending : styles.disabled}`}>
                  {requirements.paymentCompleted ? '✅' : requirements.walletConnected ? '⏳' : '⚪'}
                </div>
                <div className={styles.requirementText}>
                  <h4>2. Activation Payment</h4>
                  <p>One-time fee of 0.25 MON to activate your account</p>
                </div>
                {requirements.walletConnected && !requirements.paymentCompleted && (
                  <button 
                    className={`${styles.actionBtn} ${styles.payment}`}
                    onClick={handlePaymentClick}
                  >
                    Pay 0.25 MON
                  </button>
                )}
              </div>

              <div className={styles.requirement}>
                <div className={`${styles.requirementStatus} ${requirements.ethVerificationPassed ? styles.completed : requirements.paymentCompleted ? styles.pending : styles.disabled}`}>
                  {requirements.ethVerificationPassed ? '✅' : requirements.paymentCompleted ? '⏳' : '⚪'}
                </div>
                <div className={styles.requirementText}>
                  <h4>3. Ethereum Verification</h4>
                  <p>Verify 5+ transactions on Ethereum mainnet</p>
                </div>
                {requirements.paymentCompleted && !requirements.ethVerificationPassed && (
                  <button 
                    className={`${styles.actionBtn} ${styles.verification}`}
                    onClick={handleEthVerificationClick}
                  >
                    Verify Ethereum
                  </button>
                )}
              </div>
            </div>
          )}

          {accessStage === 'access_granted' && (
            <div className={styles.successState}>
              <div className={styles.successIcon}>🎉</div>
              <h3>Welcome to Werant!</h3>
              <p>All requirements met. Redirecting to platform...</p>
            </div>
          )}
        </div>

        <div className={styles.footer}>
          <p>Secure • Decentralized • Transparent</p>
          <div className={styles.networkInfo}>
            <span>Monad Testnet</span>
            <span>•</span>
            <span>Ethereum Mainnet Verified</span>
          </div>
        </div>
      </div>

      {/* Modals */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onPaymentSuccess={handlePaymentSuccess}
      />

      <EthereumVerificationModal
        isOpen={showEthVerificationModal}
        onClose={() => setShowEthVerificationModal(false)}
        onVerificationSuccess={handleEthVerificationSuccess}
      />
    </div>
  )
}

export default SiteGatekeeper
