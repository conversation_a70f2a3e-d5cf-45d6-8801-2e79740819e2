.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.connectPrompt {
  text-align: center;
  padding: 48px 24px;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.connectPrompt p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.controls {
  display: flex;
  gap: 16px;
  align-items: end;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.inputGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.inputGroup input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  width: 120px;
}

.inputGroup input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.refreshBtn {
  padding: 10px 16px;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: fit-content;
}

.refreshBtn:hover:not(:disabled) {
  background: #5856eb;
  transform: translateY(-1px);
}

.refreshBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error {
  padding: 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin-bottom: 24px;
}

.error p {
  color: #dc2626;
  margin: 0;
  font-weight: 500;
}

.summary {
  margin-bottom: 32px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.summary h3 {
  font-size: 20px;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0 0 16px 0;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0f2fe;
}

.summaryItem .label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.summaryItem .value {
  font-size: 16px;
  color: #0f172a;
  font-weight: 600;
}

.votersList {
  margin-bottom: 32px;
}

.votersList h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.votersTable {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.tableHeader {
  display: grid;
  grid-template-columns: 60px 1fr 80px 100px 120px;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.tableRow {
  display: grid;
  grid-template-columns: 60px 1fr 80px 100px 120px;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
  transition: background-color 0.2s ease;
}

.tableRow:hover {
  background: #f9fafb;
}

.tableRow:last-child {
  border-bottom: none;
}

.rank {
  font-weight: 600;
  color: #6366f1;
}

.address {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  color: #374151;
}

.votes {
  font-weight: 600;
  color: #059669;
}

.score {
  font-weight: 500;
  color: #d97706;
}

.reward {
  font-weight: 600;
  color: #dc2626;
}

.distributionSection {
  text-align: center;
  padding: 24px;
  background: #fefce8;
  border-radius: 12px;
  border: 1px solid #fde047;
}

.distributeBtn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.distributeBtn:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
}

.distributeBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .inputGroup {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .inputGroup input {
    width: 100px;
  }
  
  .tableHeader,
  .tableRow {
    grid-template-columns: 50px 1fr 60px 80px 100px;
    gap: 8px;
    padding: 12px;
    font-size: 13px;
  }
  
  .address {
    font-size: 12px;
  }
}
