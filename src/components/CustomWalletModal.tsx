'use client'

import { useState, useEffect } from 'react'
import { useAppKit, useAppKitAccount } from '@reown/appkit/react'
import { useDisconnect } from 'wagmi'
import styles from './CustomWalletModal.module.css'

interface CustomWalletModalProps {
  isOpen: boolean
  onClose: () => void
}

const CustomWalletModal: React.FC<CustomWalletModalProps> = ({ isOpen, onClose }) => {
  const { open } = useAppKit()
  const { address, isConnected } = useAppKitAccount()
  const { disconnect } = useDisconnect()
  const [hasMetaMask, setHasMetaMask] = useState(false)
  const [hasTrustWallet, setHasTrustWallet] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Check for MetaMask
    setHasMetaMask(
      typeof window !== 'undefined' &&
      typeof window.ethereum !== 'undefined' &&
      !!(window.ethereum as any)?.isMetaMask
    )

    // Check for Trust Wallet
    setHasTrustWallet(
      typeof window !== 'undefined' &&
      typeof window.ethereum !== 'undefined' &&
      !!(window.ethereum as any)?.isTrust
    )
  }, [])

  const walletOptions = [
    {
      id: 'reown-appkit',
      name: 'All Wallets',
      description: '600+ wallets, social logins & email',
      icon: '🌐',
      iconBg: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
      tag: '600+',
      tagColor: '#3b82f6',
      available: true,
      action: () => openReownModal(),
    },
    {
      id: 'metamask',
      name: 'MetaMask',
      description: 'Connect using browser extension',
      icon: '🦊',
      iconBg: 'linear-gradient(135deg, #f6851b, #e2761b)',
      tag: hasMetaMask ? 'POPULAR' : 'INSTALL',
      tagColor: hasMetaMask ? '#10b981' : '#f59e0b',
      available: hasMetaMask,
      action: () => openReownModal(),
    },
    {
      id: 'walletconnect',
      name: 'WalletConnect',
      description: 'Scan with WalletConnect to connect',
      icon: '📱',
      iconBg: 'linear-gradient(135deg, #3b99fc, #1a73e8)',
      tag: 'QR CODE',
      tagColor: '#3b82f6',
      available: true,
      action: () => openReownModal(),
    },
    {
      id: 'social',
      name: 'Social Login',
      description: 'Google, X, GitHub, Discord, Apple',
      icon: '👤',
      iconBg: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
      tag: 'SOCIAL',
      tagColor: '#8b5cf6',
      available: true,
      action: () => openReownModal(),
    },
  ]

  const openReownModal = async () => {
    try {
      setIsLoading(true)
      await open()
      onClose()
    } catch (error) {
      console.error('Failed to open Reown modal:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleConnect = async (action: () => Promise<void>) => {
    try {
      await action()
    } catch (error) {
      console.error('Connection failed:', error)
    }
  }

  const handleDisconnect = () => {
    disconnect()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className={styles.walletModalOverlay} onClick={onClose}>
      <div className={styles.walletModal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.walletModalHeader}>
          <h2>Connect a Wallet</h2>
          <button className={styles.closeButton} onClick={onClose}>
            ✕
          </button>
        </div>

        {isConnected ? (
          <div className={styles.connectedState}>
            <div className={styles.connectedInfo}>
              <div className={styles.connectionStatus}>
                <span className={styles.statusDot}></span>
                <span>Connected</span>
              </div>
              <div className={styles.walletAddress}>
                {address?.slice(0, 6)}...{address?.slice(-4)}
              </div>
            </div>
            <button className={styles.disconnectButton} onClick={handleDisconnect}>
              Disconnect
            </button>
          </div>
        ) : (
          <div className={styles.walletOptions}>
            {walletOptions.map((wallet) => (
              <div
                key={wallet.id}
                className={`${styles.walletOption} ${!wallet.available ? styles.disabled : ''}`}
                onClick={() => wallet.available && handleConnect(wallet.action)}
              >
                <div className={styles.walletInfo}>
                  <div
                    className={styles.walletIcon}
                    style={{ background: wallet.iconBg }}
                  >
                    {wallet.icon}
                  </div>
                  <div>
                    <div className={styles.walletName}>{wallet.name}</div>
                    <div className={styles.walletDescription}>{wallet.description}</div>
                  </div>
                </div>
                <span
                  className={styles.walletTag}
                  style={{ backgroundColor: wallet.tagColor }}
                >
                  {wallet.tag}
                </span>
              </div>
            ))}
          </div>
        )}

        {isLoading && (
          <div className={styles.loadingState}>
            <div className={styles.spinner}></div>
            <p>Opening Reown AppKit...</p>
          </div>
        )}

        <div className={styles.modalFooter}>
          <p className={styles.termsText}>
            By connecting, I accept Werant&apos;s{' '}
            <a href="#" className={styles.termsLink}>Terms of Service</a> and{' '}
            <a href="#" className={styles.termsLink}>Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  )
}

export default CustomWalletModal
