/* Debug Panel Styles */
.debugPanel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 16px;
  border-radius: 12px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  max-width: 300px;
  z-index: 1000;
}

.debugPanel h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #3b82f6;
}

.debugInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.debugRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debugRow span:first-child {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.success {
  color: #10b981;
}

.error {
  color: #ef4444;
}

.loading {
  color: #f59e0b;
}

.debugActions {
  margin-bottom: 16px;
}

.resetBtn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resetBtn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.contractInfo {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.contractInfo p {
  margin: 4px 0;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

/* Hide on mobile */
@media (max-width: 768px) {
  .debugPanel {
    display: none;
  }
}
