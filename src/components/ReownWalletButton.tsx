'use client'

import { useAppKit, useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react'
import { useEffect, useState } from 'react'
import { usePaymentStatus } from '@/hooks/usePaymentStatus'
import { useEthereumVerification } from '@/hooks/useEthereumVerification'
import PaymentModal from './PaymentModal'
import EthereumVerificationModal from './EthereumVerificationModal'
import styles from './ReownWalletButton.module.css'

export default function ReownWalletButton() {
  const { open } = useAppKit()
  const { address, isConnected } = useAppKitAccount()
  const { chainId } = useAppKitNetwork()
  const { hasPaid, isLoading: paymentLoading, markAsPaid, refreshPaymentStatus } = usePaymentStatus()
  const { isEligible: isEthereumEligible, isLoading: ethVerificationLoading } = useEthereumVerification()
  const [isWrongNetwork, setIsWrongNetwork] = useState(false)
  const [showEthVerificationModal, setShowEthVerificationModal] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [ethVerificationPassed, setEthVerificationPassed] = useState(false)

  // Monad Testnet chain ID
  const MONAD_TESTNET_ID = 10143

  // Check if user is on wrong network
  useEffect(() => {
    if (isConnected && chainId) {
      setIsWrongNetwork(chainId !== MONAD_TESTNET_ID)
    } else {
      setIsWrongNetwork(false)
    }
  }, [isConnected, chainId])

  // Check verification and payment status when user connects
  useEffect(() => {
    if (isConnected && !isWrongNetwork) {
      // Add a small delay to ensure wallet connection is fully established
      const timer = setTimeout(() => {
        // First check Ethereum verification
        if (!ethVerificationLoading && !isEthereumEligible && !ethVerificationPassed) {
          setShowEthVerificationModal(true)
        }
        // Then check payment status (only if Ethereum verification passed)
        else if ((isEthereumEligible || ethVerificationPassed) && !paymentLoading && !hasPaid) {
          setShowPaymentModal(true)
        }
      }, 1000)

      return () => clearTimeout(timer)
    } else {
      setShowEthVerificationModal(false)
      setShowPaymentModal(false)
    }
  }, [isConnected, isWrongNetwork, ethVerificationLoading, isEthereumEligible, ethVerificationPassed, paymentLoading, hasPaid])

  // Refresh payment status when wallet connects
  useEffect(() => {
    if (isConnected && address) {
      refreshPaymentStatus()
    }
  }, [isConnected, address, refreshPaymentStatus])

  const shortenAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  const handleConnect = async () => {
    try {
      await open()
    } catch (error) {
      console.error('Failed to open wallet modal:', error)
    }
  }

  const handleNetworkSwitch = async () => {
    try {
      await open({ view: 'Networks' })
    } catch (error) {
      console.error('Failed to open network selector:', error)
    }
  }

  const handleEthVerificationSuccess = () => {
    setEthVerificationPassed(true)
    setShowEthVerificationModal(false)
    // After Ethereum verification, show payment modal if not paid
    if (!hasPaid) {
      setTimeout(() => {
        setShowPaymentModal(true)
      }, 500)
    }
  }

  const handlePaymentSuccess = () => {
    markAsPaid()
    setShowPaymentModal(false)
  }

  if (isConnected && address) {
    if (isWrongNetwork) {
      return (
        <div className={styles.connectedState}>
          <div className={styles.walletInfo}>
            <div className={`${styles.connectionIndicator} ${styles.wrongNetwork}`}>
              <span className={`${styles.statusDot} ${styles.warning}`}></span>
              <span>Wrong Network</span>
            </div>
            <div className={styles.walletAddress}>
              {shortenAddress(address)}
            </div>
            <div className={styles.walletNetwork}>
              Please switch to Monad Testnet
            </div>
          </div>
          <button
            className={`${styles.connectBtn} ${styles.warning}`}
            onClick={handleNetworkSwitch}
          >
            <span className={styles.icon}>🔄</span>
            Switch to Monad
          </button>
        </div>
      )
    }

    return (
      <div className={styles.connectedState}>
        <div className={styles.walletInfo}>
          <div className={styles.connectionIndicator}>
            <span className={styles.statusDot}></span>
            <span>Connected</span>
          </div>
          <div className={styles.walletAddress}>
            {shortenAddress(address)}
          </div>
          <div className={styles.walletNetwork}>
            Monad Testnet • Reown AppKit
          </div>
        </div>
        <button
          className={`${styles.connectBtn} ${styles.secondary}`}
          onClick={() => open()}
        >
          <span className={styles.icon}>⚙️</span>
          Manage Wallet
        </button>
      </div>
    )
  }

  return (
    <>
      <button
        className={`${styles.connectBtn} ${styles.primary}`}
        onClick={handleConnect}
      >
        <span className={styles.icon}>🔗</span>
        Connect Wallet
      </button>

      <EthereumVerificationModal
        isOpen={showEthVerificationModal}
        onClose={() => setShowEthVerificationModal(false)}
        onVerificationSuccess={handleEthVerificationSuccess}
      />

      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onPaymentSuccess={handlePaymentSuccess}
      />
    </>
  )
}
