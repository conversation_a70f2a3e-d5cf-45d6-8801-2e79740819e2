/* Payment Modal Styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 24px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s ease-out;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.content {
  text-align: center;
}

.paymentInfo {
  margin-bottom: 24px;
}

.amountDisplay {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  color: white;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.amount {
  display: block;
  font-size: 48px;
  font-weight: 900;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.currency {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 600;
}

.benefits {
  text-align: left;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.benefits h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefits li {
  padding: 8px 0;
  font-size: 15px;
  color: #475569;
  font-weight: 500;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.payBtn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.payBtn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.payBtn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.payBtn.primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.payBtn.secondary {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
  border: 2px solid rgba(100, 116, 139, 0.2);
}

.payBtn.secondary:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: translateY(-1px);
}

.icon {
  font-size: 20px;
}

.statusDisplay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.2);
  border-left: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.successIcon {
  font-size: 48px;
  color: #10b981;
}

.errorIcon {
  font-size: 48px;
  color: #ef4444;
}

.statusDisplay p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.closeBtn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(100, 116, 139, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    padding: 24px;
    margin: 20px;
  }
  
  .amount {
    font-size: 36px;
  }
  
  .payBtn {
    padding: 16px 24px;
    font-size: 16px;
  }
  
  .benefits {
    padding: 20px;
  }
}
