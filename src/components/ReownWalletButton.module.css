/* Reown AppKit Wallet Button Styles */
.connectBtn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 36px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 280px;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.connectBtn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.3);
  border: 2px solid transparent;
}

.connectBtn.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.connectBtn.primary:active {
  transform: translateY(-1px);
}

.connectBtn.secondary {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.connectBtn.secondary:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.5);
}

.connectBtn.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: 2px solid rgba(245, 158, 11, 0.3);
  backdrop-filter: blur(10px);
}

.connectBtn.warning:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  border-color: rgba(245, 158, 11, 0.5);
}

.icon {
  font-size: 22px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.connectedState {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24px;
  padding: 32px;
  max-width: 450px;
  margin: 0 auto;
  border: 2px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  backdrop-filter: blur(20px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.walletInfo {
  margin-bottom: 24px;
}

.connectionIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 16px;
  font-weight: 700;
  color: #10b981;
  font-size: 16px;
}

.connectionIndicator.wrongNetwork {
  color: #f59e0b;
}

.statusDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
}

.statusDot.warning {
  background: #f59e0b;
  animation: pulseWarning 2s infinite;
  box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
}

.walletAddress {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 12px;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.walletNetwork {
  font-size: 14px;
  opacity: 0.9;
  color: #e2e8f0;
  font-weight: 500;
}

/* Pulse animation for status dot */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes pulseWarning {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .connectBtn {
    padding: 16px 28px;
    font-size: 16px;
    min-width: 240px;
  }
  
  .connectedState {
    padding: 24px;
    margin: 0 10px;
  }
  
  .walletAddress {
    font-size: 18px;
    padding: 10px 16px;
  }
  
  .connectionIndicator {
    font-size: 14px;
  }
  
  .icon {
    font-size: 20px;
  }
}

/* Loading state */
.connectBtn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

/* Focus states for accessibility */
.connectBtn:focus {
  outline: none;
  ring: 3px solid rgba(59, 130, 246, 0.5);
}

/* Gradient animation on hover */
.connectBtn.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.connectBtn.primary:hover::before {
  left: 100%;
}
