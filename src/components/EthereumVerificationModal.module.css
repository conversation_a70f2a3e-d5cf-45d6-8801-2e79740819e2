/* Ethereum Verification Modal Styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 24px;
  padding: 40px;
  max-width: 550px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s ease-out;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.content {
  text-align: center;
}

.requirementInfo {
  margin-bottom: 32px;
}

.requirementCard {
  display: flex;
  align-items: center;
  gap: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20px;
  padding: 24px;
  color: white;
  text-align: left;
}

.requirementIcon {
  font-size: 32px;
  flex-shrink: 0;
}

.requirementText h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.requirementText p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

.walletInfo {
  margin-bottom: 24px;
}

.walletDisplay {
  background: rgba(99, 102, 241, 0.1);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.walletLabel {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
}

.walletAddress {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 700;
  color: #6366f1;
}

.statusDisplay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  margin-bottom: 24px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(99, 102, 241, 0.2);
  border-left: 4px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.successIcon {
  font-size: 48px;
  color: #10b981;
}

.warningIcon {
  font-size: 48px;
  color: #f59e0b;
}

.errorIcon {
  font-size: 48px;
  color: #ef4444;
}

.statusDisplay p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.subText {
  font-size: 14px !important;
  color: #64748b !important;
  font-weight: 400 !important;
}

.errorText {
  font-size: 14px !important;
  color: #ef4444 !important;
  font-weight: 400 !important;
}

.transactionInfo {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin: 24px 0;
}

.txCount,
.txRequirement {
  text-align: center;
}

.txNumber,
.reqNumber {
  display: block;
  font-size: 32px;
  font-weight: 900;
  color: #6366f1;
}

.txLabel,
.reqLabel {
  display: block;
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailsBtn {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.detailsBtn:hover {
  background: rgba(99, 102, 241, 0.2);
}

.details {
  background: rgba(99, 102, 241, 0.05);
  border-radius: 16px;
  padding: 20px;
  margin-top: 16px;
  text-align: left;
}

.details h4 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
  font-size: 14px;
}

.detailRow span:first-child {
  font-weight: 600;
  color: #64748b;
}

.detailRow span:last-child {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1e293b;
}

.eligible {
  color: #10b981 !important;
  font-weight: 700 !important;
}

.notEligible {
  color: #ef4444 !important;
  font-weight: 700 !important;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notEligibleActions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.helpText {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  padding: 16px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.actionBtn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 16px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  width: 100%;
}

.actionBtn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.actionBtn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
}

.actionBtn.secondary {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 2px solid rgba(99, 102, 241, 0.2);
}

.actionBtn.secondary:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.actionBtn.demo {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 2px solid rgba(245, 158, 11, 0.2);
}

.actionBtn.demo:hover {
  background: rgba(245, 158, 11, 0.2);
  transform: translateY(-1px);
}

.icon {
  font-size: 18px;
}

.closeBtn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(100, 116, 139, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    padding: 24px;
    margin: 20px;
  }
  
  .requirementCard {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .transactionInfo {
    gap: 24px;
  }
  
  .actionBtn {
    padding: 16px 24px;
    font-size: 15px;
  }
}
