/* Voting Component Styles */
.votingCard {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 32px;
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h3 {
  font-size: 24px;
  font-weight: 800;
  margin: 0 0 8px 0;
  color: white;
}

.header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 500;
}

.connectPrompt,
.paymentPrompt {
  text-align: center;
  padding: 24px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.connectPrompt p,
.paymentPrompt p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.votingForm {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.scoreSection,
.commentSection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scoreSection label,
.commentSection label {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.scoreInput {
  display: flex;
  align-items: center;
  gap: 16px;
}

.slider {
  flex: 1;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.scoreValue {
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
}

.commentInput {
  width: 100%;
  min-height: 100px;
  padding: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  backdrop-filter: blur(10px);
}

.commentInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.commentInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.commentInput:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.charCount {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: right;
}

.feeInfo {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.feeDisplay {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feeLabel {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.feeAmount {
  font-size: 20px;
  font-weight: 800;
  color: #10b981;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 12px;
}

.voteBtn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.voteBtn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.voteBtn.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.voteBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.icon {
  font-size: 20px;
}

.statusDisplay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(16, 185, 129, 0.2);
  border-left: 4px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.successIcon {
  font-size: 48px;
  color: #10b981;
}

.errorIcon {
  font-size: 48px;
  color: #ef4444;
}

.statusDisplay p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .votingCard {
    padding: 24px;
    margin: 0 10px;
  }
  
  .header h3 {
    font-size: 20px;
  }
  
  .voteBtn {
    padding: 16px 24px;
    font-size: 16px;
  }
  
  .scoreInput {
    flex-direction: column;
    gap: 12px;
  }
  
  .feeDisplay {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
