'use client'

import { useState } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import { useEthereumVerification } from '@/hooks/useEthereumVerification'
import { formatAddress, getEligibilityMessage } from '@/services/ethereumVerification'
import styles from './EthereumVerificationModal.module.css'

interface EthereumVerificationModalProps {
  isOpen: boolean
  onClose: () => void
  onVerificationSuccess: () => void
}

const EthereumVerificationModal: React.FC<EthereumVerificationModalProps> = ({ 
  isOpen, 
  onClose, 
  onVerificationSuccess 
}) => {
  const { address } = useAppKitAccount()
  const { 
    verificationStatus, 
    isLoading, 
    error, 
    isEligible, 
    transactionCount,
    refreshVerification 
  } = useEthereumVerification()

  const [showDetails, setShowDetails] = useState(false)

  const handleContinue = () => {
    if (isEligible) {
      onVerificationSuccess()
      onClose()
    }
  }

  const handleSkipForDemo = () => {
    // For demo purposes, allow skipping verification
    onVerificationSuccess()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2>🔍 Ethereum Verification</h2>
          <p>Verify your wallet activity on Ethereum mainnet</p>
        </div>

        <div className={styles.content}>
          <div className={styles.requirementInfo}>
            <div className={styles.requirementCard}>
              <div className={styles.requirementIcon}>⚡</div>
              <div className={styles.requirementText}>
                <h3>Activity Requirement</h3>
                <p>Your wallet must have at least <strong>5 transactions</strong> on Ethereum mainnet to create an account</p>
              </div>
            </div>
          </div>

          {address && (
            <div className={styles.walletInfo}>
              <div className={styles.walletDisplay}>
                <span className={styles.walletLabel}>Checking Wallet:</span>
                <span className={styles.walletAddress}>{formatAddress(address)}</span>
              </div>
            </div>
          )}

          {isLoading && (
            <div className={styles.statusDisplay}>
              <div className={styles.spinner}></div>
              <p>Checking Ethereum mainnet history...</p>
              <p className={styles.subText}>This may take a few seconds</p>
            </div>
          )}

          {error && !isLoading && (
            <div className={styles.statusDisplay}>
              <div className={styles.errorIcon}>❌</div>
              <p>Verification Error</p>
              <p className={styles.errorText}>{error}</p>
              <button 
                className={`${styles.actionBtn} ${styles.secondary}`}
                onClick={refreshVerification}
              >
                🔄 Try Again
              </button>
            </div>
          )}

          {verificationStatus && !isLoading && !error && (
            <div className={styles.statusDisplay}>
              <div className={isEligible ? styles.successIcon : styles.warningIcon}>
                {isEligible ? '✅' : '⚠️'}
              </div>
              <p>{getEligibilityMessage(verificationStatus)}</p>
              
              <div className={styles.transactionInfo}>
                <div className={styles.txCount}>
                  <span className={styles.txNumber}>{transactionCount}</span>
                  <span className={styles.txLabel}>Transactions Found</span>
                </div>
                <div className={styles.txRequirement}>
                  <span className={styles.reqNumber}>5</span>
                  <span className={styles.reqLabel}>Required</span>
                </div>
              </div>

              {!showDetails && (
                <button 
                  className={styles.detailsBtn}
                  onClick={() => setShowDetails(true)}
                >
                  📊 Show Details
                </button>
              )}

              {showDetails && (
                <div className={styles.details}>
                  <h4>Verification Details</h4>
                  <div className={styles.detailRow}>
                    <span>Wallet Address:</span>
                    <span>{address}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span>Network:</span>
                    <span>Ethereum Mainnet</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span>Transactions:</span>
                    <span>{transactionCount}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span>Checked At:</span>
                    <span>{new Date(verificationStatus.checkedAt).toLocaleString()}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span>Status:</span>
                    <span className={isEligible ? styles.eligible : styles.notEligible}>
                      {isEligible ? 'Eligible ✅' : 'Not Eligible ❌'}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {verificationStatus && !isLoading && (
            <div className={styles.actions}>
              {isEligible ? (
                <button 
                  className={`${styles.actionBtn} ${styles.primary}`}
                  onClick={handleContinue}
                >
                  <span className={styles.icon}>✅</span>
                  Continue to Payment
                </button>
              ) : (
                <div className={styles.notEligibleActions}>
                  <p className={styles.helpText}>
                    Your wallet needs more transaction history on Ethereum mainnet. 
                    Try using a different wallet or complete more transactions.
                  </p>
                  <button 
                    className={`${styles.actionBtn} ${styles.secondary}`}
                    onClick={refreshVerification}
                  >
                    🔄 Check Again
                  </button>
                  <button 
                    className={`${styles.actionBtn} ${styles.demo}`}
                    onClick={handleSkipForDemo}
                  >
                    ⏭️ Skip for Demo
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        <button 
          className={styles.closeBtn}
          onClick={onClose}
        >
          ×
        </button>
      </div>
    </div>
  )
}

export default EthereumVerificationModal
