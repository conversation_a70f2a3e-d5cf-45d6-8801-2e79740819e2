/* Modern Dribbble-Style Wallet Modal - Clean White Design */
.walletModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(12px);
  animation: fadeIn 0.3s ease-out;
}

.walletModal {
  background: #ffffff;
  border-radius: 24px;
  width: 100%;
  max-width: 420px;
  margin: 20px;
  box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.08);
  animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;
}

.walletModalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid #f1f5f9;
}

.walletModalHeader h2 {
  color: #0f172a;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.closeButton {
  background: #f8fafc;
  border: none;
  color: #64748b;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: #e2e8f0;
  color: #334155;
}

.walletOptions {
  padding: 0 32px 32px 32px;
}

.walletOption {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  border: 2px solid #f1f5f9;
  position: relative;
}

.walletOption:hover:not(.disabled) {
  background: #f8fafc;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.walletOption.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: #f8fafc;
}

.walletInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.walletIcon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #e2e8f0;
  font-size: 24px;
  position: relative;
}

.walletName {
  color: #0f172a;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.walletDescription {
  color: #64748b;
  font-size: 14px;
  font-weight: 400;
  margin-top: 2px;
}

.walletTag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: absolute;
  top: 16px;
  right: 16px;
}

.connectedState {
  padding: 32px;
  text-align: center;
}

.connectedInfo {
  background: #f8fafc;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.connectionStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #10b981;
  font-weight: 600;
  font-size: 14px;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.walletAddress {
  color: #0f172a;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 600;
  background: #ffffff;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.disconnectButton {
  background: #ef4444;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.disconnectButton:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.loadingState {
  padding: 32px;
  text-align: center;
  color: #64748b;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px auto;
}

.modalFooter {
  padding: 0 32px 32px 32px;
  border-top: 1px solid #f1f5f9;
  margin-top: 8px;
  padding-top: 24px;
}

.termsText {
  color: #64748b;
  font-size: 13px;
  text-align: center;
  margin: 0;
  line-height: 1.5;
}

.termsLink {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
  font-weight: 500;
}

.termsLink:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(32px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .walletModal {
    margin: 10px;
    max-width: none;
  }
  
  .walletModalHeader {
    padding: 20px 20px 12px 20px;
  }
  
  .walletOptions {
    padding: 12px 20px 20px 20px;
  }
  
  .walletOption {
    padding: 14px;
  }
  
  .walletName {
    font-size: 15px;
  }
  
  .walletTag {
    font-size: 10px;
    padding: 3px 6px;
  }
}
