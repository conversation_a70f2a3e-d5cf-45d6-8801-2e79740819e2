/* Site Gatekeeper Styles */
.gatekeeper {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.gatekeeper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

.gatekeeperContent {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 48px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 32px 80px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

.logo {
  text-align: center;
  margin-bottom: 40px;
}

.logo h1 {
  font-size: 48px;
  font-weight: 900;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.accessCard {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  margin-bottom: 32px;
}

.accessHeader {
  text-align: center;
  margin-bottom: 32px;
}

.accessIcon {
  font-size: 64px;
  margin-bottom: 16px;
}

.accessHeader h2 {
  font-size: 24px;
  font-weight: 800;
  margin: 0 0 8px 0;
  color: #1e293b;
}

.accessHeader p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-left: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.requirementsList {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.requirementsList h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.requirement {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.requirement:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.requirementStatus {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.requirementStatus.completed {
  background: rgba(16, 185, 129, 0.1);
  border: 2px solid #10b981;
}

.requirementStatus.pending {
  background: rgba(245, 158, 11, 0.1);
  border: 2px solid #f59e0b;
  animation: pulse 2s infinite;
}

.requirementStatus.disabled {
  background: rgba(148, 163, 184, 0.1);
  border: 2px solid #94a3b8;
  opacity: 0.5;
}

.requirementText {
  flex: 1;
}

.requirementText h4 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #1e293b;
}

.requirementText p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.actionBtn {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.actionBtn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.actionBtn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.actionBtn.payment {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.actionBtn.payment:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

.actionBtn.verification {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.actionBtn.verification:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

.successState {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  text-align: center;
}

.successIcon {
  font-size: 64px;
  animation: bounce 1s ease-in-out infinite;
}

.successState h3 {
  font-size: 24px;
  font-weight: 800;
  margin: 0;
  color: #10b981;
}

.successState p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.footer {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

.footer p {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.networkInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 14px;
  opacity: 0.8;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .gatekeeper {
    padding: 10px;
  }
  
  .gatekeeperContent {
    padding: 32px 24px;
  }
  
  .logo h1 {
    font-size: 36px;
  }
  
  .requirement {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .requirementText {
    order: -1;
  }
  
  .actionBtn {
    width: 100%;
  }
}
