'use client'

import { useState, useEffect } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import styles from './RewardDistribution.module.css'

interface TopVoter {
  address: string
  totalVotes: number
  averageScore: string
  totalFeesContributed: string
  lastVoteDate: string
  suggestedRewardMON: string
  suggestedRewardWei: string
  rank: number
}

interface RewardData {
  topVoters: TopVoter[]
  summary: {
    totalVoters: number
    totalSuggestedReward: string
    totalSuggestedRewardWei: string
    minVotes: number
    limit: number
  }
  batchData: {
    recipients: string[]
    amounts: string[]
  }
}

const REWARDS_CONTRACT = '0x...' // Will be updated after deployment
const REWARDS_ABI = [
  {
    "inputs": [
      {"internalType": "address[]", "name": "recipients", "type": "address[]"},
      {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}
    ],
    "name": "batchSendRewards",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getContractBalance",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
] as const

export default function RewardDistribution() {
  const { address, isConnected } = useAppKitAccount()
  const [rewardData, setRewardData] = useState<RewardData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isDistributing, setIsDistributing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [minVotes, setMinVotes] = useState(1)
  const [limit, setLimit] = useState(10)

  const { writeContract, data: hash } = useWriteContract()
  const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({
    hash,
  })

  // Fetch top voters data
  const fetchTopVoters = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/rewards/top-voters?limit=${limit}&minVotes=${minVotes}`)
      const result = await response.json()
      
      if (result.success) {
        setRewardData(result.data)
      } else {
        setError(result.error || 'Failed to fetch top voters')
      }
    } catch (err) {
      setError('Network error while fetching data')
      console.error('Fetch error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle batch reward distribution
  const handleBatchDistribution = async () => {
    if (!rewardData || !isConnected) return

    try {
      setIsDistributing(true)
      setError(null)

      console.log('🎁 Starting batch reward distribution...')
      console.log('Recipients:', rewardData.batchData.recipients)
      console.log('Amounts:', rewardData.batchData.amounts)

      writeContract({
        address: REWARDS_CONTRACT as `0x${string}`,
        abi: REWARDS_ABI,
        functionName: 'batchSendRewards',
        args: [
          rewardData.batchData.recipients as `0x${string}`[],
          rewardData.batchData.amounts.map(amount => BigInt(amount))
        ],
      })
    } catch (err) {
      console.error('Batch distribution failed:', err)
      setError('Failed to distribute rewards')
      setIsDistributing(false)
    }
  }

  // Handle transaction confirmation
  useEffect(() => {
    if (isConfirmed) {
      console.log('✅ Batch rewards distributed successfully!')
      setIsDistributing(false)
      // Refresh data after successful distribution
      fetchTopVoters()
    }
  }, [isConfirmed])

  // Load data on component mount
  useEffect(() => {
    fetchTopVoters()
  }, [])

  if (!isConnected) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h2>🎁 Reward Distribution</h2>
          <p>Connect your wallet to manage rewards</p>
        </div>
        <div className={styles.connectPrompt}>
          <p>Please connect your wallet to access reward distribution</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>🎁 Reward Distribution</h2>
        <p>Distribute rewards to top voters</p>
      </div>

      {/* Controls */}
      <div className={styles.controls}>
        <div className={styles.inputGroup}>
          <label>Minimum Votes:</label>
          <input
            type="number"
            value={minVotes}
            onChange={(e) => setMinVotes(parseInt(e.target.value) || 1)}
            min="1"
            max="100"
          />
        </div>
        <div className={styles.inputGroup}>
          <label>Top Voters Limit:</label>
          <input
            type="number"
            value={limit}
            onChange={(e) => setLimit(parseInt(e.target.value) || 10)}
            min="1"
            max="100"
          />
        </div>
        <button 
          className={styles.refreshBtn}
          onClick={fetchTopVoters}
          disabled={isLoading}
        >
          {isLoading ? '🔄 Loading...' : '🔄 Refresh Data'}
        </button>
      </div>

      {error && (
        <div className={styles.error}>
          <p>❌ {error}</p>
        </div>
      )}

      {rewardData && (
        <>
          {/* Summary */}
          <div className={styles.summary}>
            <h3>📊 Distribution Summary</h3>
            <div className={styles.summaryGrid}>
              <div className={styles.summaryItem}>
                <span className={styles.label}>Total Recipients:</span>
                <span className={styles.value}>{rewardData.summary.totalVoters}</span>
              </div>
              <div className={styles.summaryItem}>
                <span className={styles.label}>Total Reward:</span>
                <span className={styles.value}>{rewardData.summary.totalSuggestedReward}</span>
              </div>
              <div className={styles.summaryItem}>
                <span className={styles.label}>Min Votes:</span>
                <span className={styles.value}>{rewardData.summary.minVotes}</span>
              </div>
            </div>
          </div>

          {/* Top Voters List */}
          <div className={styles.votersList}>
            <h3>🏆 Top Voters</h3>
            <div className={styles.votersTable}>
              <div className={styles.tableHeader}>
                <span>Rank</span>
                <span>Address</span>
                <span>Votes</span>
                <span>Avg Score</span>
                <span>Reward</span>
              </div>
              {rewardData.topVoters.map((voter) => (
                <div key={voter.address} className={styles.tableRow}>
                  <span className={styles.rank}>#{voter.rank}</span>
                  <span className={styles.address}>
                    {voter.address.slice(0, 6)}...{voter.address.slice(-4)}
                  </span>
                  <span className={styles.votes}>{voter.totalVotes}</span>
                  <span className={styles.score}>{voter.averageScore}</span>
                  <span className={styles.reward}>{voter.suggestedRewardMON} MON</span>
                </div>
              ))}
            </div>
          </div>

          {/* Distribution Button */}
          <div className={styles.distributionSection}>
            <button
              className={styles.distributeBtn}
              onClick={handleBatchDistribution}
              disabled={isDistributing || isConfirming}
            >
              {isDistributing || isConfirming ? (
                <span>
                  <span className={styles.spinner}></span>
                  {isConfirming ? 'Confirming...' : 'Distributing...'}
                </span>
              ) : (
                <span>
                  🎁 Distribute Rewards ({rewardData.summary.totalSuggestedReward})
                </span>
              )}
            </button>
          </div>
        </>
      )}
    </div>
  )
}
