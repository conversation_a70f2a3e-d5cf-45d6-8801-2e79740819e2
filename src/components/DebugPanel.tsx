'use client'

import { useAppKitAccount } from '@reown/appkit/react'
import { usePaymentStatus } from '@/hooks/usePaymentStatus'
import { useEthereumVerification } from '@/hooks/useEthereumVerification'
import styles from './DebugPanel.module.css'

export default function DebugPanel() {
  const { address, isConnected } = useAppKitAccount()
  const { hasPaid, isLoading, resetPaymentStatus, contractPaidStatus, isContractLoading } = usePaymentStatus()
  const { isEligible: isEthEligible, transactionCount, isLoading: ethLoading } = useEthereumVerification()

  if (!isConnected) return null

  return (
    <div className={styles.debugPanel}>
      <h4>🔧 Debug Panel</h4>
      <div className={styles.debugInfo}>
        <div className={styles.debugRow}>
          <span>Wallet:</span>
          <span>{address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}</span>
        </div>
        <div className={styles.debugRow}>
          <span>Connected:</span>
          <span className={isConnected ? styles.success : styles.error}>
            {isConnected ? '✅ Yes' : '❌ No'}
          </span>
        </div>
        <div className={styles.debugRow}>
          <span>Contract Status:</span>
          <span className={isContractLoading ? styles.loading : contractPaidStatus ? styles.success : styles.error}>
            {isContractLoading ? '⏳ Loading...' : contractPaidStatus ? '✅ Paid' : '❌ Not Paid'}
          </span>
        </div>
        <div className={styles.debugRow}>
          <span>Local Status:</span>
          <span className={hasPaid ? styles.success : styles.error}>
            {hasPaid ? '✅ Paid' : '❌ Not Paid'}
          </span>
        </div>
        <div className={styles.debugRow}>
          <span>Loading:</span>
          <span className={isLoading ? styles.loading : styles.success}>
            {isLoading ? '⏳ Yes' : '✅ No'}
          </span>
        </div>
        <div className={styles.debugRow}>
          <span>ETH Eligible:</span>
          <span className={ethLoading ? styles.loading : isEthEligible ? styles.success : styles.error}>
            {ethLoading ? '⏳ Checking...' : isEthEligible ? '✅ Yes' : '❌ No'}
          </span>
        </div>
        <div className={styles.debugRow}>
          <span>ETH Txs:</span>
          <span className={transactionCount >= 5 ? styles.success : styles.error}>
            {transactionCount}/5
          </span>
        </div>
      </div>
      
      <div className={styles.debugActions}>
        <button 
          className={styles.resetBtn}
          onClick={resetPaymentStatus}
        >
          🔄 Reset Local Status
        </button>
      </div>
      
      <div className={styles.contractInfo}>
        <p><strong>Payment Contract:</strong> 0x7De9...0a09b</p>
        <p><strong>ETH Verification:</strong> Mainnet API</p>
        <p><strong>Min Transactions:</strong> 5 on Ethereum</p>
      </div>
    </div>
  )
}
