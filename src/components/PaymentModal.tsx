'use client'

import { useState, useEffect } from 'react'
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react'
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import styles from './PaymentModal.module.css'

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  onPaymentSuccess: () => void
}

// Contract constants (defined outside component to avoid re-declaration)
const MONAD_TESTNET_ID = 10143
const PAYMENT_AMOUNT = '100000000000000000' // 0.1 MON in wei (updated from 0.25 MON)
const WALLET_CONNECT_CONTRACT = '******************************************'

// Simplified Contract ABI for WerantWalletConnect - only essential functions
const WALLET_CONNECT_ABI = [
  {
    "inputs": [],
    "name": "connectWallet",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "wallet", "type": "address"}],
    "name": "isWalletConnected",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
] as const

const PaymentModal: React.FC<PaymentModalProps> = ({ isOpen, onClose, onPaymentSuccess }) => {
  const { address, isConnected } = useAppKitAccount()
  const { chainId } = useAppKitNetwork()
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle')
  const [txHash, setTxHash] = useState<string>('')

  const { writeContract, data: hash, error } = useWriteContract()

  const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({
    hash,
  })

  // Note: Contract verification removed to avoid initialization issues
  // We'll verify the contract works when we make the actual payment

  useEffect(() => {
    if (isConfirmed && hash) {
      console.log('✅ Payment confirmed! Transaction hash:', hash)
      setPaymentStatus('success')
      setIsProcessing(false)
      setTxHash(hash)

      // Store payment status in localStorage
      if (address) {
        localStorage.setItem(`werant_paid_${address.toLowerCase()}`, 'true')
        console.log('💾 Payment status saved to localStorage')
      }

      // Call success callback after a short delay to show success message
      setTimeout(() => {
        console.log('🎉 Payment process complete, calling success callback')
        onPaymentSuccess()
        onClose()
      }, 3000) // Increased delay to show success message longer
    }
  }, [isConfirmed, hash, address, onPaymentSuccess, onClose])

  useEffect(() => {
    if (error) {
      console.error('❌ Transaction error:', error)
      console.error('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      })
      setPaymentStatus('error')
      setIsProcessing(false)
    }
  }, [error])

  useEffect(() => {
    if (hash && !isConfirmed && !isConfirming) {
      console.log('📡 Transaction submitted, hash:', hash)
      console.log('⏳ Waiting for confirmation...')
    }
  }, [hash, isConfirmed, isConfirming])

  // Log payment amount for debugging
  useEffect(() => {
    console.log('💰 PAYMENT_AMOUNT:', PAYMENT_AMOUNT, 'wei (0.1 MON)')
    console.log('📍 Contract Address:', WALLET_CONNECT_CONTRACT)
  }, [])

  const handlePayment = async () => {
    if (!isConnected || !address) {
      alert('Please connect your wallet first')
      return
    }

    // Check if user is on correct network
    if (chainId !== MONAD_TESTNET_ID) {
      alert('Please switch to Monad Testnet (Chain ID: 10143) to make payment')
      return
    }

    try {
      setIsProcessing(true)
      setPaymentStatus('pending')

      console.log('🔄 Starting payment process...')
      console.log('📍 Contract Address:', WALLET_CONNECT_CONTRACT)
      console.log('💰 Payment Amount:', PAYMENT_AMOUNT, 'wei (0.1 MON)')
      console.log('👤 User Address:', address)
      console.log('🌐 Network Chain ID:', chainId)

      // Call connectWallet function on WerantWalletConnect contract
      writeContract({
        address: WALLET_CONNECT_CONTRACT as `0x${string}`,
        abi: WALLET_CONNECT_ABI,
        functionName: 'connectWallet',
        value: BigInt(PAYMENT_AMOUNT), // 0.1 MON
        gas: BigInt(500000), // Set gas limit
      })

      console.log('✅ Transaction submitted')
    } catch (err) {
      console.error('❌ Payment failed:', err)
      console.error('Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        code: (err as any)?.code,
        reason: (err as any)?.reason,
        data: (err as any)?.data
      })
      setPaymentStatus('error')
      setIsProcessing(false)
    }
  }

  const handleSkip = () => {
    // For demo purposes, allow skipping payment
    if (address) {
      localStorage.setItem(`werant_paid_${address.toLowerCase()}`, 'true')
      console.log('⏭️ Payment skipped for demo purposes')
    }
    onPaymentSuccess()
    onClose()
  }

  const handleTestPayment = async () => {
    // Alternative payment method - direct transfer to contract
    if (!isConnected || !address) {
      alert('Please connect your wallet first')
      return
    }

    if (chainId !== MONAD_TESTNET_ID) {
      alert('Please switch to Monad Testnet (Chain ID: 10143) to make payment')
      return
    }

    try {
      setIsProcessing(true)
      setPaymentStatus('pending')

      console.log('🧪 Testing direct transfer payment...')

      // Use the same connectWallet function but with different gas limit
      writeContract({
        address: WALLET_CONNECT_CONTRACT as `0x${string}`,
        abi: WALLET_CONNECT_ABI,
        functionName: 'connectWallet',
        value: BigInt(PAYMENT_AMOUNT),
        gas: BigInt(300000), // Lower gas limit for test
      })

      console.log('✅ Test payment submitted')
    } catch (err) {
      console.error('❌ Test payment failed:', err)
      setPaymentStatus('error')
      setIsProcessing(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2>🎉 Welcome to Werant!</h2>
          <p>One-time activation fee for new users</p>
        </div>

        <div className={styles.content}>
          <div className={styles.paymentInfo}>
            <div className={styles.amountDisplay}>
              <span className={styles.amount}>0.1 MON</span>
              <span className={styles.currency}>Monad Testnet</span>
            </div>
            
            <div className={styles.benefits}>
              <h3>What you get:</h3>
              <ul>
                <li>✅ Full access to Werant voting platform</li>
                <li>✅ Participate in community governance</li>
                <li>✅ Create and vote on proposals</li>
                <li>✅ Access to leaderboard features</li>
             
              </ul>
            </div>

            {paymentStatus === 'idle' && (
              <div className={styles.actions}>
                <button 
                  className={`${styles.payBtn} ${styles.primary}`}
                  onClick={handlePayment}
                  disabled={!isConnected || isProcessing}
                >
                  <span className={styles.icon}>💳</span>
                  Pay 0.1 MON
                </button>
                
                <button
                  className={`${styles.payBtn} ${styles.secondary}`}
                  onClick={handleTestPayment}
                >
                  <span className={styles.icon}>🧪</span>
                  Test Payment
                </button>

                <button
                  className={`${styles.payBtn} ${styles.secondary}`}
                  onClick={handleSkip}
                  style={{ fontSize: '14px', padding: '12px 20px' }}
                >
                  <span className={styles.icon}>⏭️</span>
                  Skip for Demo
                </button>
              </div>
            )}

            {paymentStatus === 'pending' && (
              <div className={styles.statusDisplay}>
                <div className={styles.spinner}></div>
                <p>Processing payment...</p>
                {isConfirming && <p>Waiting for confirmation...</p>}
              </div>
            )}

            {paymentStatus === 'success' && (
              <div className={styles.statusDisplay}>
                <div className={styles.successIcon}>✅</div>
                <p>Payment successful!</p>
                <p>Welcome to Werant! 🎉</p>
              </div>
            )}

            {paymentStatus === 'error' && (
              <div className={styles.statusDisplay}>
                <div className={styles.errorIcon}>❌</div>
                <p>Payment failed</p>
                <p style={{ fontSize: '14px', color: '#ef4444', marginBottom: '16px' }}>
                  {error?.message || 'Transaction failed. Please try again.'}
                </p>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}>
                  <button
                    className={`${styles.payBtn} ${styles.primary}`}
                    onClick={() => {
                      setPaymentStatus('idle')
                      setIsProcessing(false)
                    }}
                  >
                    Try Again
                  </button>

                  <button
                    className={`${styles.payBtn} ${styles.secondary}`}
                    onClick={handleTestPayment}
                  >
                    🧪 Try Test Payment
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {paymentStatus === 'idle' && (
          <button 
            className={styles.closeBtn}
            onClick={onClose}
          >
            ×
          </button>
        )}
      </div>
    </div>
  )
}

export default PaymentModal
