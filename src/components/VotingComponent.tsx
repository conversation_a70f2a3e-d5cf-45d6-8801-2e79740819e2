'use client'

import { useState, useEffect } from 'react'
import { useAppKitAccount } from '@reown/appkit/react'
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import { usePaymentStatus } from '@/hooks/usePaymentStatus'
import styles from './VotingComponent.module.css'

interface VotingComponentProps {
  projectId?: number
  projectName?: string
}

const VotingComponent: React.FC<VotingComponentProps> = ({ 
  projectId = 1, 
  projectName = "Sample Project" 
}) => {
  const { address, isConnected } = useAppKitAccount()
  const { hasPaid } = usePaymentStatus()
  const [score, setScore] = useState<number>(5)
  const [comment, setComment] = useState<string>('')
  const [isVoting, setIsVoting] = useState(false)
  const [voteStatus, setVoteStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle')

  const { writeContract, data: hash, error } = useWriteContract()
  
  const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({
    hash,
  })

  // WerantLeaderboard contract address on Monad Testnet
  const LEADERBOARD_CONTRACT = '0xDC077E6D9bC6998fC61c23e5DfDd3d25e023ddB2'
  
  // Voting fee: 0.1 MON in wei (0.1 * 10^18)
  const VOTING_FEE = '100000000000000000' // 0.1 MON

  // Contract ABI for voting function
  const LEADERBOARD_ABI = [
    "function vote(uint256 projectId, uint256 score, string memory comment) external payable",
    "function getProjectVotes(uint256 projectId) external view returns (uint256)"
  ]

  useEffect(() => {
    if (isConfirmed && hash) {
      setVoteStatus('success')
      setIsVoting(false)
      
      // Reset form
      setTimeout(() => {
        setScore(5)
        setComment('')
        setVoteStatus('idle')
      }, 3000)
    }
  }, [isConfirmed, hash])

  useEffect(() => {
    if (error) {
      setVoteStatus('error')
      setIsVoting(false)
    }
  }, [error])

  const handleVote = async () => {
    if (!isConnected || !address) {
      alert('Please connect your wallet first')
      return
    }

    if (!hasPaid) {
      alert('Please complete the activation payment first')
      return
    }

    if (score < 1 || score > 10) {
      alert('Score must be between 1 and 10')
      return
    }

    if (comment.trim().length < 5) {
      alert('Comment must be at least 5 characters long')
      return
    }

    try {
      setIsVoting(true)
      setVoteStatus('pending')

      // Call vote function on WerantLeaderboard contract
      writeContract({
        address: LEADERBOARD_CONTRACT as `0x${string}`,
        abi: LEADERBOARD_ABI,
        functionName: 'vote',
        args: [BigInt(projectId), BigInt(score), comment],
        value: BigInt(VOTING_FEE), // 0.1 MON
      })
    } catch (err) {
      console.error('Voting failed:', err)
      setVoteStatus('error')
      setIsVoting(false)
    }
  }

  if (!isConnected) {
    return (
      <div className={styles.votingCard}>
        <div className={styles.header}>
          <h3>🗳️ Vote on {projectName}</h3>
          <p>Connect your wallet to vote</p>
        </div>
        <div className={styles.connectPrompt}>
          <p>Please connect your wallet to participate in voting</p>
        </div>
      </div>
    )
  }

  if (!hasPaid) {
    return (
      <div className={styles.votingCard}>
        <div className={styles.header}>
          <h3>🗳️ Vote on {projectName}</h3>
          <p>Complete activation to vote</p>
        </div>
        <div className={styles.paymentPrompt}>
          <p>Complete the 0.25 MON activation payment to start voting</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.votingCard}>
      <div className={styles.header}>
        <h3>🗳️ Vote on {projectName}</h3>
        <p>Cast your vote with 0.1 MON gas fee</p>
      </div>

      <div className={styles.votingForm}>
        <div className={styles.scoreSection}>
          <label htmlFor="score">Score (1-10):</label>
          <div className={styles.scoreInput}>
            <input
              type="range"
              id="score"
              min="1"
              max="10"
              value={score}
              onChange={(e) => setScore(parseInt(e.target.value))}
              className={styles.slider}
              disabled={isVoting}
            />
            <span className={styles.scoreValue}>{score}/10</span>
          </div>
        </div>

        <div className={styles.commentSection}>
          <label htmlFor="comment">Comment:</label>
          <textarea
            id="comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Share your thoughts about this project..."
            className={styles.commentInput}
            disabled={isVoting}
            maxLength={500}
          />
          <div className={styles.charCount}>
            {comment.length}/500 characters
          </div>
        </div>

        <div className={styles.feeInfo}>
          <div className={styles.feeDisplay}>
            <span className={styles.feeLabel}>Voting Fee:</span>
            <span className={styles.feeAmount}>0.1 MON</span>
          </div>
        </div>

        {voteStatus === 'idle' && (
          <button
            className={`${styles.voteBtn} ${styles.primary}`}
            onClick={handleVote}
            disabled={!isConnected || !hasPaid || isVoting || comment.trim().length < 5}
          >
            <span className={styles.icon}>🗳️</span>
            Vote Now (0.1 MON)
          </button>
        )}

        {voteStatus === 'pending' && (
          <div className={styles.statusDisplay}>
            <div className={styles.spinner}></div>
            <p>Processing vote...</p>
            {isConfirming && <p>Waiting for confirmation...</p>}
          </div>
        )}

        {voteStatus === 'success' && (
          <div className={styles.statusDisplay}>
            <div className={styles.successIcon}>✅</div>
            <p>Vote cast successfully!</p>
            <p>Thank you for participating! 🎉</p>
          </div>
        )}

        {voteStatus === 'error' && (
          <div className={styles.statusDisplay}>
            <div className={styles.errorIcon}>❌</div>
            <p>Vote failed</p>
            <p>{error?.message || 'Please try again'}</p>
            <button 
              className={`${styles.voteBtn} ${styles.primary}`}
              onClick={() => {
                setVoteStatus('idle')
                setIsVoting(false)
              }}
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default VotingComponent
