const express = require('express');
const { ethers } = require('ethers');
const WalletConnection = require('../models/WalletConnection');
const { 
  provider, 
  CONTRACT_ADDRESSES, 
  WALLET_CONNECT_ABI, 
  GAS_PRICES,
  getContractInstance,
  isValidAddress,
  formatMON
} = require('../config/blockchain');

const router = express.Router();

// Get wallet connection status
router.get('/status/:address', async (req, res) => {
  try {
    const { address } = req.params;
    
    if (!isValidAddress(address)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address'
      });
    }

    // Check database record
    const dbConnection = await WalletConnection.findOne({ 
      walletAddress: address.toLowerCase() 
    });

    // Check smart contract
    let contractStatus = null;
    if (CONTRACT_ADDRESSES.WALLET_CONNECT) {
      try {
        const contract = getContractInstance(
          CONTRACT_ADDRESSES.WALLET_CONNECT, 
          WALLET_CONNECT_ABI
        );
        
        const [isConnected, timestamp, connectionId] = await contract.getConnectionDetails(address);
        contractStatus = {
          isConnected,
          timestamp: timestamp.toNumber(),
          connectionId: connectionId.toNumber()
        };
      } catch (error) {
        console.error('Contract query error:', error);
      }
    }

    res.json({
      success: true,
      data: {
        address: address.toLowerCase(),
        database: dbConnection ? {
          isConnected: dbConnection.isConnected,
          connectionId: dbConnection.connectionId,
          connectionTimestamp: dbConnection.connectionTimestamp,
          feePaid: dbConnection.feePaid,
          transactionHash: dbConnection.transactionHash
        } : null,
        contract: contractStatus,
        connectionFee: formatMON(GAS_PRICES.WALLET_CONNECT) + ' MON'
      }
    });

  } catch (error) {
    console.error('Wallet status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get wallet status'
    });
  }
});

// Record wallet connection (called after successful blockchain transaction)
router.post('/connect', async (req, res) => {
  try {
    const { 
      walletAddress, 
      transactionHash, 
      connectionId, 
      feePaid, 
      blockNumber, 
      gasUsed 
    } = req.body;

    // Validate required fields
    if (!walletAddress || !transactionHash || !connectionId || !feePaid || !blockNumber) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    if (!isValidAddress(walletAddress)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address'
      });
    }

    // Verify transaction on blockchain
    try {
      const tx = await provider.getTransaction(transactionHash);
      const receipt = await provider.getTransactionReceipt(transactionHash);
      
      if (!tx || !receipt) {
        return res.status(400).json({
          success: false,
          error: 'Transaction not found or not confirmed'
        });
      }

      if (receipt.status !== 1) {
        return res.status(400).json({
          success: false,
          error: 'Transaction failed'
        });
      }

      // Verify transaction is to the correct contract
      if (tx.to.toLowerCase() !== CONTRACT_ADDRESSES.WALLET_CONNECT.toLowerCase()) {
        return res.status(400).json({
          success: false,
          error: 'Transaction not sent to wallet connect contract'
        });
      }

    } catch (error) {
      console.error('Transaction verification error:', error);
      return res.status(400).json({
        success: false,
        error: 'Failed to verify transaction'
      });
    }

    // Check if wallet is already connected
    const existingConnection = await WalletConnection.findOne({ 
      walletAddress: walletAddress.toLowerCase() 
    });

    if (existingConnection && existingConnection.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Wallet already connected'
      });
    }

    // Create or update connection record
    const connectionData = {
      walletAddress: walletAddress.toLowerCase(),
      connectionId,
      isConnected: true,
      connectionTimestamp: new Date(),
      transactionHash,
      feePaid: feePaid.toString(),
      blockNumber,
      gasUsed: gasUsed?.toString() || '0',
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip
    };

    let connection;
    if (existingConnection) {
      // Update existing record
      Object.assign(existingConnection, connectionData);
      connection = await existingConnection.save();
    } else {
      // Create new record
      connection = new WalletConnection(connectionData);
      await connection.save();
    }

    res.json({
      success: true,
      message: 'Wallet connection recorded successfully',
      data: {
        connectionId: connection.connectionId,
        walletAddress: connection.walletAddress,
        feePaid: formatMON(connection.feePaid) + ' MON',
        transactionHash: connection.transactionHash,
        timestamp: connection.connectionTimestamp
      }
    });

  } catch (error) {
    console.error('Wallet connect error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record wallet connection'
    });
  }
});

// Record wallet disconnection
router.post('/disconnect', async (req, res) => {
  try {
    const { walletAddress, transactionHash } = req.body;

    if (!isValidAddress(walletAddress)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address'
      });
    }

    const connection = await WalletConnection.findOne({ 
      walletAddress: walletAddress.toLowerCase() 
    });

    if (!connection) {
      return res.status(404).json({
        success: false,
        error: 'Wallet connection not found'
      });
    }

    if (!connection.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'Wallet already disconnected'
      });
    }

    // Update connection record
    await connection.disconnect();

    res.json({
      success: true,
      message: 'Wallet disconnected successfully',
      data: {
        walletAddress: connection.walletAddress,
        disconnectionTimestamp: connection.disconnectionTimestamp,
        connectionDuration: connection.connectionDuration
      }
    });

  } catch (error) {
    console.error('Wallet disconnect error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record wallet disconnection'
    });
  }
});

// Get connection statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await WalletConnection.getConnectionStats();
    const activeConnections = await WalletConnection.getActiveConnections();

    res.json({
      success: true,
      data: {
        overview: stats[0] || {
          totalConnections: 0,
          activeConnections: 0,
          totalFeesCollected: 0
        },
        recentConnections: activeConnections.slice(0, 10).map(conn => ({
          walletAddress: conn.walletAddress,
          connectionId: conn.connectionId,
          connectionTimestamp: conn.connectionTimestamp,
          feePaid: formatMON(conn.feePaid) + ' MON'
        })),
        connectionFee: formatMON(GAS_PRICES.WALLET_CONNECT) + ' MON'
      }
    });

  } catch (error) {
    console.error('Wallet stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get wallet statistics'
    });
  }
});

module.exports = router;
