const express = require('express');
const { ethers } = require('ethers');
const { 
  provider, 
  CONTRACT_ADDRESSES, 
  WALLET_CONNECT_ABI, 
  LEADERBOARD_ABI,
  GAS_PRICES,
  MONAD_TESTNET_CONFIG,
  getContractInstance,
  formatMON
} = require('../config/blockchain');

const router = express.Router();

// Get contract information and network details
router.get('/info', async (req, res) => {
  try {
    const networkInfo = {
      chainId: MONAD_TESTNET_CONFIG.chainId,
      name: MONAD_TESTNET_CONFIG.name,
      rpcUrl: MONAD_TESTNET_CONFIG.rpcUrl,
      explorerUrl: MONAD_TESTNET_CONFIG.explorerUrl,
      nativeCurrency: MONAD_TESTNET_CONFIG.nativeCurrency
    };

    const contractInfo = {
      walletConnect: {
        address: CONTRACT_ADDRESSES.WALLET_CONNECT,
        connectionFee: formatMON(GAS_PRICES.WALLET_CONNECT) + ' MON',
        explorerUrl: CONTRACT_ADDRESSES.WALLET_CONNECT ? 
          `${MONAD_TESTNET_CONFIG.explorerUrl}/address/${CONTRACT_ADDRESSES.WALLET_CONNECT}` : null
      },
      leaderboard: {
        address: CONTRACT_ADDRESSES.LEADERBOARD,
        votingFee: formatMON(GAS_PRICES.VOTING) + ' MON',
        explorerUrl: CONTRACT_ADDRESSES.LEADERBOARD ? 
          `${MONAD_TESTNET_CONFIG.explorerUrl}/address/${CONTRACT_ADDRESSES.LEADERBOARD}` : null
      }
    };

    // Get contract stats if contracts are deployed
    let contractStats = {};
    
    if (CONTRACT_ADDRESSES.WALLET_CONNECT) {
      try {
        const walletContract = getContractInstance(
          CONTRACT_ADDRESSES.WALLET_CONNECT, 
          WALLET_CONNECT_ABI
        );
        
        const [totalConnections, contractBalance] = await Promise.all([
          walletContract.getTotalConnections(),
          walletContract.getContractBalance()
        ]);

        contractStats.walletConnect = {
          totalConnections: totalConnections.toNumber(),
          contractBalance: formatMON(contractBalance) + ' MON'
        };
      } catch (error) {
        console.error('Wallet contract stats error:', error);
        contractStats.walletConnect = { error: 'Unable to fetch stats' };
      }
    }

    if (CONTRACT_ADDRESSES.LEADERBOARD) {
      try {
        const leaderboardContract = getContractInstance(
          CONTRACT_ADDRESSES.LEADERBOARD, 
          LEADERBOARD_ABI
        );
        
        const [totalProjects, totalVotes, contractBalance] = await Promise.all([
          leaderboardContract.getTotalProjects(),
          leaderboardContract.getTotalVotes(),
          leaderboardContract.getContractBalance()
        ]);

        contractStats.leaderboard = {
          totalProjects: totalProjects.toNumber(),
          totalVotes: totalVotes.toNumber(),
          contractBalance: formatMON(contractBalance) + ' MON'
        };
      } catch (error) {
        console.error('Leaderboard contract stats error:', error);
        contractStats.leaderboard = { error: 'Unable to fetch stats' };
      }
    }

    res.json({
      success: true,
      data: {
        network: networkInfo,
        contracts: contractInfo,
        stats: contractStats,
        gasLimits: {
          default: GAS_PRICES.DEFAULT_GAS_LIMIT,
          walletConnect: '200000',
          voting: '300000'
        },
        abis: {
          walletConnect: WALLET_CONNECT_ABI,
          leaderboard: LEADERBOARD_ABI
        }
      }
    });

  } catch (error) {
    console.error('Contract info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get contract information'
    });
  }
});

// Get current gas prices and network status
router.get('/network', async (req, res) => {
  try {
    const [blockNumber, gasPrice, network] = await Promise.all([
      provider.getBlockNumber(),
      provider.getGasPrice(),
      provider.getNetwork()
    ]);

    res.json({
      success: true,
      data: {
        network: {
          chainId: network.chainId,
          name: network.name || 'Monad Testnet'
        },
        currentBlock: blockNumber,
        gasPrice: {
          current: formatMON(gasPrice) + ' MON',
          wei: gasPrice.toString()
        },
        fees: {
          walletConnect: formatMON(GAS_PRICES.WALLET_CONNECT) + ' MON',
          voting: formatMON(GAS_PRICES.VOTING) + ' MON'
        },
        rpcUrl: MONAD_TESTNET_CONFIG.rpcUrl,
        explorerUrl: MONAD_TESTNET_CONFIG.explorerUrl
      }
    });

  } catch (error) {
    console.error('Network info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get network information'
    });
  }
});

// Validate transaction hash
router.get('/transaction/:hash', async (req, res) => {
  try {
    const { hash } = req.params;
    
    if (!hash || !hash.match(/^0x[a-fA-F0-9]{64}$/)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid transaction hash format'
      });
    }

    const [tx, receipt] = await Promise.all([
      provider.getTransaction(hash),
      provider.getTransactionReceipt(hash)
    ]);

    if (!tx) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    const response = {
      success: true,
      data: {
        hash: tx.hash,
        from: tx.from,
        to: tx.to,
        value: formatMON(tx.value) + ' MON',
        gasLimit: tx.gasLimit.toString(),
        gasPrice: formatMON(tx.gasPrice) + ' MON',
        blockNumber: tx.blockNumber,
        confirmations: tx.confirmations,
        status: receipt ? (receipt.status === 1 ? 'success' : 'failed') : 'pending',
        explorerUrl: `${MONAD_TESTNET_CONFIG.explorerUrl}/tx/${hash}`
      }
    };

    if (receipt) {
      response.data.gasUsed = receipt.gasUsed.toString();
      response.data.effectiveGasPrice = formatMON(receipt.effectiveGasPrice) + ' MON';
    }

    // Check if transaction is related to our contracts
    if (tx.to) {
      if (tx.to.toLowerCase() === CONTRACT_ADDRESSES.WALLET_CONNECT?.toLowerCase()) {
        response.data.contractType = 'walletConnect';
        response.data.contractName = 'WerantWalletConnect';
      } else if (tx.to.toLowerCase() === CONTRACT_ADDRESSES.LEADERBOARD?.toLowerCase()) {
        response.data.contractType = 'leaderboard';
        response.data.contractName = 'WerantLeaderboard';
      }
    }

    res.json(response);

  } catch (error) {
    console.error('Transaction info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get transaction information'
    });
  }
});

// Get contract events (recent activity)
router.get('/events', async (req, res) => {
  try {
    const { contract = 'all', limit = 10 } = req.query;
    const limitNum = Math.min(parseInt(limit), 100);
    
    const events = [];
    const fromBlock = Math.max(0, await provider.getBlockNumber() - 1000); // Last 1000 blocks

    // Get wallet connect events
    if ((contract === 'all' || contract === 'wallet') && CONTRACT_ADDRESSES.WALLET_CONNECT) {
      try {
        const walletContract = getContractInstance(
          CONTRACT_ADDRESSES.WALLET_CONNECT, 
          WALLET_CONNECT_ABI
        );
        
        const connectEvents = await walletContract.queryFilter(
          walletContract.filters.WalletConnected(),
          fromBlock
        );
        
        const disconnectEvents = await walletContract.queryFilter(
          walletContract.filters.WalletDisconnected(),
          fromBlock
        );

        events.push(
          ...connectEvents.map(event => ({
            type: 'WalletConnected',
            contract: 'walletConnect',
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
            args: {
              wallet: event.args.wallet,
              connectionId: event.args.connectionId.toNumber(),
              timestamp: event.args.timestamp.toNumber(),
              feePaid: formatMON(event.args.feePaid) + ' MON'
            }
          })),
          ...disconnectEvents.map(event => ({
            type: 'WalletDisconnected',
            contract: 'walletConnect',
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
            args: {
              wallet: event.args.wallet,
              timestamp: event.args.timestamp.toNumber()
            }
          }))
        );
      } catch (error) {
        console.error('Wallet events error:', error);
      }
    }

    // Get leaderboard events
    if ((contract === 'all' || contract === 'leaderboard') && CONTRACT_ADDRESSES.LEADERBOARD) {
      try {
        const leaderboardContract = getContractInstance(
          CONTRACT_ADDRESSES.LEADERBOARD, 
          LEADERBOARD_ABI
        );
        
        const projectEvents = await leaderboardContract.queryFilter(
          leaderboardContract.filters.ProjectCreated(),
          fromBlock
        );
        
        const voteEvents = await leaderboardContract.queryFilter(
          leaderboardContract.filters.VoteCast(),
          fromBlock
        );

        events.push(
          ...projectEvents.map(event => ({
            type: 'ProjectCreated',
            contract: 'leaderboard',
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
            args: {
              projectId: event.args.projectId.toNumber(),
              name: event.args.name,
              creator: event.args.creator,
              timestamp: event.args.timestamp.toNumber()
            }
          })),
          ...voteEvents.map(event => ({
            type: 'VoteCast',
            contract: 'leaderboard',
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
            args: {
              voteId: event.args.voteId.toNumber(),
              projectId: event.args.projectId.toNumber(),
              voter: event.args.voter,
              score: event.args.score.toNumber(),
              comment: event.args.comment,
              feePaid: formatMON(event.args.feePaid) + ' MON',
              timestamp: event.args.timestamp.toNumber()
            }
          }))
        );
      } catch (error) {
        console.error('Leaderboard events error:', error);
      }
    }

    // Sort by block number (most recent first) and limit
    events.sort((a, b) => b.blockNumber - a.blockNumber);
    const limitedEvents = events.slice(0, limitNum);

    res.json({
      success: true,
      data: {
        events: limitedEvents,
        totalEvents: events.length,
        fromBlock,
        currentBlock: await provider.getBlockNumber()
      }
    });

  } catch (error) {
    console.error('Contract events error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get contract events'
    });
  }
});

module.exports = router;
