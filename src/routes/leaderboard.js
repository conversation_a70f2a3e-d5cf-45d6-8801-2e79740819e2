const express = require('express');
const { ethers } = require('ethers');
const Project = require('../models/Project');
const Vote = require('../models/Vote');
const WalletConnection = require('../models/WalletConnection');
const { 
  provider, 
  CONTRACT_ADDRESSES, 
  LEADERBOARD_ABI, 
  GAS_PRICES,
  getContractInstance,
  isValidAddress,
  formatMON
} = require('../config/blockchain');

const router = express.Router();

// Middleware to check if wallet is connected
const requireWalletConnection = async (req, res, next) => {
  try {
    const { walletAddress } = req.body;
    
    if (!walletAddress || !isValidAddress(walletAddress)) {
      return res.status(400).json({
        success: false,
        error: 'Valid wallet address required'
      });
    }

    const connection = await WalletConnection.findOne({ 
      walletAddress: walletAddress.toLowerCase(),
      isConnected: true
    });

    if (!connection) {
      return res.status(403).json({
        success: false,
        error: 'Wallet must be connected first. Please connect your wallet with 0.25 MON fee.'
      });
    }

    req.walletConnection = connection;
    next();
  } catch (error) {
    console.error('Wallet connection check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify wallet connection'
    });
  }
};

// Get leaderboard
router.get('/', async (req, res) => {
  try {
    const { limit = 10, sortBy = 'votes' } = req.query;
    const limitNum = Math.min(parseInt(limit), 100); // Max 100 projects

    let projects;
    if (sortBy === 'rating') {
      projects = await Project.getTopRated(limitNum);
    } else {
      projects = await Project.getLeaderboard(limitNum);
    }

    // Get contract data if available
    let contractLeaderboard = null;
    if (CONTRACT_ADDRESSES.LEADERBOARD) {
      try {
        const contract = getContractInstance(
          CONTRACT_ADDRESSES.LEADERBOARD, 
          LEADERBOARD_ABI
        );
        
        const [projectIds, averageScores] = await contract.getLeaderboard(limitNum);
        contractLeaderboard = projectIds.map((id, index) => ({
          projectId: id.toNumber(),
          averageScore: averageScores[index].toNumber() / 100 // Convert from contract format
        }));
      } catch (error) {
        console.error('Contract leaderboard error:', error);
      }
    }

    res.json({
      success: true,
      data: {
        projects: projects.map(project => ({
          projectId: project.projectId,
          name: project.name,
          description: project.description,
          creator: project.creator,
          totalVotes: project.totalVotes,
          averageScore: project.averageScore || project.averageScore,
          averageScorePercentage: project.averageScorePercentage,
          category: project.category,
          isActive: project.isActive,
          createdAt: project.createdAt
        })),
        contractData: contractLeaderboard,
        sortBy,
        limit: limitNum,
        votingFee: formatMON(GAS_PRICES.VOTING) + ' MON'
      }
    });

  } catch (error) {
    console.error('Leaderboard error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get leaderboard'
    });
  }
});

// Create new project
router.post('/project', requireWalletConnection, async (req, res) => {
  try {
    const { 
      walletAddress, 
      name, 
      description, 
      transactionHash, 
      projectId, 
      blockNumber,
      category,
      website,
      github,
      twitter,
      discord
    } = req.body;

    // Validate required fields
    if (!name || !description || !transactionHash || !projectId || !blockNumber) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Verify transaction on blockchain
    try {
      const tx = await provider.getTransaction(transactionHash);
      const receipt = await provider.getTransactionReceipt(transactionHash);
      
      if (!tx || !receipt || receipt.status !== 1) {
        return res.status(400).json({
          success: false,
          error: 'Transaction not found or failed'
        });
      }

      if (tx.to.toLowerCase() !== CONTRACT_ADDRESSES.LEADERBOARD.toLowerCase()) {
        return res.status(400).json({
          success: false,
          error: 'Transaction not sent to leaderboard contract'
        });
      }
    } catch (error) {
      console.error('Transaction verification error:', error);
      return res.status(400).json({
        success: false,
        error: 'Failed to verify transaction'
      });
    }

    // Check if project already exists
    const existingProject = await Project.findOne({ projectId });
    if (existingProject) {
      return res.status(400).json({
        success: false,
        error: 'Project already exists'
      });
    }

    // Create project record
    const project = new Project({
      projectId,
      name: name.trim(),
      description: description.trim(),
      creator: walletAddress.toLowerCase(),
      transactionHash,
      blockNumber,
      category: category || 'Other',
      website,
      github,
      twitter,
      discord
    });

    await project.save();

    res.json({
      success: true,
      message: 'Project created successfully',
      data: {
        projectId: project.projectId,
        name: project.name,
        description: project.description,
        creator: project.creator,
        category: project.category,
        transactionHash: project.transactionHash,
        createdAt: project.createdAt
      }
    });

  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create project'
    });
  }
});

// Vote for project
router.post('/vote', requireWalletConnection, async (req, res) => {
  try {
    const { 
      walletAddress, 
      projectId, 
      score, 
      comment, 
      transactionHash, 
      voteId, 
      blockNumber, 
      feePaid, 
      gasUsed 
    } = req.body;

    // Validate required fields
    if (!projectId || !score || !transactionHash || !voteId || !blockNumber || !feePaid) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Validate score
    if (score < 1 || score > 5) {
      return res.status(400).json({
        success: false,
        error: 'Score must be between 1 and 5'
      });
    }

    // Check if project exists
    const project = await Project.findOne({ projectId });
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    if (!project.isActive) {
      return res.status(400).json({
        success: false,
        error: 'Project is not active'
      });
    }

    // Check if user has already voted
    const existingVote = await Vote.hasVoted(walletAddress.toLowerCase(), projectId);
    if (existingVote) {
      return res.status(400).json({
        success: false,
        error: 'You have already voted for this project'
      });
    }

    // Verify transaction on blockchain
    try {
      const tx = await provider.getTransaction(transactionHash);
      const receipt = await provider.getTransactionReceipt(transactionHash);
      
      if (!tx || !receipt || receipt.status !== 1) {
        return res.status(400).json({
          success: false,
          error: 'Transaction not found or failed'
        });
      }

      if (tx.to.toLowerCase() !== CONTRACT_ADDRESSES.LEADERBOARD.toLowerCase()) {
        return res.status(400).json({
          success: false,
          error: 'Transaction not sent to leaderboard contract'
        });
      }
    } catch (error) {
      console.error('Transaction verification error:', error);
      return res.status(400).json({
        success: false,
        error: 'Failed to verify transaction'
      });
    }

    // Create vote record
    const vote = new Vote({
      voteId,
      projectId,
      voter: walletAddress.toLowerCase(),
      score,
      comment: comment?.trim() || '',
      transactionHash,
      blockNumber,
      feePaid: feePaid.toString(),
      gasUsed: gasUsed?.toString() || '0',
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip
    });

    await vote.save();

    // Update project stats
    await project.addVote(score);

    res.json({
      success: true,
      message: 'Vote submitted successfully',
      data: {
        voteId: vote.voteId,
        projectId: vote.projectId,
        score: vote.score,
        comment: vote.comment,
        feePaid: formatMON(vote.feePaid) + ' MON',
        transactionHash: vote.transactionHash,
        timestamp: vote.timestamp,
        projectStats: {
          totalVotes: project.totalVotes,
          averageScore: project.averageScore
        }
      }
    });

  } catch (error) {
    console.error('Vote error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit vote'
    });
  }
});

// Get project details
router.get('/project/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    
    const project = await Project.findOne({ projectId: parseInt(projectId) });
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    // Get recent votes
    const recentVotes = await Vote.getVotesByProject(parseInt(projectId));

    res.json({
      success: true,
      data: {
        project: {
          projectId: project.projectId,
          name: project.name,
          description: project.description,
          creator: project.creator,
          totalVotes: project.totalVotes,
          averageScore: project.averageScore,
          averageScorePercentage: project.averageScorePercentage,
          category: project.category,
          website: project.website,
          github: project.github,
          twitter: project.twitter,
          discord: project.discord,
          isActive: project.isActive,
          createdAt: project.createdAt
        },
        recentVotes: recentVotes.slice(0, 10).map(vote => ({
          voteId: vote.voteId,
          voter: vote.voter,
          score: vote.score,
          comment: vote.comment,
          timestamp: vote.timestamp
        })),
        votingFee: formatMON(GAS_PRICES.VOTING) + ' MON'
      }
    });

  } catch (error) {
    console.error('Project details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get project details'
    });
  }
});

// Get voting statistics
router.get('/stats', async (req, res) => {
  try {
    const [voteStats, projectStats] = await Promise.all([
      Vote.getVoteStats(),
      Project.getProjectStats()
    ]);

    const recentVotes = await Vote.getRecentVotes(10);
    const topVoters = await Vote.getTopVoters(10);
    const projectsByCategory = await Project.getProjectsByCategory();

    res.json({
      success: true,
      data: {
        votes: voteStats[0] || {
          totalVotes: 0,
          averageScore: 0,
          totalFeesCollected: 0
        },
        projects: projectStats[0] || {
          totalProjects: 0,
          activeProjects: 0,
          totalVotes: 0,
          averageVotesPerProject: 0
        },
        recentVotes: recentVotes.map(vote => ({
          voteId: vote.voteId,
          projectId: vote.projectId,
          voter: vote.voter,
          score: vote.score,
          timestamp: vote.timestamp
        })),
        topVoters: topVoters.map(voter => ({
          address: voter._id,
          totalVotes: voter.totalVotes,
          averageScore: voter.averageScore?.toFixed(2),
          totalFeesContributed: formatMON(voter.totalFeesContributed.toString()) + ' MON'
        })),
        projectsByCategory,
        fees: {
          votingFee: formatMON(GAS_PRICES.VOTING) + ' MON',
          connectionFee: formatMON(GAS_PRICES.WALLET_CONNECT) + ' MON'
        }
      }
    });

  } catch (error) {
    console.error('Leaderboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get statistics'
    });
  }
});

module.exports = router;
