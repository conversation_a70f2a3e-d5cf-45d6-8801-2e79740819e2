const express = require('express');
const { ethers } = require('ethers');
const Vote = require('../models/Vote');
const { 
  provider, 
  CONTRACT_ADDRESSES, 
  GAS_PRICES,
  formatMON
} = require('../config/blockchain');

const router = express.Router();

// WerantRewards contract ABI (simplified)
const REWARDS_ABI = [
  "function batchSendRewards(address[] calldata recipients, uint256[] calldata amounts) external",
  "function sendSingleReward(address recipient, uint256 amount) external",
  "function getContractBalance() external view returns (uint256)",
  "function getRewardStats(address recipient) external view returns (uint256 totalReceived, uint256 rewardTimes)",
  "function getOverallStats() external view returns (uint256 totalDistributed, uint256 totalRecipients, uint256 contractBalance)",
  "function depositFunds() external payable",
  "event BatchRewardSent(address[] recipients, uint256[] amounts, uint256 totalAmount, uint256 timestamp)"
];

// Get top voters for reward distribution
router.get('/top-voters', async (req, res) => {
  try {
    const { limit = 10, minVotes = 1 } = req.query;
    const limitNum = Math.min(parseInt(limit), 100); // Max 100 for batch operations
    const minVotesNum = parseInt(minVotes);

    // Get top voters from database
    const topVoters = await Vote.aggregate([
      {
        $group: {
          _id: '$voter',
          totalVotes: { $sum: 1 },
          averageScore: { $avg: '$score' },
          totalFeesContributed: { $sum: { $toDouble: '$feePaid' } },
          lastVoteDate: { $max: '$timestamp' }
        }
      },
      {
        $match: {
          totalVotes: { $gte: minVotesNum }
        }
      },
      { $sort: { totalVotes: -1, averageScore: -1 } },
      { $limit: limitNum }
    ]);

    // Calculate suggested reward amounts based on contribution
    const topVotersWithRewards = topVoters.map((voter, index) => {
      // Reward calculation: Base reward + bonus for ranking + bonus for average score
      const baseReward = 0.01; // 0.01 MON base
      const rankingBonus = (limitNum - index) * 0.005; // Higher rank = more bonus
      const scoreBonus = (voter.averageScore - 3) * 0.01; // Above average score bonus
      
      const suggestedReward = Math.max(baseReward + rankingBonus + scoreBonus, 0.005); // Min 0.005 MON
      const rewardWei = ethers.utils.parseEther(suggestedReward.toFixed(4)).toString();

      return {
        address: voter._id,
        totalVotes: voter.totalVotes,
        averageScore: voter.averageScore?.toFixed(2),
        totalFeesContributed: formatMON(voter.totalFeesContributed.toString()) + ' MON',
        lastVoteDate: voter.lastVoteDate,
        suggestedRewardMON: suggestedReward.toFixed(4),
        suggestedRewardWei: rewardWei,
        rank: index + 1
      };
    });

    const totalSuggestedReward = topVotersWithRewards.reduce(
      (sum, voter) => sum + parseFloat(voter.suggestedRewardMON), 
      0
    );

    res.json({
      success: true,
      data: {
        topVoters: topVotersWithRewards,
        summary: {
          totalVoters: topVotersWithRewards.length,
          totalSuggestedReward: totalSuggestedReward.toFixed(4) + ' MON',
          totalSuggestedRewardWei: ethers.utils.parseEther(totalSuggestedReward.toFixed(4)).toString(),
          minVotes: minVotesNum,
          limit: limitNum
        },
        batchData: {
          recipients: topVotersWithRewards.map(v => v.address),
          amounts: topVotersWithRewards.map(v => v.suggestedRewardWei)
        }
      }
    });

  } catch (error) {
    console.error('Top voters error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get top voters'
    });
  }
});

// Get reward contract information
router.get('/contract-info', async (req, res) => {
  try {
    if (!CONTRACT_ADDRESSES.REWARDS) {
      return res.json({
        success: false,
        error: 'Rewards contract not deployed'
      });
    }

    const contract = new ethers.Contract(
      CONTRACT_ADDRESSES.REWARDS,
      REWARDS_ABI,
      provider
    );

    const [totalDistributed, totalRecipients, contractBalance] = await contract.getOverallStats();

    res.json({
      success: true,
      data: {
        contractAddress: CONTRACT_ADDRESSES.REWARDS,
        contractBalance: formatMON(contractBalance.toString()) + ' MON',
        contractBalanceWei: contractBalance.toString(),
        totalRewardsDistributed: formatMON(totalDistributed.toString()) + ' MON',
        totalRewardRecipients: totalRecipients.toString(),
        abi: REWARDS_ABI
      }
    });

  } catch (error) {
    console.error('Contract info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get contract information'
    });
  }
});

// Get reward statistics for a specific address
router.get('/stats/:address', async (req, res) => {
  try {
    const { address } = req.params;
    
    if (!ethers.utils.isAddress(address)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid address format'
      });
    }

    if (!CONTRACT_ADDRESSES.REWARDS) {
      return res.json({
        success: false,
        error: 'Rewards contract not deployed'
      });
    }

    const contract = new ethers.Contract(
      CONTRACT_ADDRESSES.REWARDS,
      REWARDS_ABI,
      provider
    );

    const [totalReceived, rewardTimes] = await contract.getRewardStats(address);

    // Get voting stats from database
    const votingStats = await Vote.aggregate([
      {
        $match: { voter: address.toLowerCase() }
      },
      {
        $group: {
          _id: null,
          totalVotes: { $sum: 1 },
          averageScore: { $avg: '$score' },
          totalFeesContributed: { $sum: { $toDouble: '$feePaid' } }
        }
      }
    ]);

    const stats = votingStats[0] || {
      totalVotes: 0,
      averageScore: 0,
      totalFeesContributed: 0
    };

    res.json({
      success: true,
      data: {
        address,
        rewards: {
          totalReceived: formatMON(totalReceived.toString()) + ' MON',
          totalReceivedWei: totalReceived.toString(),
          rewardTimes: rewardTimes.toString()
        },
        voting: {
          totalVotes: stats.totalVotes,
          averageScore: stats.averageScore?.toFixed(2),
          totalFeesContributed: formatMON(stats.totalFeesContributed.toString()) + ' MON'
        }
      }
    });

  } catch (error) {
    console.error('Reward stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get reward statistics'
    });
  }
});

module.exports = router;
