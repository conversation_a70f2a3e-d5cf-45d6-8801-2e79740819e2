<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - WalletConnect v2 Integration</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <script src="https://unpkg.com/@walletconnect/modal@2.7.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@walletconnect/universal-provider@2.11.2/dist/index.umd.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 3rem;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Gradient Button Styles */
        .gradient-btn {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 8px;
            padding: 18px 36px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.2s ease;
            transform: scale(1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
            border: none;
            cursor: pointer;
            font-size: 18px;
            background: linear-gradient(to right, #9333ea 20%, #ec4899 80%);
            color: white;
            width: 100%;
        }
        
        .gradient-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
            background: linear-gradient(to right, #7c3aed, #db2777);
        }
        
        .gradient-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: scale(1);
        }
        
        .wallet-widget {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 24px;
            backdrop-filter: blur(10px);
            color: #1a1b23;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .copy-btn, .disconnect-btn {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 6px;
            border-radius: 6px;
            transition: color 0.2s ease;
            font-size: 14px;
        }
        
        .copy-btn:hover {
            color: #9333ea;
        }
        
        .disconnect-btn:hover {
            color: #ef4444;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid rgba(76, 175, 80, 0.5); }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid rgba(244, 67, 54, 0.5); }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid rgba(33, 150, 243, 0.5); }
        .warning { background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.5); }
        
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fee-info h3 {
            color: #ff6b6b;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Werant</h1>
            <p>WalletConnect v2 - 600+ Wallets Supported</p>
        </div>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <!-- Wallet Connection Section -->
        <div id="walletSection" style="text-align: center; margin: 40px 0;">
            <!-- Connect Button (shown when not connected) -->
            <div id="connectSection">
                <button id="connectBtn" class="gradient-btn">
                    <i class="fas fa-wallet"></i>
                    Connect Wallet
                </button>
            </div>
            
            <!-- Wallet Widget (shown when connected) -->
            <div id="walletWidget" class="wallet-widget" style="display: none;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span class="status-indicator status-connected"></span>
                        <span style="font-weight: 600;">Connected</span>
                    </div>
                    <button id="disconnectBtn" class="disconnect-btn" title="Disconnect">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                
                <div>
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Address:</span>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span id="walletAddress" style="font-family: monospace; font-size: 14px;"></span>
                            <button id="copyBtn" class="copy-btn" title="Copy address">
                                <i id="copyIcon" class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Balance:</span>
                        <span id="walletBalance" style="font-size: 14px;">Loading...</span>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="font-size: 14px; color: #6b7280;">Network:</span>
                        <span id="networkName" style="font-size: 14px;">Monad Testnet</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" class="status info">
            Initializing WalletConnect v2...
        </div>

        <!-- Debug Section -->
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="debugWalletConnect()" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 12px;
                margin-right: 10px;
            ">Debug WalletConnect</button>

            <button onclick="testModalOpen()" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 12px;
            ">Test Modal Open</button>
        </div>
    </div>

    <script>
        // Your Reown Project ID
        const projectId = '3203e3196ca7682fb5394a53b725d357';
        
        // Global variables
        let walletConnectModal = null;
        let signClient = null;
        let userAddress = null;
        let isConnected = false;
        let provider = null;
        let currentSession = null;

        // Utility function to shorten addresses
        function shortenAddr(addr) {
            if (!addr) return '';
            return addr.slice(0, 5) + "..." + addr.slice(-5);
        }

        // Initialize WalletConnect v2
        async function initializeWalletConnect() {
            try {
                updateStatus('Initializing WalletConnect v2...', 'info');

                // Check if libraries are loaded
                if (typeof WalletConnectModal === 'undefined') {
                    throw new Error('WalletConnect Modal library not loaded');
                }

                // Initialize WalletConnect Modal
                walletConnectModal = new WalletConnectModal.WalletConnectModal({
                    projectId: projectId,
                    chains: [10143], // Monad Testnet
                    themeMode: 'light',
                    themeVariables: {
                        '--w3m-accent': '#9333ea',
                        '--w3m-border-radius-master': '12px'
                    }
                });

                // Subscribe to modal events
                walletConnectModal.subscribeModal((state) => {
                    console.log('Modal state changed:', state);
                });

                // Subscribe to account changes
                walletConnectModal.subscribeAccount((account) => {
                    console.log('Account changed:', account);
                    if (account.isConnected && account.address) {
                        userAddress = account.address;
                        isConnected = true;
                        updateWalletUI();
                        updateStatus('Wallet connected successfully! 🎉', 'success');
                    } else {
                        userAddress = null;
                        isConnected = false;
                        updateWalletUI();
                        updateStatus('Wallet disconnected', 'info');
                    }
                });

                // Subscribe to chain changes
                walletConnectModal.subscribeChainId((chainId) => {
                    console.log('Chain changed:', chainId);
                    if (isConnected) {
                        loadWalletBalance();
                    }
                });

                console.log('WalletConnect Modal initialized');
                updateStatus('Ready! Click "Connect Wallet" to see 600+ wallet options.', 'success');

            } catch (error) {
                console.error('Failed to initialize WalletConnect:', error);
                updateStatus('Failed to initialize WalletConnect: ' + error.message, 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            setupEventListeners();

            // Wait for libraries to load with multiple attempts
            let attempts = 0;
            const maxAttempts = 10;

            const tryInitialize = async () => {
                attempts++;
                console.log(`Initialization attempt ${attempts}/${maxAttempts}`);

                if (typeof WalletConnectModal !== 'undefined') {
                    await initializeWalletConnect();
                } else if (attempts < maxAttempts) {
                    updateStatus(`Loading WalletConnect libraries... (${attempts}/${maxAttempts})`, 'info');
                    setTimeout(tryInitialize, 1000);
                } else {
                    updateStatus('Failed to load WalletConnect libraries. Please refresh the page.', 'error');
                    console.error('WalletConnect libraries failed to load after', maxAttempts, 'attempts');
                }
            };

            setTimeout(tryInitialize, 500);
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('connectBtn').addEventListener('click', connectWallet);
            document.getElementById('disconnectBtn').addEventListener('click', disconnectWallet);
            document.getElementById('copyBtn').addEventListener('click', copyAddress);
        }

        // Connect wallet
        async function connectWallet() {
            try {
                if (!walletConnectModal) {
                    updateStatus('WalletConnect not initialized. Please refresh the page.', 'error');
                    return;
                }

                updateStatus('Opening wallet selection with 600+ wallets...', 'info');

                // Open WalletConnect modal - this should show 600+ wallets
                await walletConnectModal.open();

            } catch (error) {
                console.error('Connection error:', error);
                updateStatus('Connection failed: ' + error.message, 'error');
            }
        }

        // Disconnect wallet
        async function disconnectWallet() {
            try {
                if (walletConnectModal) {
                    await walletConnectModal.disconnect();
                }
                
                userAddress = null;
                isConnected = false;
                provider = null;
                currentSession = null;
                
                updateWalletUI();
                updateStatus('Wallet disconnected', 'info');
            } catch (error) {
                console.error('Disconnect error:', error);
                updateStatus('Disconnect failed: ' + error.message, 'error');
            }
        }

        // Update wallet UI
        function updateWalletUI() {
            const connectSection = document.getElementById('connectSection');
            const walletWidget = document.getElementById('walletWidget');

            if (isConnected && userAddress) {
                connectSection.style.display = 'none';
                walletWidget.style.display = 'block';
                
                document.getElementById('walletAddress').textContent = shortenAddr(userAddress);
                loadWalletBalance();
            } else {
                connectSection.style.display = 'block';
                walletWidget.style.display = 'none';
            }
        }

        // Load wallet balance
        async function loadWalletBalance() {
            try {
                if (userAddress) {
                    const provider = new ethers.providers.JsonRpcProvider('https://testnet-rpc.monad.xyz');
                    const balance = await provider.getBalance(userAddress);
                    const balanceInMON = ethers.utils.formatEther(balance);
                    document.getElementById('walletBalance').textContent = `${parseFloat(balanceInMON).toFixed(4)} MON`;
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('walletBalance').textContent = 'Error loading balance';
            }
        }

        // Copy address to clipboard
        async function copyAddress() {
            if (userAddress) {
                try {
                    await navigator.clipboard.writeText(userAddress);
                    const copyIcon = document.getElementById('copyIcon');
                    copyIcon.className = 'fas fa-check-circle';
                    
                    setTimeout(() => {
                        copyIcon.className = 'fas fa-copy';
                    }, 2000);
                    
                    updateStatus('Address copied to clipboard! 📋', 'success');
                } catch (error) {
                    console.error('Failed to copy address:', error);
                    updateStatus('Failed to copy address', 'error');
                }
            }
        }

        // Update status message
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Debug functions
        window.debugWalletConnect = () => {
            console.log('=== WALLETCONNECT DEBUG INFO ===');
            console.log('WalletConnect Modal:', walletConnectModal);
            console.log('Connected:', isConnected);
            console.log('Address:', userAddress);
            console.log('Project ID:', projectId);
            console.log('WalletConnectModal available:', typeof WalletConnectModal !== 'undefined');
            console.log('Modal instance:', walletConnectModal);

            if (walletConnectModal) {
                console.log('Modal methods:', Object.getOwnPropertyNames(walletConnectModal));
            }

            updateStatus(`Debug: Modal=${!!walletConnectModal}, Connected=${isConnected}, Address=${userAddress ? 'Yes' : 'No'}`, 'info');
        };

        window.testModalOpen = async () => {
            try {
                if (walletConnectModal) {
                    console.log('Testing modal open...');
                    updateStatus('Testing modal open...', 'info');
                    await walletConnectModal.open();
                } else {
                    updateStatus('Modal not initialized', 'error');
                }
            } catch (error) {
                console.error('Test modal error:', error);
                updateStatus('Test modal failed: ' + error.message, 'error');
            }
        };

        // Make modal available globally
        window.walletConnectModal = walletConnectModal;
    </script>
</body>
</html>
