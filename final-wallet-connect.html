<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - Final Wallet Connect</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 3rem;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .gradient-btn {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 8px;
            padding: 18px 36px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.2s ease;
            transform: scale(1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
            border: none;
            cursor: pointer;
            font-size: 18px;
            background: linear-gradient(to right, #9333ea 20%, #ec4899 80%);
            color: white;
            width: 100%;
        }
        
        .gradient-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
            background: linear-gradient(to right, #7c3aed, #db2777);
        }
        
        .wallet-widget {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 24px;
            backdrop-filter: blur(10px);
            color: #1a1b23;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid rgba(76, 175, 80, 0.5); }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid rgba(244, 67, 54, 0.5); }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid rgba(33, 150, 243, 0.5); }
        .warning { background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.5); }
        
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fee-info h3 {
            color: #ff6b6b;
            margin-top: 0;
        }

        .wallet-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        }
        
        .modal-content {
            background: white;
            border-radius: 24px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            color: #333;
        }
        
        .wallet-option {
            width: 100%;
            padding: 16px;
            margin: 8px 0;
            border: 2px solid #f3f4f6;
            background: white;
            border-radius: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 16px;
            transition: all 0.2s ease;
        }
        
        .wallet-option:hover {
            border-color: #9333ea;
            background: #f8faff;
            transform: translateY(-1px);
        }
        
        .wallet-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Werant</h1>
            <p>Simple & Reliable Wallet Connection</p>
        </div>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <div id="connectSection">
                <button id="connectBtn" class="gradient-btn">
                    <i class="fas fa-wallet"></i>
                    Connect Wallet
                </button>
            </div>
            
            <div id="walletWidget" class="wallet-widget" style="display: none;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="width: 12px; height: 12px; background: #10b981; border-radius: 50%; display: inline-block;"></span>
                        <span style="font-weight: 600;">Connected</span>
                    </div>
                    <button id="disconnectBtn" style="background: none; border: none; color: #ef4444; cursor: pointer; padding: 6px;">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                
                <div>
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Address:</span>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span id="walletAddress" style="font-family: monospace; font-size: 14px;"></span>
                            <button id="copyBtn" style="background: none; border: none; color: #9333ea; cursor: pointer; padding: 4px;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="font-size: 14px; color: #6b7280;">Balance:</span>
                        <span id="walletBalance" style="font-size: 14px;">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" class="status success">
            Ready! Click "Connect Wallet" to choose from multiple wallet options.
        </div>

        <!-- Debug Section -->
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testConnection()" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 12px;
            ">Test Connection</button>
        </div>
    </div>

    <script>
        // Global variables
        let provider = null;
        let signer = null;
        let userAddress = null;
        let isConnected = false;

        // Utility functions
        function shortenAddr(addr) {
            if (!addr) return '';
            return addr.slice(0, 5) + "..." + addr.slice(-5);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateWalletUI() {
            const connectSection = document.getElementById('connectSection');
            const walletWidget = document.getElementById('walletWidget');

            if (isConnected && userAddress) {
                connectSection.style.display = 'none';
                walletWidget.style.display = 'block';
                
                document.getElementById('walletAddress').textContent = shortenAddr(userAddress);
                loadWalletBalance();
            } else {
                connectSection.style.display = 'block';
                walletWidget.style.display = 'none';
            }
        }

        async function loadWalletBalance() {
            try {
                if (userAddress) {
                    const rpcProvider = new ethers.providers.JsonRpcProvider('https://testnet-rpc.monad.xyz');
                    const balance = await rpcProvider.getBalance(userAddress);
                    const balanceInMON = ethers.utils.formatEther(balance);
                    document.getElementById('walletBalance').textContent = `${parseFloat(balanceInMON).toFixed(4)} MON`;
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('walletBalance').textContent = 'Error loading balance';
            }
        }

        // Wallet options
        const wallets = [
            {
                name: 'MetaMask',
                description: 'Connect using MetaMask browser extension',
                icon: '🦊',
                iconBg: 'linear-gradient(45deg, #f6851b, #e2761b)',
                id: 'metamask',
                connect: connectMetaMask,
                available: () => window.ethereum && window.ethereum.isMetaMask
            },
            {
                name: 'Coinbase Wallet',
                description: 'Connect using Coinbase Wallet',
                icon: '💼',
                iconBg: 'linear-gradient(45deg, #0052ff, #0041cc)',
                id: 'coinbase',
                connect: connectCoinbase,
                available: () => window.ethereum && window.ethereum.isCoinbaseWallet
            },
            {
                name: 'Trust Wallet',
                description: 'Connect using Trust Wallet',
                icon: '🛡️',
                iconBg: 'linear-gradient(45deg, #3375bb, #1a5490)',
                id: 'trust',
                connect: connectTrust,
                available: () => window.ethereum && window.ethereum.isTrust
            },
            {
                name: 'WalletConnect',
                description: 'Scan QR code with mobile wallet',
                icon: '🔗',
                iconBg: 'linear-gradient(45deg, #3b99fc, #1a73e8)',
                id: 'walletconnect',
                connect: connectWalletConnect,
                available: () => true
            },
            {
                name: 'Any Injected Wallet',
                description: 'Connect using any available wallet',
                icon: '💳',
                iconBg: 'linear-gradient(45deg, #8b5cf6, #7c3aed)',
                id: 'injected',
                connect: connectInjected,
                available: () => window.ethereum
            }
        ];

        // Show wallet selection modal
        function showWalletModal() {
            const modal = document.createElement('div');
            modal.className = 'wallet-modal';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            
            modalContent.innerHTML = `
                <div style="text-align: center; margin-bottom: 24px;">
                    <h2 style="margin: 0; font-size: 24px; font-weight: 700;">Connect Wallet</h2>
                    <p style="color: #6b7280; margin: 8px 0 0 0;">Choose your preferred wallet</p>
                </div>
                <div id="wallet-options"></div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    width: 100%;
                    padding: 12px;
                    margin-top: 20px;
                    border: 2px solid #f3f4f6;
                    background: #f9fafb;
                    border-radius: 12px;
                    cursor: pointer;
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                ">Cancel</button>
            `;

            const walletOptions = modalContent.querySelector('#wallet-options');
            
            wallets.forEach(wallet => {
                const isAvailable = wallet.available();
                const button = document.createElement('button');
                button.className = 'wallet-option';
                
                if (!isAvailable) {
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                }
                
                button.innerHTML = `
                    <div class="wallet-icon" style="background: ${wallet.iconBg};">
                        ${wallet.icon}
                    </div>
                    <div style="flex: 1; text-align: left;">
                        <div style="font-weight: 600;">${wallet.name}</div>
                        <div style="font-size: 14px; color: #6b7280; margin-top: 2px;">
                            ${isAvailable ? wallet.description : 'Not available - please install'}
                        </div>
                    </div>
                    <div style="color: ${isAvailable ? '#10b981' : '#ef4444'}; font-size: 18px;">
                        ${isAvailable ? '✓' : '✗'}
                    </div>
                `;
                
                if (isAvailable) {
                    button.onclick = () => {
                        modal.remove();
                        wallet.connect();
                    };
                }
                
                walletOptions.appendChild(button);
            });

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Close on backdrop click
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };
        }

        // Wallet connection functions
        async function connectMetaMask() {
            try {
                updateStatus('Connecting to MetaMask...', 'info');

                if (!window.ethereum || !window.ethereum.isMetaMask) {
                    updateStatus('MetaMask not found! Please install MetaMask extension.', 'error');
                    return;
                }

                await window.ethereum.request({ method: 'eth_requestAccounts' });
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('MetaMask connected successfully! 🦊', 'success');

            } catch (error) {
                console.error('MetaMask connection error:', error);
                if (error.code === 4001) {
                    updateStatus('Connection rejected by user', 'warning');
                } else {
                    updateStatus('MetaMask connection failed: ' + error.message, 'error');
                }
            }
        }

        async function connectCoinbase() {
            try {
                updateStatus('Connecting to Coinbase Wallet...', 'info');

                if (!window.ethereum || !window.ethereum.isCoinbaseWallet) {
                    updateStatus('Coinbase Wallet not found! Please install Coinbase Wallet.', 'error');
                    return;
                }

                await window.ethereum.request({ method: 'eth_requestAccounts' });
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('Coinbase Wallet connected successfully! 💼', 'success');

            } catch (error) {
                console.error('Coinbase connection error:', error);
                updateStatus('Coinbase connection failed: ' + error.message, 'error');
            }
        }

        async function connectTrust() {
            try {
                updateStatus('Connecting to Trust Wallet...', 'info');

                if (!window.ethereum || !window.ethereum.isTrust) {
                    updateStatus('Trust Wallet not found! Please install Trust Wallet.', 'error');
                    return;
                }

                await window.ethereum.request({ method: 'eth_requestAccounts' });
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('Trust Wallet connected successfully! 🛡️', 'success');

            } catch (error) {
                console.error('Trust Wallet connection error:', error);
                updateStatus('Trust Wallet connection failed: ' + error.message, 'error');
            }
        }

        async function connectWalletConnect() {
            updateStatus('WalletConnect integration coming soon! For now, please use MetaMask or another browser wallet.', 'warning');
        }

        async function connectInjected() {
            try {
                updateStatus('Connecting to wallet...', 'info');

                if (!window.ethereum) {
                    updateStatus('No wallet found! Please install MetaMask or another Web3 wallet.', 'error');
                    return;
                }

                await window.ethereum.request({ method: 'eth_requestAccounts' });
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('Wallet connected successfully! 💳', 'success');

            } catch (error) {
                console.error('Wallet connection error:', error);
                updateStatus('Wallet connection failed: ' + error.message, 'error');
            }
        }

        // Check and add Monad network
        async function checkAndAddNetwork() {
            try {
                const network = await provider.getNetwork();
                if (network.chainId !== 10143) {
                    updateStatus('Adding Monad Testnet...', 'warning');
                    await window.ethereum.request({
                        method: 'wallet_addEthereumChain',
                        params: [{
                            chainId: '0x279F', // 10143 in hex
                            chainName: 'Monad Testnet',
                            rpcUrls: ['https://testnet-rpc.monad.xyz'],
                            nativeCurrency: {
                                name: 'MON',
                                symbol: 'MON',
                                decimals: 18
                            },
                            blockExplorerUrls: ['https://testnet-explorer.monad.xyz']
                        }]
                    });
                }
            } catch (error) {
                console.error('Network error:', error);
                updateStatus('Failed to add Monad Testnet: ' + error.message, 'warning');
            }
        }

        // Disconnect wallet
        async function disconnectWallet() {
            provider = null;
            signer = null;
            userAddress = null;
            isConnected = false;

            updateWalletUI();
            updateStatus('Wallet disconnected', 'info');
        }

        // Copy address
        async function copyAddress() {
            if (userAddress) {
                try {
                    await navigator.clipboard.writeText(userAddress);
                    updateStatus('Address copied to clipboard! 📋', 'success');
                } catch (error) {
                    updateStatus('Failed to copy address', 'error');
                }
            }
        }

        // Test connection function
        function testConnection() {
            console.log('=== CONNECTION TEST ===');
            console.log('window.ethereum:', !!window.ethereum);
            console.log('MetaMask available:', !!(window.ethereum && window.ethereum.isMetaMask));
            console.log('Coinbase available:', !!(window.ethereum && window.ethereum.isCoinbaseWallet));
            console.log('Trust available:', !!(window.ethereum && window.ethereum.isTrust));
            console.log('Connected:', isConnected);
            console.log('Address:', userAddress);

            updateStatus('Check console for connection test results', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('connectBtn').addEventListener('click', showWalletModal);
            document.getElementById('disconnectBtn').addEventListener('click', disconnectWallet);
            document.getElementById('copyBtn').addEventListener('click', copyAddress);

            // Listen for account changes
            if (window.ethereum) {
                window.ethereum.on('accountsChanged', (accounts) => {
                    if (accounts.length === 0) {
                        disconnectWallet();
                    } else {
                        location.reload();
                    }
                });

                window.ethereum.on('chainChanged', () => {
                    location.reload();
                });
            }
        });
    </script>
</body>
</html>
    </script>
</body>
</html>
