{"name": "werant-backend", "version": "1.0.0", "description": "Werant backend with wallet connect and smart contracts on Monad testnet", "main": "src/server.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "server": "node src/server.js", "server:dev": "nodemon src/server.js", "test": "jest", "deploy:contracts": "npx hardhat run scripts/deploy.js --network monad-testnet", "compile": "npx hardhat compile"}, "dependencies": {"@openzeppelin/contracts": "^5.4.0", "@rainbow-me/rainbowkit": "^1.3.7", "@reown/appkit": "^1.7.16", "@reown/appkit-adapter-wagmi": "^1.7.16", "@tanstack/react-query": "^5.83.0", "@wagmi/connectors": "^5.9.0", "@wagmi/core": "^2.18.0", "@walletconnect/modal": "^2.7.0", "@walletconnect/sign-client": "^2.21.5", "@walletconnect/web3-provider": "^1.8.0", "axios": "^1.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^5.8.0", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "next": "^14.0.4", "pino-pretty": "^13.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "viem": "^2.33.0", "wagmi": "^2.16.0", "web3": "^4.2.0", "web3modal": "^1.9.12"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^2.0.0", "@nomiclabs/hardhat-ethers": "^2.2.3", "@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "hardhat": "^2.17.0", "jest": "^29.6.0", "nodemon": "^3.0.1", "typescript": "^5.3.3"}, "keywords": ["werant", "wallet-connect", "monad", "blockchain", "smart-contracts", "leaderboard", "voting"], "author": "Werant Team", "license": "MIT"}