<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - Simple Wallet Connect</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 3rem;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Gradient Button Styles */
        .gradient-btn {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 8px;
            padding: 18px 36px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.2s ease;
            transform: scale(1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
            border: none;
            cursor: pointer;
            font-size: 18px;
            background: linear-gradient(to right, #9333ea 20%, #ec4899 80%);
            color: white;
        }
        
        .gradient-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
            background: linear-gradient(to right, #7c3aed, #db2777);
        }
        
        .gradient-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: scale(1);
        }
        
        .wallet-widget {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 24px;
            backdrop-filter: blur(10px);
            color: #1a1b23;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-connected {
            background: #10b981;
        }
        
        .status-disconnected {
            background: #ef4444;
        }
        
        .copy-btn, .disconnect-btn {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 6px;
            border-radius: 6px;
            transition: color 0.2s ease;
            font-size: 14px;
        }
        
        .copy-btn:hover {
            color: #9333ea;
        }
        
        .disconnect-btn:hover {
            color: #ef4444;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid rgba(76, 175, 80, 0.5); }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid rgba(244, 67, 54, 0.5); }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid rgba(33, 150, 243, 0.5); }
        .warning { background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.5); }
        
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fee-info h3 {
            color: #ff6b6b;
            margin-top: 0;
        }
        
        .wallet-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .modal-content {
            background: white;
            border-radius: 24px;
            padding: 32px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            color: #333;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .wallet-option {
            width: 100%;
            padding: 16px;
            margin: 8px 0;
            border: 2px solid #f3f4f6;
            background: white;
            border-radius: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 16px;
            transition: all 0.2s ease;
        }
        
        .wallet-option:hover {
            border-color: #9333ea;
            background: #f8faff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(147, 51, 234, 0.15);
        }
        
        .wallet-icon-large {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Werant</h1>
            <p>Simple wallet connection with beautiful UI</p>
        </div>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <!-- Wallet Connection Section -->
        <div id="walletSection" style="text-align: center; margin: 40px 0;">
            <!-- Connect Button (shown when not connected) -->
            <div id="connectSection">
                <button id="connectBtn" class="gradient-btn">
                    <i class="fas fa-wallet"></i>
                    Connect Wallet
                </button>
            </div>
            
            <!-- Wallet Widget (shown when connected) -->
            <div id="walletWidget" class="wallet-widget" style="display: none;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span id="statusIndicator" class="status-indicator status-connected"></span>
                        <span style="font-weight: 600;">Connected</span>
                    </div>
                    <button id="disconnectBtn" class="disconnect-btn" title="Disconnect">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                
                <div>
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Address:</span>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span id="walletAddress" style="font-family: monospace; font-size: 14px;"></span>
                            <button id="copyBtn" class="copy-btn" title="Copy address">
                                <i id="copyIcon" class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Balance:</span>
                        <span id="walletBalance" style="font-size: 14px;">Loading...</span>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="font-size: 14px; color: #6b7280;">Network:</span>
                        <span id="networkName" style="font-size: 14px;">Monad Testnet</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" class="status info">
            Click "Connect Wallet" to connect your wallet
        </div>

        <!-- Debug Section -->
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="debugWallets()" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 12px;
            ">Debug Wallet Detection</button>
        </div>
    </div>

    <script>
        let provider = null;
        let signer = null;
        let userAddress = null;
        let isConnected = false;
        
        // Utility function to shorten wallet addresses
        function shortenAddr(addr) {
            if (!addr) return '';
            return addr.slice(0, 5) + "..." + addr.slice(-5);
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            setupEventListeners();
            updateStatus('Ready to connect! Click "Connect Wallet" to start.', 'info');
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('connectBtn').addEventListener('click', openWalletModal);
            document.getElementById('disconnectBtn').addEventListener('click', disconnectWallet);
            document.getElementById('copyBtn').addEventListener('click', copyAddress);
        }

        // Check wallet availability with better detection
        function checkWalletAvailability() {
            console.log('Checking wallet availability...');
            console.log('window.ethereum:', !!window.ethereum);

            if (window.ethereum) {
                console.log('ethereum.isMetaMask:', window.ethereum.isMetaMask);
                console.log('ethereum.isCoinbaseWallet:', window.ethereum.isCoinbaseWallet);
                console.log('ethereum.isTrust:', window.ethereum.isTrust);
                console.log('ethereum providers:', window.ethereum.providers);
            }
        }

        // Open wallet selection modal
        function openWalletModal() {
            // Debug wallet detection
            checkWalletAvailability();

            const wallets = [
                {
                    name: 'MetaMask',
                    description: 'Connect using browser extension',
                    icon: '🦊',
                    iconBg: 'linear-gradient(45deg, #f6851b, #e2761b)',
                    id: 'metamask',
                    available: () => {
                        // More comprehensive MetaMask detection
                        if (!window.ethereum) return false;

                        // Check for MetaMask directly
                        if (window.ethereum.isMetaMask) return true;

                        // Check in providers array (for multiple wallets)
                        if (window.ethereum.providers) {
                            return window.ethereum.providers.some(provider => provider.isMetaMask);
                        }

                        return false;
                    }
                },
                {
                    name: 'Coinbase Wallet',
                    description: 'Connect using Coinbase Wallet',
                    icon: '💼',
                    iconBg: 'linear-gradient(45deg, #0052ff, #0041cc)',
                    id: 'coinbase',
                    available: () => {
                        if (!window.ethereum) return false;
                        if (window.ethereum.isCoinbaseWallet) return true;
                        if (window.ethereum.providers) {
                            return window.ethereum.providers.some(provider => provider.isCoinbaseWallet);
                        }
                        return false;
                    }
                },
                {
                    name: 'Trust Wallet',
                    description: 'Connect using Trust Wallet',
                    icon: '🛡️',
                    iconBg: 'linear-gradient(45deg, #3375bb, #1a5490)',
                    id: 'trust',
                    available: () => {
                        if (!window.ethereum) return false;
                        if (window.ethereum.isTrust) return true;
                        if (window.ethereum.providers) {
                            return window.ethereum.providers.some(provider => provider.isTrust);
                        }
                        return false;
                    }
                },
                {
                    name: 'Any Wallet',
                    description: 'Connect using any available wallet',
                    icon: '💳',
                    iconBg: 'linear-gradient(45deg, #8b5cf6, #7c3aed)',
                    id: 'injected',
                    available: () => !!window.ethereum
                }
            ];

            const modal = document.createElement('div');
            modal.className = 'wallet-modal';

            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';

            modalContent.innerHTML = `
                <div style="text-align: center; margin-bottom: 24px;">
                    <h2 style="margin: 0; font-size: 24px; font-weight: 700;">Connect a Wallet</h2>
                    <p style="color: #6b7280; margin: 8px 0 0 0;">Choose how you want to connect</p>
                </div>
                <div id="wallet-options"></div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    width: 100%;
                    padding: 12px;
                    margin-top: 20px;
                    border: 2px solid #f3f4f6;
                    background: #f9fafb;
                    border-radius: 12px;
                    cursor: pointer;
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                ">Cancel</button>
            `;

            const walletOptions = modalContent.querySelector('#wallet-options');

            wallets.forEach(wallet => {
                const isAvailable = wallet.available();
                const button = document.createElement('button');
                button.className = 'wallet-option';

                if (!isAvailable) {
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                }

                button.innerHTML = `
                    <div class="wallet-icon-large" style="background: ${wallet.iconBg};">
                        ${wallet.icon}
                    </div>
                    <div style="flex: 1; text-align: left;">
                        <div style="font-weight: 600;">${wallet.name}</div>
                        <div style="font-size: 14px; color: #6b7280; margin-top: 2px;">
                            ${isAvailable ? wallet.description : 'Not available'}
                        </div>
                    </div>
                    <div style="color: ${isAvailable ? '#10b981' : '#ef4444'}; font-size: 18px;">
                        ${isAvailable ? '✓' : '✗'}
                    </div>
                `;

                if (isAvailable) {
                    button.onclick = () => {
                        modal.remove();
                        connectWallet(wallet.id, wallet.name);
                    };
                }

                walletOptions.appendChild(button);
            });

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Close on backdrop click
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };

            // Close on escape key
            const escapeHandler = (e) => {
                if (e.key === 'Escape') {
                    modal.remove();
                    document.removeEventListener('keydown', escapeHandler);
                }
            };
            document.addEventListener('keydown', escapeHandler);
        }

        // Connect wallet function
        async function connectWallet(walletId, walletName) {
            try {
                updateStatus(`Connecting to ${walletName}...`, 'info');

                if (!window.ethereum) {
                    updateStatus(`No wallet found! Please install ${walletName}.`, 'error');
                    return;
                }

                let selectedProvider = window.ethereum;

                // If multiple providers exist, try to select the right one
                if (window.ethereum.providers && window.ethereum.providers.length > 0) {
                    console.log('Multiple providers detected:', window.ethereum.providers);

                    if (walletId === 'metamask') {
                        const metamaskProvider = window.ethereum.providers.find(provider => provider.isMetaMask);
                        if (metamaskProvider) {
                            selectedProvider = metamaskProvider;
                            console.log('Selected MetaMask provider');
                        }
                    } else if (walletId === 'coinbase') {
                        const coinbaseProvider = window.ethereum.providers.find(provider => provider.isCoinbaseWallet);
                        if (coinbaseProvider) {
                            selectedProvider = coinbaseProvider;
                            console.log('Selected Coinbase provider');
                        }
                    } else if (walletId === 'trust') {
                        const trustProvider = window.ethereum.providers.find(provider => provider.isTrust);
                        if (trustProvider) {
                            selectedProvider = trustProvider;
                            console.log('Selected Trust provider');
                        }
                    }
                }

                console.log('Using provider:', selectedProvider);

                // Request account access from the selected provider
                await selectedProvider.request({ method: 'eth_requestAccounts' });

                provider = new ethers.providers.Web3Provider(selectedProvider);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                console.log('Connected address:', userAddress);

                // Check if we're on the right network
                const network = await provider.getNetwork();
                console.log('Current network:', network);

                if (network.chainId !== 10143) {
                    updateStatus('Adding Monad Testnet...', 'warning');
                    await addMonadNetwork();
                } else {
                    updateStatus(`${walletName} connected successfully! ${getWalletEmoji(walletId)}`, 'success');
                }

                updateWalletUI();

            } catch (error) {
                console.error('Wallet connection error:', error);
                if (error.code === 4001) {
                    updateStatus('Connection rejected by user', 'warning');
                } else if (error.code === -32002) {
                    updateStatus('Connection request already pending. Please check your wallet.', 'warning');
                } else {
                    updateStatus(`${walletName} connection failed: ${error.message}`, 'error');
                }
            }
        }

        // Get wallet emoji
        function getWalletEmoji(walletId) {
            const emojis = {
                'metamask': '🦊',
                'coinbase': '💼',
                'trust': '🛡️',
                'injected': '💳'
            };
            return emojis[walletId] || '✅';
        }

        // Add Monad Network
        async function addMonadNetwork() {
            try {
                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [{
                        chainId: '0x279F', // 10143 in hex
                        chainName: 'Monad Testnet',
                        rpcUrls: ['https://testnet-rpc.monad.xyz'],
                        nativeCurrency: {
                            name: 'MON',
                            symbol: 'MON',
                            decimals: 18
                        },
                        blockExplorerUrls: ['https://testnet-explorer.monad.xyz']
                    }]
                });
                updateStatus('Monad Testnet added! Wallet connected successfully! 🌐', 'success');
            } catch (error) {
                console.error('Add network error:', error);
                if (error.code === 4001) {
                    updateStatus('Network addition rejected by user', 'warning');
                } else {
                    updateStatus('Failed to add network: ' + error.message, 'error');
                }
            }
        }

        // Update wallet UI
        function updateWalletUI() {
            const connectSection = document.getElementById('connectSection');
            const walletWidget = document.getElementById('walletWidget');

            if (isConnected && userAddress) {
                connectSection.style.display = 'none';
                walletWidget.style.display = 'block';

                document.getElementById('walletAddress').textContent = shortenAddr(userAddress);
                loadWalletBalance();
            } else {
                connectSection.style.display = 'block';
                walletWidget.style.display = 'none';
            }
        }

        // Load wallet balance
        async function loadWalletBalance() {
            try {
                if (provider && userAddress) {
                    const balance = await provider.getBalance(userAddress);
                    const balanceInMON = ethers.utils.formatEther(balance);
                    document.getElementById('walletBalance').textContent = `${parseFloat(balanceInMON).toFixed(4)} MON`;
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('walletBalance').textContent = 'Error loading balance';
            }
        }

        // Disconnect wallet
        async function disconnectWallet() {
            userAddress = null;
            isConnected = false;
            provider = null;
            signer = null;

            updateWalletUI();
            updateStatus('Wallet disconnected', 'info');
        }

        // Copy address to clipboard
        async function copyAddress() {
            if (userAddress) {
                try {
                    await navigator.clipboard.writeText(userAddress);
                    const copyIcon = document.getElementById('copyIcon');
                    copyIcon.className = 'fas fa-check-circle';

                    setTimeout(() => {
                        copyIcon.className = 'fas fa-copy';
                    }, 2000);

                    updateStatus('Address copied to clipboard! 📋', 'success');
                } catch (error) {
                    console.error('Failed to copy address:', error);
                    updateStatus('Failed to copy address', 'error');
                }
            }
        }

        // Update status message
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Debug function to check wallet detection
        function debugWallets() {
            console.log('=== WALLET DEBUG INFO ===');
            console.log('window.ethereum exists:', !!window.ethereum);

            if (window.ethereum) {
                console.log('ethereum.isMetaMask:', window.ethereum.isMetaMask);
                console.log('ethereum.isCoinbaseWallet:', window.ethereum.isCoinbaseWallet);
                console.log('ethereum.isTrust:', window.ethereum.isTrust);
                console.log('ethereum.isRabby:', window.ethereum.isRabby);
                console.log('ethereum.providers:', window.ethereum.providers);

                if (window.ethereum.providers) {
                    console.log('Provider details:');
                    window.ethereum.providers.forEach((provider, index) => {
                        console.log(`Provider ${index}:`, {
                            isMetaMask: provider.isMetaMask,
                            isCoinbaseWallet: provider.isCoinbaseWallet,
                            isTrust: provider.isTrust,
                            isRabby: provider.isRabby
                        });
                    });
                }
            } else {
                console.log('No ethereum provider found');
            }

            // Show debug info in status
            let debugInfo = 'Debug: ';
            if (!window.ethereum) {
                debugInfo += 'No wallet detected. Please install MetaMask or another Web3 wallet.';
            } else {
                const wallets = [];
                if (window.ethereum.isMetaMask) wallets.push('MetaMask');
                if (window.ethereum.isCoinbaseWallet) wallets.push('Coinbase');
                if (window.ethereum.isTrust) wallets.push('Trust');
                if (window.ethereum.providers) {
                    window.ethereum.providers.forEach(provider => {
                        if (provider.isMetaMask && !wallets.includes('MetaMask')) wallets.push('MetaMask');
                        if (provider.isCoinbaseWallet && !wallets.includes('Coinbase')) wallets.push('Coinbase');
                        if (provider.isTrust && !wallets.includes('Trust')) wallets.push('Trust');
                    });
                }
                debugInfo += wallets.length > 0 ? `Detected: ${wallets.join(', ')}` : 'Wallet detected but type unknown';
            }

            updateStatus(debugInfo, 'info');
            console.log('=== END DEBUG INFO ===');
        }

        // Listen for account changes
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', function (accounts) {
                if (accounts.length === 0) {
                    disconnectWallet();
                } else {
                    location.reload();
                }
            });

            window.ethereum.on('chainChanged', function (chainId) {
                location.reload();
            });
        }
    </script>
</body>
</html>
