# Server Configuration
PORT=3000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/werant

# JWT
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRE=7d

# Monad Testnet Configuration
MONAD_TESTNET_RPC_URL=https://testnet-rpc.monad.xyz
MONAD_TESTNET_CHAIN_ID=41454
MONAD_TESTNET_EXPLORER=https://testnet-explorer.monad.xyz

# Private Keys (for deployment)
DEPLOYER_PRIVATE_KEY=your_private_key_here
ADMIN_PRIVATE_KEY=your_admin_private_key_here

# Smart Contract Addresses (will be filled after deployment)
WALLET_CONNECT_CONTRACT_ADDRESS=
LEADERBOARD_CONTRACT_ADDRESS=
REWARDS_CONTRACT_ADDRESS=

# Gas Configuration
DEFAULT_GAS_LIMIT=500000
WALLET_CONNECT_GAS_PRICE=100000000000000000  # 0.1 MON in wei
VOTING_GAS_PRICE=100000000000000000          # 0.1 MON in wei

# API Keys
INFURA_PROJECT_ID=your_infura_project_id
ALCHEMY_API_KEY=your_alchemy_api_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
FRONTEND_URL=http://localhost:3001
