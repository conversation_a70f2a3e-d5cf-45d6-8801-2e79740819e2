<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - WalletConnect Integration</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <script src="https://unpkg.com/@walletconnect/modal@2.7.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@walletconnect/sign-client@2.21.5/dist/index.umd.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 3rem;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .connect-section {
            text-align: center;
            margin: 40px 0;
        }
        /* Gradient Button Styles */
        .gradient-btn {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 8px;
            padding: 12px 24px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.2s ease;
            transform: scale(1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: none;
            cursor: pointer;
            font-size: 16px;
            position: relative;
        }

        .gradient-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .gradient-btn.variant-1 {
            background: linear-gradient(to right, #9333ea 20%, #ec4899 80%);
            color: white;
        }

        .gradient-btn.variant-1:hover {
            background: linear-gradient(to right, #7c3aed, #db2777);
        }

        .gradient-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: scale(1);
        }

        .wallet-widget {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-connected {
            background: #10b981;
        }

        .status-disconnected {
            background: #ef4444;
        }

        .copy-btn {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s ease;
        }

        .copy-btn:hover {
            color: #9333ea;
        }

        .disconnect-btn {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s ease;
        }

        .disconnect-btn:hover {
            color: #ef4444;
        }
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid rgba(76, 175, 80, 0.5); }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid rgba(244, 67, 54, 0.5); }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid rgba(33, 150, 243, 0.5); }
        .warning { background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.5); }
        .wallet-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .wallet-info h3 {
            margin-top: 0;
            color: #4ecdc4;
        }
        .address {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            word-break: break-all;
            margin: 10px 0;
        }
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fee-info h3 {
            color: #ff6b6b;
            margin-top: 0;
        }
        .actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        
        /* RainbowKit-style Modal */
        .wallet-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .modal-content {
            background: white;
            border-radius: 24px;
            padding: 32px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            color: #333;
            animation: slideIn 0.3s ease-out;
            position: relative;
        }
        
        .modal-header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            color: #1a1b23;
        }
        
        .modal-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 8px 0 0 0;
        }
        
        .wallet-option {
            width: 100%;
            padding: 16px;
            margin: 8px 0;
            border: 2px solid #f3f4f6;
            background: white;
            border-radius: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .wallet-option:hover {
            border-color: #667eea;
            background: #f8faff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .wallet-option:active {
            transform: translateY(0);
        }
        
        .wallet-option.unavailable {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }
        
        .wallet-option.unavailable:hover {
            border-color: #e5e7eb;
            background: white;
            transform: none;
            box-shadow: none;
        }
        
        .wallet-icon-large {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .wallet-info-text {
            flex: 1;
            text-align: left;
        }
        
        .wallet-name {
            font-weight: 600;
            color: #1a1b23;
            margin: 0;
        }
        
        .wallet-description {
            font-size: 14px;
            color: #6b7280;
            margin: 2px 0 0 0;
        }
        
        .wallet-status {
            color: #10b981;
            font-size: 18px;
        }
        
        .wallet-status.unavailable {
            color: #ef4444;
        }
        
        .close-button {
            width: 100%;
            padding: 12px;
            margin-top: 20px;
            border: 2px solid #f3f4f6;
            background: #f9fafb;
            border-radius: 12px;
            cursor: pointer;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .close-button:hover {
            background: #f3f4f6;
            border-color: #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Werant</h1>
            <p>Connect your wallet with proper WalletConnect v2 integration</p>
        </div>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <!-- Wallet Connection Section -->
        <div id="walletSection" style="text-align: center; margin: 40px 0;">
            <!-- Connect Button (shown when not connected) -->
            <div id="connectSection">
                <button id="connectBtn" class="gradient-btn variant-1" style="font-size: 18px; padding: 18px 36px;">
                    <i class="fas fa-wallet"></i>
                    Connect Wallet
                </button>
            </div>

            <!-- Wallet Widget (shown when connected) -->
            <div id="walletWidget" class="wallet-widget" style="display: none; max-width: 400px; margin: 0 auto;">
                <div style="display: flex; align-items: center; justify-content: between; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span id="statusIndicator" class="status-indicator status-connected"></span>
                        <span style="font-weight: 600; color: #1a1b23;">Connected</span>
                    </div>
                    <button id="disconnectBtn" class="disconnect-btn" title="Disconnect">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>

                <div style="margin-bottom: 12px;">
                    <div style="display: flex; align-items: center; justify-content: between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Address:</span>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span id="walletAddress" style="font-family: monospace; font-size: 14px; color: #1a1b23;"></span>
                            <button id="copyBtn" class="copy-btn" title="Copy address">
                                <i id="copyIcon" class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; justify-content: between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Balance:</span>
                        <span id="walletBalance" style="font-size: 14px; color: #1a1b23;">Loading...</span>
                    </div>

                    <div style="display: flex; align-items: center; justify-content: between;">
                        <span style="font-size: 14px; color: #6b7280;">Network:</span>
                        <span id="networkName" style="font-size: 14px; color: #1a1b23;">Monad Testnet</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" class="status info">
            Click "Connect Wallet" to connect with Reown AppKit (formerly WalletConnect)
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3002';
        let appKit = null;
        let provider = null;
        let signer = null;
        let userAddress = null;
        let isConnected = false;
        let copied = false;

        // Utility function to shorten wallet addresses
        function shortenAddr(addr) {
            if (!addr) return '';
            return addr.slice(0, 5) + "..." + addr.slice(-5);
        }

        // Initialize WalletConnect v2
        async function initAppKit() {
            try {
                // Check if WalletConnect libraries are loaded
                if (typeof WalletConnectModal === 'undefined') {
                    console.error('WalletConnect Modal not loaded');
                    updateStatus('WalletConnect libraries not loaded. Please refresh the page.', 'error');
                    return;
                }

                // Initialize WalletConnect Modal
                appKit = new WalletConnectModal.WalletConnectModal({
                    projectId: '2f5a6e7c8b9d0e1f2a3b4c5d6e7f8a9b', // Replace with your real project ID from https://cloud.walletconnect.com
                    chains: [10143], // Monad Testnet
                    themeMode: 'light',
                    themeVariables: {
                        '--w3m-accent': '#9333ea',
                        '--w3m-background-color': '#9333ea'
                    }
                });

                console.log('WalletConnect Modal initialized successfully');
                updateStatus('Wallet connection ready! Click "Connect Wallet" to start.', 'success');
            } catch (error) {
                console.error('Failed to initialize WalletConnect:', error);
                updateStatus('Failed to initialize wallet connection: ' + error.message, 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            await initAppKit();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Connect button
            document.getElementById('connectBtn').addEventListener('click', async () => {
                await connectWallet();
            });

            // Disconnect button
            document.getElementById('disconnectBtn').addEventListener('click', async () => {
                await disconnectWallet();
            });

            // Copy address button
            document.getElementById('copyBtn').addEventListener('click', copyAddress);
        }

        // Connect wallet function
        async function connectWallet() {
            try {
                updateStatus('Opening wallet selection...', 'info');

                // Try WalletConnect first
                if (appKit) {
                    await appKit.openModal();

                    // Listen for connection
                    appKit.subscribeModal((state) => {
                        if (!state.open && userAddress) {
                            // Modal closed and we have an address - connection successful
                            updateStatus('Wallet connected successfully! 🎉', 'success');
                        }
                    });
                } else {
                    // Fallback to MetaMask if available
                    if (window.ethereum) {
                        updateStatus('Connecting to MetaMask...', 'info');
                        await window.ethereum.request({ method: 'eth_requestAccounts' });

                        provider = new ethers.providers.Web3Provider(window.ethereum);
                        signer = provider.getSigner();
                        userAddress = await signer.getAddress();
                        isConnected = true;

                        // Check network
                        const network = await provider.getNetwork();
                        if (network.chainId !== 10143) {
                            await addMonadNetwork();
                        }

                        updateWalletUI();
                        updateStatus('MetaMask connected successfully! 🦊', 'success');
                    } else {
                        updateStatus('No wallet found. Please install MetaMask or another Web3 wallet.', 'error');
                    }
                }
            } catch (error) {
                console.error('Connection error:', error);
                if (error.code === 4001) {
                    updateStatus('Connection rejected by user', 'warning');
                } else {
                    updateStatus('Connection failed: ' + error.message, 'error');
                }
            }
        }

        // Disconnect wallet function
        async function disconnectWallet() {
            try {
                if (appKit) {
                    await appKit.disconnect();
                }

                userAddress = null;
                isConnected = false;
                provider = null;
                signer = null;

                updateWalletUI();
                updateStatus('Wallet disconnected', 'info');
            } catch (error) {
                console.error('Disconnect error:', error);
                updateStatus('Disconnect failed: ' + error.message, 'error');
            }
        }

        // Add Monad Network to wallet
        async function addMonadNetwork() {
            try {
                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [{
                        chainId: '0x279F', // 10143 in hex
                        chainName: 'Monad Testnet',
                        rpcUrls: ['https://testnet-rpc.monad.xyz'],
                        nativeCurrency: {
                            name: 'MON',
                            symbol: 'MON',
                            decimals: 18
                        },
                        blockExplorerUrls: ['https://testnet-explorer.monad.xyz']
                    }]
                });
                updateStatus('Monad Testnet added to wallet! 🌐', 'success');
            } catch (error) {
                console.error('Add network error:', error);
                if (error.code === 4001) {
                    updateStatus('Network addition rejected by user', 'warning');
                } else {
                    updateStatus('Failed to add network: ' + error.message, 'error');
                }
            }
        }

        // Update wallet UI based on connection status
        function updateWalletUI() {
            const connectSection = document.getElementById('connectSection');
            const walletWidget = document.getElementById('walletWidget');
            const statusIndicator = document.getElementById('statusIndicator');
            const walletAddressEl = document.getElementById('walletAddress');
            const walletBalanceEl = document.getElementById('walletBalance');

            if (isConnected && userAddress) {
                // Show wallet widget, hide connect button
                connectSection.style.display = 'none';
                walletWidget.style.display = 'block';

                // Update address
                walletAddressEl.textContent = shortenAddr(userAddress);

                // Update status indicator
                statusIndicator.className = 'status-indicator status-connected';

                // Load balance
                loadWalletBalance();
            } else {
                // Show connect button, hide wallet widget
                connectSection.style.display = 'block';
                walletWidget.style.display = 'none';

                // Reset values
                walletAddressEl.textContent = '';
                walletBalanceEl.textContent = 'Loading...';
            }
        }

        // Load wallet balance
        async function loadWalletBalance() {
            try {
                if (userAddress) {
                    const provider = new ethers.providers.JsonRpcProvider('https://testnet-rpc.monad.xyz');
                    const balance = await provider.getBalance(userAddress);
                    const balanceInMON = ethers.utils.formatEther(balance);
                    document.getElementById('walletBalance').textContent = `${parseFloat(balanceInMON).toFixed(4)} MON`;
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('walletBalance').textContent = 'Error loading balance';
            }
        }

        // Copy address to clipboard
        async function copyAddress() {
            if (userAddress) {
                try {
                    await navigator.clipboard.writeText(userAddress);
                    const copyIcon = document.getElementById('copyIcon');
                    copyIcon.className = 'fas fa-check-circle';
                    copied = true;

                    setTimeout(() => {
                        copyIcon.className = 'fas fa-copy';
                        copied = false;
                    }, 2000);

                    updateStatus('Address copied to clipboard! 📋', 'success');
                } catch (error) {
                    console.error('Failed to copy address:', error);
                    updateStatus('Failed to copy address', 'error');
                }
            }
        }

        // Update status message
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

    </script>
</body>
</html>
    </script>
</body>
</html>
