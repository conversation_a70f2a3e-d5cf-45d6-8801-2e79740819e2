# Werant Backend

A blockchain-based voting and leaderboard system built on Monad Testnet with wallet connection functionality.

## 🚀 Features

- **Wallet Connection**: Users pay 0.25 MON to connect their wallets
- **Voting System**: Users pay 0.1 MON to vote on projects (requires wallet connection)
- **Leaderboard**: Real-time ranking of projects based on votes and ratings
- **Smart Contracts**: Deployed on Monad Testnet for transparency and decentralization
- **RESTful API**: Complete backend API for frontend integration

## 🏗️ Architecture

### Smart Contracts
- **WerantWalletConnect**: Handles wallet connections with 0.25 MON fee
- **WerantLeaderboard**: Manages projects and voting with 0.1 MON fee per vote

### Backend Services
- **Express.js API**: RESTful endpoints for all operations
- **MongoDB**: Database for caching and analytics
- **Ethers.js**: Blockchain interaction and transaction verification

## 📋 Prerequisites

- Node.js (v16 or higher)
- MongoDB (running locally or remote)
- Monad Testnet MON tokens for deployment and testing

## 🛠️ Installation

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd werant-backend
npm install
```

2. **Set up environment variables**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start MongoDB**:
```bash
sudo systemctl start mongod
```

4. **Compile smart contracts**:
```bash
npx hardhat compile
```

5. **Deploy contracts to Monad Testnet**:
```bash
# Add your private key to .env first
npx hardhat run scripts/deploy.js --network monad-testnet
```

6. **Start the server**:
```bash
npm run dev
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `MONGODB_URI` | MongoDB connection string | mongodb://localhost:27017/werant |
| `MONAD_TESTNET_RPC_URL` | Monad testnet RPC endpoint | https://testnet-rpc.monad.xyz |
| `DEPLOYER_PRIVATE_KEY` | Private key for contract deployment | - |
| `WALLET_CONNECT_CONTRACT_ADDRESS` | Deployed wallet contract address | - |
| `LEADERBOARD_CONTRACT_ADDRESS` | Deployed leaderboard contract address | - |

### Gas Fees

- **Wallet Connection**: 0.25 MON
- **Voting**: 0.1 MON per vote
- **Project Creation**: Gas fees only (no additional fee)

## 📡 API Endpoints

### Wallet Management
- `GET /api/wallet/status/:address` - Get wallet connection status
- `POST /api/wallet/connect` - Record wallet connection
- `POST /api/wallet/disconnect` - Record wallet disconnection
- `GET /api/wallet/stats` - Get connection statistics

### Leaderboard & Voting
- `GET /api/leaderboard` - Get project leaderboard
- `POST /api/leaderboard/project` - Create new project
- `POST /api/leaderboard/vote` - Submit vote (requires wallet connection)
- `GET /api/leaderboard/project/:id` - Get project details
- `GET /api/leaderboard/stats` - Get voting statistics

### Contract Information
- `GET /api/contracts/info` - Get contract addresses and network info
- `GET /api/contracts/network` - Get current network status
- `GET /api/contracts/transaction/:hash` - Validate transaction
- `GET /api/contracts/events` - Get recent contract events

### System
- `GET /health` - Health check
- `GET /` - API information

## 🔐 Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **CORS Protection**: Configured for specific frontend origins
- **Transaction Verification**: All blockchain transactions are verified
- **Input Validation**: Comprehensive validation for all inputs
- **Wallet Address Validation**: Ensures valid Ethereum addresses

## 🧪 Testing

### Manual Testing

1. **Health Check**:
```bash
curl http://localhost:3000/health
```

2. **Get Contract Info**:
```bash
curl http://localhost:3000/api/contracts/info
```

3. **Check Wallet Status**:
```bash
curl http://localhost:3000/api/wallet/status/******************************************
```

### Frontend Integration

The API is designed to work with web3 frontends. Typical flow:

1. **Connect Wallet**: User connects wallet via MetaMask/WalletConnect
2. **Pay Connection Fee**: User sends 0.25 MON to wallet contract
3. **Record Connection**: Frontend calls `/api/wallet/connect` with transaction details
4. **Vote on Projects**: User can now vote by paying 0.1 MON per vote
5. **View Leaderboard**: Anyone can view the leaderboard without fees

## 🌐 Monad Testnet Setup

### Add Monad Testnet to MetaMask

```json
{
  "chainId": "0xA1F6",
  "chainName": "Monad Testnet",
  "rpcUrls": ["https://testnet-rpc.monad.xyz"],
  "nativeCurrency": {
    "name": "MON",
    "symbol": "MON",
    "decimals": 18
  },
  "blockExplorerUrls": ["https://testnet-explorer.monad.xyz"]
}
```

### Get Testnet MON

Visit the Monad testnet faucet to get test MON tokens for development.

## 📊 Database Schema

### Collections

- **walletconnections**: Wallet connection records
- **projects**: Project information and stats
- **votes**: Individual vote records

### Indexes

Optimized indexes for:
- Wallet address lookups
- Project queries
- Vote aggregations
- Timestamp-based queries

## 🚀 Deployment

### Production Checklist

- [ ] Update environment variables
- [ ] Set strong JWT secret
- [ ] Configure production MongoDB
- [ ] Set up proper private keys
- [ ] Deploy contracts to mainnet
- [ ] Configure rate limiting
- [ ] Set up monitoring
- [ ] Enable HTTPS

### Docker Deployment

```dockerfile
# Dockerfile example
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API endpoints

## 🔗 Links

- [Monad Testnet Explorer](https://testnet-explorer.monad.xyz)
- [Monad Documentation](https://docs.monad.xyz)
- [Hardhat Documentation](https://hardhat.org/docs)
