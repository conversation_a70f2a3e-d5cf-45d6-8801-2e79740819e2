# 🚀 Werant - Custom Wallet Connect Modal

A beautiful, custom wallet connection modal built with **React**, **Wagmi**, **RainbowKit**, and **TypeScript**. Features a dark theme design matching modern Web3 UX patterns.

## ✨ Features

- **🎨 Custom Dark Modal** - Beautiful dark theme matching your design
- **🦊 MetaMask Detection** - Shows "INSTALLED" when MetaMask is detected
- **🔗 WalletConnect Support** - QR code scanning for mobile wallets
- **🛡️ Trust Wallet** - Direct Trust Wallet integration
- **💳 Universal Fallback** - "All Wallets" option for any injected wallet
- **⚡ Real-time Status** - Dynamic connection indicators
- **📱 Mobile Responsive** - Works perfectly on all devices
- **🌐 Monad Testnet** - Pre-configured for Monad blockchain

## 🛠️ Tech Stack

- **React 18** - Modern React with hooks
- **TypeScript** - Type-safe development
- **Wagmi** - React hooks for Ethereum
- **RainbowKit** - Wallet connection library
- **Viem** - TypeScript Ethereum library
- **Vite** - Fast build tool

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Start Development Server

```bash
npm run dev
```

### 3. Open in Browser

Navigate to `http://localhost:5173`

## 📁 Project Structure

```
werant-custom-wallet-modal/
├── src/
│   ├── CustomWalletModal.tsx    # Main wallet modal component
│   ├── CustomWalletModal.css    # Modal styling (dark theme)
│   ├── App.tsx                  # Main app component
│   ├── App.css                  # App styling
│   └── main.tsx                 # React app entry point
├── index.html                   # HTML template
├── vite.config.ts              # Vite configuration
├── package.json                # Dependencies
└── README-WALLET-MODAL.md      # This file
```

## 🎨 Modal Features

### Wallet Options

1. **🔗 WalletConnect**
   - QR code scanning
   - Mobile wallet support
   - Blue "QR CODE" tag

2. **🦊 MetaMask**
   - Auto-detection
   - Green "INSTALLED" tag when available
   - Red "NOT INSTALLED" when missing

3. **🛡️ Trust Wallet**
   - Direct connection
   - Installation detection
   - Status indicators

4. **💳 All Wallets**
   - Universal fallback
   - "460+" wallets supported
   - Gray tag indicator

### UI Elements

- **Dark Theme** - `#1a1b23` background with `#252631` cards
- **Gradient Buttons** - Purple to pink gradients
- **Status Indicators** - Real-time connection status
- **Animations** - Smooth hover and loading effects
- **Responsive** - Mobile-first design

## ⚙️ Configuration

### Wallet Connect Project ID

Update your Reown (WalletConnect) project ID in:

```tsx
// CustomWalletModal.tsx
const walletOptions = [
  {
    id: 'walletconnect',
    // ... other config
    connector: walletConnect({
      projectId: 'YOUR_PROJECT_ID_HERE', // Replace with your ID
      showQrModal: true,
    }),
  },
  // ...
];
```

### Network Configuration

The app is pre-configured for Monad Testnet:

```tsx
// main.tsx
const monadTestnet = {
  id: 10143,
  name: 'Monad Testnet',
  rpcUrls: {
    default: { http: ['https://testnet-rpc.monad.xyz'] },
  },
  // ...
};
```

## 🎯 Usage Examples

### Basic Usage

```tsx
import CustomWalletModal from './CustomWalletModal';

function App() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div>
      <button onClick={() => setIsModalOpen(true)}>
        Connect Wallet
      </button>
      
      <CustomWalletModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}
```

### With Connection Status

```tsx
import { useAccount } from 'wagmi';

function WalletButton() {
  const { address, isConnected } = useAccount();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div>
      {isConnected ? (
        <div>Connected: {address?.slice(0, 6)}...{address?.slice(-4)}</div>
      ) : (
        <button onClick={() => setIsModalOpen(true)}>
          Connect Wallet
        </button>
      )}
      
      <CustomWalletModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}
```

## 🔧 Customization

### Styling

Modify `CustomWalletModal.css` to customize:

- Colors and gradients
- Border radius and spacing
- Animations and transitions
- Responsive breakpoints

### Wallet Options

Add or remove wallets in `CustomWalletModal.tsx`:

```tsx
const walletOptions = [
  // Add new wallet
  {
    id: 'newwallet',
    name: 'New Wallet',
    icon: '🔥',
    tag: 'NEW',
    tagColor: '#ff6b6b',
    connector: newWalletConnector(),
    available: checkNewWalletAvailability(),
  },
  // ... existing wallets
];
```

## 📱 Mobile Support

The modal is fully responsive with:

- Touch-friendly button sizes
- Mobile-optimized spacing
- Responsive grid layouts
- Proper viewport handling

## 🐛 Troubleshooting

### Common Issues

1. **MetaMask not detected**
   - Ensure MetaMask extension is installed and enabled
   - Check browser console for errors

2. **WalletConnect not working**
   - Verify your project ID is correct
   - Check network connectivity

3. **Styling issues**
   - Ensure CSS files are imported correctly
   - Check for conflicting styles

### Debug Mode

Enable debug logging:

```tsx
// Add to CustomWalletModal.tsx
console.log('Wallet detection:', {
  hasMetaMask,
  hasTrustWallet,
  ethereum: window.ethereum
});
```

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Preview Build

```bash
npm run preview
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

---

**Built with ❤️ for the Werant ecosystem**
