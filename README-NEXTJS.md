# 🚀 Werant - Next.js Custom Wallet Connect Modal

A beautiful, custom wallet connection modal built with **Next.js 14**, **React**, **Wagmi**, **RainbowKit**, and **TypeScript**. Features a dark theme design matching modern Web3 UX patterns with server-side rendering support.

## ✨ Features

- **🎨 Custom Dark Modal** - Beautiful dark theme matching your design
- **🦊 MetaMask Detection** - Shows "INSTALLED" when MetaMask is detected
- **🔗 WalletConnect Support** - QR code scanning for mobile wallets
- **🛡️ Trust Wallet** - Direct Trust Wallet integration
- **💳 Universal Fallback** - "All Wallets" option for any injected wallet
- **⚡ Real-time Status** - Dynamic connection indicators
- **📱 Mobile Responsive** - Works perfectly on all devices
- **🌐 Monad Testnet** - Pre-configured for Monad blockchain
- **🚀 Next.js 14** - App Router with SSR support
- **🎯 TypeScript** - Full type safety

## 🛠️ Tech Stack

- **Next.js 14** - React framework with App Router
- **React 18** - Modern React with hooks
- **TypeScript** - Type-safe development
- **Wagmi** - React hooks for Ethereum
- **RainbowKit** - Wallet connection library
- **Viem** - TypeScript Ethereum library
- **CSS Modules** - Scoped styling

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Copy the Next.js package.json
cp package-nextjs.json package.json

# Install dependencies
npm install
```

### 2. Environment Setup

```bash
# Copy environment variables
cp .env.example .env.local

# Edit .env.local with your values
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id_here
```

### 3. Start Development Server

```bash
npm run dev
```

### 4. Open in Browser

Navigate to `http://localhost:3000`

## 📁 Project Structure

```
werant-nextjs-wallet-modal/
├── src/
│   ├── app/
│   │   ├── layout.tsx             # Root layout with providers
│   │   ├── page.tsx               # Home page
│   │   ├── providers.tsx          # Wagmi & RainbowKit providers
│   │   └── globals.css            # Global styles
│   └── components/
│       ├── CustomWalletModal.tsx  # Main wallet modal component
│       └── CustomWalletModal.module.css # Modal styles (CSS Modules)
├── next.config.js                 # Next.js configuration
├── tsconfig.json                  # TypeScript configuration
├── package-nextjs.json            # Next.js dependencies
├── .env.local                     # Environment variables
└── README-NEXTJS.md               # This file
```

## 🎨 Modal Features

### Wallet Options

1. **🔗 WalletConnect**
   - QR code scanning
   - Mobile wallet support
   - Blue "QR CODE" tag

2. **🦊 MetaMask**
   - Auto-detection
   - Green "INSTALLED" tag when available
   - Red "NOT INSTALLED" when missing

3. **🛡️ Trust Wallet**
   - Direct connection
   - Installation detection
   - Status indicators

4. **💳 All Wallets**
   - Universal fallback
   - "460+" wallets supported
   - Gray tag indicator

### UI Elements

- **Dark Theme** - `#1a1b23` background with `#252631` cards
- **Gradient Buttons** - Purple to pink gradients
- **Status Indicators** - Real-time connection status
- **Animations** - Smooth hover and loading effects
- **CSS Modules** - Scoped styling to prevent conflicts

## ⚙️ Configuration

### Wallet Connect Project ID

Update your Reown (WalletConnect) project ID in `.env.local`:

```bash
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=3203e3196ca7682fb5394a53b725d357
```

### Network Configuration

The app is pre-configured for Monad Testnet in `src/app/providers.tsx`:

```tsx
const monadTestnet = {
  id: 10143,
  name: 'Monad Testnet',
  rpcUrls: {
    default: { http: ['https://testnet-rpc.monad.xyz'] },
  },
  // ...
}
```

## 🎯 Usage Examples

### Basic Usage

```tsx
'use client'

import { useState } from 'react'
import CustomWalletModal from '@/components/CustomWalletModal'

export default function MyPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <div>
      <button onClick={() => setIsModalOpen(true)}>
        Connect Wallet
      </button>
      
      <CustomWalletModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  )
}
```

### With Connection Status

```tsx
'use client'

import { useState } from 'react'
import { useAccount } from 'wagmi'
import CustomWalletModal from '@/components/CustomWalletModal'

export default function WalletButton() {
  const { address, isConnected } = useAccount()
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <div>
      {isConnected ? (
        <div>Connected: {address?.slice(0, 6)}...{address?.slice(-4)}</div>
      ) : (
        <button onClick={() => setIsModalOpen(true)}>
          Connect Wallet
        </button>
      )}
      
      <CustomWalletModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  )
}
```

## 🔧 Customization

### Styling with CSS Modules

Modify `src/components/CustomWalletModal.module.css`:

```css
.walletModal {
  background: #1a1b23; /* Change modal background */
  border-radius: 16px;   /* Adjust border radius */
}

.walletOption:hover {
  border-color: #9333ea; /* Change hover color */
}
```

### Adding New Wallets

Update `src/components/CustomWalletModal.tsx`:

```tsx
const walletOptions = [
  // Add new wallet
  {
    id: 'newwallet',
    name: 'New Wallet',
    icon: '🔥',
    tag: 'NEW',
    tagColor: '#ff6b6b',
    connector: newWalletConnector(),
    available: checkNewWalletAvailability(),
  },
  // ... existing wallets
]
```

## 🌐 Next.js Specific Features

### Server-Side Rendering

The app uses Next.js App Router with proper SSR support:

- **Client Components** - Marked with `'use client'`
- **Provider Pattern** - Wagmi providers in separate component
- **Environment Variables** - Secure handling with `NEXT_PUBLIC_` prefix

### Performance Optimizations

- **CSS Modules** - Scoped styles with automatic optimization
- **Tree Shaking** - Only used wallet connectors are bundled
- **Code Splitting** - Automatic route-based code splitting

## 📱 Mobile Support

The modal is fully responsive with:

- Touch-friendly button sizes
- Mobile-optimized spacing
- Responsive grid layouts
- Proper viewport handling
- iOS/Android wallet deep linking

## 🐛 Troubleshooting

### Common Issues

1. **Hydration Errors**
   - Ensure wallet detection runs only on client-side
   - Use `useEffect` for browser-specific code

2. **CSS Module Issues**
   - Check import paths use `.module.css` extension
   - Verify class names match CSS file

3. **Environment Variables**
   - Ensure variables start with `NEXT_PUBLIC_`
   - Restart dev server after changes

### Debug Mode

Enable debug logging:

```tsx
// Add to CustomWalletModal.tsx
useEffect(() => {
  console.log('Wallet detection:', {
    hasMetaMask,
    hasTrustWallet,
    ethereum: typeof window !== 'undefined' ? window.ethereum : null
  })
}, [hasMetaMask, hasTrustWallet])
```

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Deploy to Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### Environment Variables for Production

Set in your deployment platform:

```bash
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id
NEXT_PUBLIC_MONAD_RPC_URL=https://testnet-rpc.monad.xyz
```

## 📄 Scripts

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
npm run type-check # Run TypeScript check
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Built with ❤️ for the Werant ecosystem using Next.js 14**
