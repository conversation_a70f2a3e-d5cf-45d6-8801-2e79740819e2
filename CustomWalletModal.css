/* Custom Wallet Modal Styles - Dark Theme */
.wallet-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.2s ease-out;
}

.wallet-modal {
  background: #1a1b23;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  margin: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid #2d2e36;
  animation: slideUp 0.3s ease-out;
}

.wallet-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #2d2e36;
}

.wallet-modal-header h2 {
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #8b8ca7;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #2d2e36;
  color: #ffffff;
}

.wallet-options {
  padding: 16px 24px 24px 24px;
}

.wallet-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #252631;
  border-radius: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.wallet-option:hover:not(.disabled) {
  background: #2d2e36;
  border-color: #9333ea;
  transform: translateY(-1px);
}

.wallet-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.wallet-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wallet-icon {
  font-size: 24px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.wallet-name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

.wallet-tag {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.connected-state {
  padding: 24px;
  text-align: center;
}

.connected-info {
  background: #252631;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #28a745;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #28a745;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.wallet-address {
  color: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 600;
}

.disconnect-button {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.disconnect-button:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
  transform: translateY(-1px);
}

.loading-state {
  padding: 24px;
  text-align: center;
  color: #8b8ca7;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #2d2e36;
  border-top: 2px solid #9333ea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px auto;
}

.modal-footer {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #2d2e36;
}

.terms-text {
  color: #8b8ca7;
  font-size: 12px;
  text-align: center;
  margin: 0;
  line-height: 1.4;
}

.terms-link {
  color: #9333ea;
  text-decoration: none;
  transition: color 0.2s ease;
}

.terms-link:hover {
  color: #a855f7;
  text-decoration: underline;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .wallet-modal {
    margin: 10px;
    max-width: none;
  }
  
  .wallet-modal-header {
    padding: 20px 20px 12px 20px;
  }
  
  .wallet-options {
    padding: 12px 20px 20px 20px;
  }
  
  .wallet-option {
    padding: 14px;
  }
  
  .wallet-name {
    font-size: 15px;
  }
  
  .wallet-tag {
    font-size: 10px;
    padding: 3px 6px;
  }
}
