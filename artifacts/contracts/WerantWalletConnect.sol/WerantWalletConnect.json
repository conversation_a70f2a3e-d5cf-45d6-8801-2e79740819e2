{"_format": "hh-sol-artifact-1", "contractName": "WerantWalletConnect", "sourceName": "contracts/WerantWalletConnect.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FeeWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "connectionId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feePaid", "type": "uint256"}], "name": "WalletConnected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "WalletDisconnected", "type": "event"}, {"inputs": [], "name": "CONNECTION_FEE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "connectWallet", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "connectedWallets", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "connectionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "connectionTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disconnectWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}], "name": "getConnectionDetails", "outputs": [{"internalType": "bool", "name": "isConnected", "type": "bool"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalConnections", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}], "name": "isWalletConnected", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "new<PERSON>ee", "type": "uint256"}], "name": "updateConnectionFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}