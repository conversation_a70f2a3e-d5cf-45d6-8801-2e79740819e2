{"id": "58ee998498958f16af9b2f7b76a14333", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.20", "solcLongVersion": "0.8.20+commit.a1b79de6", "input": {"language": "Solidity", "sources": {"@openzeppelin/contracts/access/Ownable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {Context} from \"../utils/Context.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract Ownable is Context {\n    address private _owner;\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    constructor(address initialOwner) {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        return _owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        address oldOwner = _owner;\n        _owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts/utils/Context.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract Context {\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Contract module that helps prevent reentrant calls to a function.\n *\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n * available, which can be applied to functions to make sure there are no nested\n * (reentrant) calls to them.\n *\n * Note that because there is a single `nonReentrant` guard, functions marked as\n * `nonReentrant` may not call one another. This can be worked around by making\n * those functions `private`, and then adding `external` `nonReentrant` entry\n * points to them.\n *\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n * consider using {ReentrancyGuardTransient} instead.\n *\n * TIP: If you would like to learn more about reentrancy and alternative ways\n * to protect against it, check out our blog post\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\n */\nabstract contract ReentrancyGuard {\n    // Booleans are more expensive than uint256 or any type that takes up a full\n    // word because each write operation emits an extra SLOAD to first read the\n    // slot's contents, replace the bits taken up by the boolean, and then write\n    // back. This is the compiler's defense against contract upgrades and\n    // pointer aliasing, and it cannot be disabled.\n\n    // The values being non-zero value makes deployment a bit more expensive,\n    // but in exchange the refund on every call to nonReentrant will be lower in\n    // amount. Since refunds are capped to a percentage of the total\n    // transaction's gas, it is best to keep them low in cases like this one, to\n    // increase the likelihood of the full refund coming into effect.\n    uint256 private constant NOT_ENTERED = 1;\n    uint256 private constant ENTERED = 2;\n\n    uint256 private _status;\n\n    /**\n     * @dev Unauthorized reentrant call.\n     */\n    error ReentrancyGuardReentrantCall();\n\n    constructor() {\n        _status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Prevents a contract from calling itself, directly or indirectly.\n     * Calling a `nonReentrant` function from another `nonReentrant`\n     * function is not supported. It is possible to prevent this from happening\n     * by making the `nonReentrant` function external, and making it call a\n     * `private` function that does the actual work.\n     */\n    modifier nonReentrant() {\n        _nonReentrantBefore();\n        _;\n        _nonReentrantAfter();\n    }\n\n    function _nonReentrantBefore() private {\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\n        if (_status == ENTERED) {\n            revert ReentrancyGuardReentrantCall();\n        }\n\n        // Any calls to nonReentrant after this point will fail\n        _status = ENTERED;\n    }\n\n    function _nonReentrantAfter() private {\n        // By storing the original value once again, a refund is triggered (see\n        // https://eips.ethereum.org/EIPS/eip-2200)\n        _status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n     * `nonReentrant` function in the call stack.\n     */\n    function _reentrancyGuardEntered() internal view returns (bool) {\n        return _status == ENTERED;\n    }\n}\n"}, "contracts/WerantWalletConnect.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.20;\n\nimport \"@openzeppelin/contracts/access/Ownable.sol\";\nimport \"@openzeppelin/contracts/utils/ReentrancyGuard.sol\";\n\n/**\n * @title WerantWalletConnect\n * @dev Smart contract for handling wallet connections with gas fee of 0.1 MON\n */\ncontract WerantWalletConnect is Ownable, ReentrancyGuard {\n    // Constants\n    uint256 public constant CONNECTION_FEE = 0.1 ether; // 0.1 MON\n\n    // State variables\n    uint256 private _connectionIds;\n    mapping(address => bool) public connectedWallets;\n    mapping(address => uint256) public connectionTimestamp;\n    mapping(address => uint256) public connectionId;\n    \n    // Events\n    event WalletConnected(\n        address indexed wallet,\n        uint256 indexed connectionId,\n        uint256 timestamp,\n        uint256 feePaid\n    );\n    \n    event WalletDisconnected(\n        address indexed wallet,\n        uint256 timestamp\n    );\n    \n    event FeeWithdrawn(\n        address indexed owner,\n        uint256 amount\n    );\n    \n    // Modifiers\n    modifier onlyConnectedWallet() {\n        require(connectedWallets[msg.sender], \"Wallet not connected\");\n        _;\n    }\n    \n    modifier notConnected() {\n        require(!connectedWallets[msg.sender], \"Wallet already connected\");\n        _;\n    }\n    \n    constructor() Ownable(msg.sender) {}\n    \n    /**\n     * @dev Connect wallet by paying the connection fee\n     */\n    function connectWallet() external payable nonReentrant notConnected {\n        require(msg.value >= CONNECTION_FEE, \"Insufficient connection fee\");\n        \n        _connectionIds++;\n        uint256 newConnectionId = _connectionIds;\n        \n        connectedWallets[msg.sender] = true;\n        connectionTimestamp[msg.sender] = block.timestamp;\n        connectionId[msg.sender] = newConnectionId;\n        \n        emit WalletConnected(\n            msg.sender,\n            newConnectionId,\n            block.timestamp,\n            msg.value\n        );\n        \n        // Refund excess payment\n        if (msg.value > CONNECTION_FEE) {\n            payable(msg.sender).transfer(msg.value - CONNECTION_FEE);\n        }\n    }\n    \n    /**\n     * @dev Disconnect wallet\n     */\n    function disconnectWallet() external onlyConnectedWallet {\n        connectedWallets[msg.sender] = false;\n        connectionTimestamp[msg.sender] = 0;\n        connectionId[msg.sender] = 0;\n        \n        emit WalletDisconnected(msg.sender, block.timestamp);\n    }\n    \n    /**\n     * @dev Check if wallet is connected\n     */\n    function isWalletConnected(address wallet) external view returns (bool) {\n        return connectedWallets[wallet];\n    }\n    \n    /**\n     * @dev Get wallet connection details\n     */\n    function getConnectionDetails(address wallet) \n        external \n        view \n        returns (\n            bool isConnected,\n            uint256 timestamp,\n            uint256 id\n        ) \n    {\n        return (\n            connectedWallets[wallet],\n            connectionTimestamp[wallet],\n            connectionId[wallet]\n        );\n    }\n    \n    /**\n     * @dev Get total connected wallets count\n     */\n    function getTotalConnections() external view returns (uint256) {\n        return _connectionIds;\n    }\n    \n    /**\n     * @dev Get contract balance\n     */\n    function getContractBalance() external view returns (uint256) {\n        return address(this).balance;\n    }\n    \n    /**\n     * @dev Withdraw collected fees (only owner)\n     */\n    function withdrawFees() external onlyOwner nonReentrant {\n        uint256 balance = address(this).balance;\n        require(balance > 0, \"No fees to withdraw\");\n        \n        payable(owner()).transfer(balance);\n        \n        emit FeeWithdrawn(owner(), balance);\n    }\n    \n    /**\n     * @dev Emergency withdraw (only owner)\n     */\n    function emergencyWithdraw() external onlyOwner {\n        payable(owner()).transfer(address(this).balance);\n    }\n    \n    /**\n     * @dev Update connection fee (only owner)\n     */\n    function updateConnectionFee(uint256 newFee) external onlyOwner {\n        // Note: This would require updating the constant, \n        // so this function is for future upgradeable versions\n        revert(\"Connection fee is fixed at 0.25 MON\");\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"errors": [{"component": "general", "errorCode": "5667", "formattedMessage": "Warning: Unused function parameter. Remove or comment out the variable name to silence this warning.\n   --> contracts/WerantWalletConnect.sol:151:34:\n    |\n151 |     function updateConnectionFee(uint256 newFee) external onlyOwner {\n    |                                  ^^^^^^^^^^^^^^\n\n", "message": "Unused function parameter. Remove or comment out the variable name to silence this warning.", "severity": "warning", "sourceLocation": {"end": 4048, "file": "contracts/WerantWalletConnect.sol", "start": 4034}, "type": "Warning"}, {"component": "general", "errorCode": "2018", "formattedMessage": "Warning: Function state mutability can be restricted to view\n   --> contracts/WerantWalletConnect.sol:151:5:\n    |\n151 |     function updateConnectionFee(uint256 newFee) external onlyOwner {\n    |     ^ (Relevant source part starts here and spans across multiple lines).\n\n", "message": "Function state mutability can be restricted to view", "severity": "warning", "sourceLocation": {"end": 4254, "file": "contracts/WerantWalletConnect.sol", "start": 4005}, "type": "Warning"}], "sources": {"@openzeppelin/contracts/access/Ownable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "exportedSymbols": {"Context": [177], "Ownable": [147]}, "id": 148, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:0"}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../utils/Context.sol", "id": 3, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 148, "sourceUnit": 178, "src": "128:45:0", "symbolAliases": [{"foreign": {"id": 2, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 177, "src": "136:7:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 5, "name": "Context", "nameLocations": ["692:7:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 177, "src": "692:7:0"}, "id": 6, "nodeType": "InheritanceSpecifier", "src": "692:7:0"}], "canonicalName": "Ownable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 4, "nodeType": "StructuredDocumentation", "src": "175:487:0", "text": " @dev Contract module which provides a basic access control mechanism, where\n there is an account (an owner) that can be granted exclusive access to\n specific functions.\n The initial owner is set to the address provided by the deployer. This can\n later be changed with {transferOwnership}.\n This module is used through inheritance. It will make available the modifier\n `onlyOwner`, which can be applied to your functions to restrict their use to\n the owner."}, "fullyImplemented": true, "id": 147, "linearizedBaseContracts": [147, 177], "name": "Ownable", "nameLocation": "681:7:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 8, "mutability": "mutable", "name": "_owner", "nameLocation": "722:6:0", "nodeType": "VariableDeclaration", "scope": 147, "src": "706:22:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7, "name": "address", "nodeType": "ElementaryTypeName", "src": "706:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"documentation": {"id": 9, "nodeType": "StructuredDocumentation", "src": "735:85:0", "text": " @dev The caller account is not authorized to perform an operation."}, "errorSelector": "118cdaa7", "id": 13, "name": "OwnableUnauthorizedAccount", "nameLocation": "831:26:0", "nodeType": "ErrorDefinition", "parameters": {"id": 12, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11, "mutability": "mutable", "name": "account", "nameLocation": "866:7:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "858:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10, "name": "address", "nodeType": "ElementaryTypeName", "src": "858:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "857:17:0"}, "src": "825:50:0"}, {"documentation": {"id": 14, "nodeType": "StructuredDocumentation", "src": "881:82:0", "text": " @dev The owner is not a valid owner account. (eg. `address(0)`)"}, "errorSelector": "1e4fbdf7", "id": 18, "name": "OwnableInvalidOwner", "nameLocation": "974:19:0", "nodeType": "ErrorDefinition", "parameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 16, "mutability": "mutable", "name": "owner", "nameLocation": "1002:5:0", "nodeType": "VariableDeclaration", "scope": 18, "src": "994:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 15, "name": "address", "nodeType": "ElementaryTypeName", "src": "994:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "993:15:0"}, "src": "968:41:0"}, {"anonymous": false, "eventSelector": "8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "id": 24, "name": "OwnershipTransferred", "nameLocation": "1021:20:0", "nodeType": "EventDefinition", "parameters": {"id": 23, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "indexed": true, "mutability": "mutable", "name": "previousOwner", "nameLocation": "1058:13:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1042:29:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 19, "name": "address", "nodeType": "ElementaryTypeName", "src": "1042:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 22, "indexed": true, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "1089:8:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1073:24:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 21, "name": "address", "nodeType": "ElementaryTypeName", "src": "1073:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1041:57:0"}, "src": "1015:84:0"}, {"body": {"id": 49, "nodeType": "Block", "src": "1259:153:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 35, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 30, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1273:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 33, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1297:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 32, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1289:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 31, "name": "address", "nodeType": "ElementaryTypeName", "src": "1289:7:0", "typeDescriptions": {}}}, "id": 34, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1289:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1273:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 44, "nodeType": "IfStatement", "src": "1269:95:0", "trueBody": {"id": 43, "nodeType": "Block", "src": "1301:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 39, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1350:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1342:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 37, "name": "address", "nodeType": "ElementaryTypeName", "src": "1342:7:0", "typeDescriptions": {}}}, "id": 40, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1342:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 36, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "1322:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 41, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1322:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42, "nodeType": "RevertStatement", "src": "1315:38:0"}]}}, {"expression": {"arguments": [{"id": 46, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1392:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 45, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "1373:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 47, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1373:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 48, "nodeType": "ExpressionStatement", "src": "1373:32:0"}]}, "documentation": {"id": 25, "nodeType": "StructuredDocumentation", "src": "1105:115:0", "text": " @dev Initializes the contract setting the address provided by the deployer as the initial owner."}, "id": 50, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 28, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 27, "mutability": "mutable", "name": "initialOwner", "nameLocation": "1245:12:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "1237:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26, "name": "address", "nodeType": "ElementaryTypeName", "src": "1237:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1236:22:0"}, "returnParameters": {"id": 29, "nodeType": "ParameterList", "parameters": [], "src": "1259:0:0"}, "scope": 147, "src": "1225:187:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 57, "nodeType": "Block", "src": "1521:41:0", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 53, "name": "_checkOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 84, "src": "1531:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1531:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 55, "nodeType": "ExpressionStatement", "src": "1531:13:0"}, {"id": 56, "nodeType": "PlaceholderStatement", "src": "1554:1:0"}]}, "documentation": {"id": 51, "nodeType": "StructuredDocumentation", "src": "1418:77:0", "text": " @dev Throws if called by any account other than the owner."}, "id": 58, "name": "only<PERSON><PERSON>er", "nameLocation": "1509:9:0", "nodeType": "ModifierDefinition", "parameters": {"id": 52, "nodeType": "ParameterList", "parameters": [], "src": "1518:2:0"}, "src": "1500:62:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 66, "nodeType": "Block", "src": "1693:30:0", "statements": [{"expression": {"id": 64, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "1710:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 63, "id": 65, "nodeType": "Return", "src": "1703:13:0"}]}, "documentation": {"id": 59, "nodeType": "StructuredDocumentation", "src": "1568:65:0", "text": " @dev Returns the address of the current owner."}, "functionSelector": "8da5cb5b", "id": 67, "implemented": true, "kind": "function", "modifiers": [], "name": "owner", "nameLocation": "1647:5:0", "nodeType": "FunctionDefinition", "parameters": {"id": 60, "nodeType": "ParameterList", "parameters": [], "src": "1652:2:0"}, "returnParameters": {"id": 63, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 62, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 67, "src": "1684:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 61, "name": "address", "nodeType": "ElementaryTypeName", "src": "1684:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1683:9:0"}, "scope": 147, "src": "1638:85:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 83, "nodeType": "Block", "src": "1841:117:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 75, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 71, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "1855:5:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 72, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1855:7:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 73, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 159, "src": "1866:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 74, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1866:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1855:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 82, "nodeType": "IfStatement", "src": "1851:101:0", "trueBody": {"id": 81, "nodeType": "Block", "src": "1880:72:0", "statements": [{"errorCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 77, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 159, "src": "1928:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 78, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1928:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 76, "name": "OwnableUnauthorizedAccount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "1901:26:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 79, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1901:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 80, "nodeType": "RevertStatement", "src": "1894:47:0"}]}}]}, "documentation": {"id": 68, "nodeType": "StructuredDocumentation", "src": "1729:62:0", "text": " @dev Throws if the sender is not the owner."}, "id": 84, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkOwner", "nameLocation": "1805:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 69, "nodeType": "ParameterList", "parameters": [], "src": "1816:2:0"}, "returnParameters": {"id": 70, "nodeType": "ParameterList", "parameters": [], "src": "1841:0:0"}, "scope": 147, "src": "1796:162:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 97, "nodeType": "Block", "src": "2347:47:0", "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "30", "id": 93, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2384:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 92, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2376:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 91, "name": "address", "nodeType": "ElementaryTypeName", "src": "2376:7:0", "typeDescriptions": {}}}, "id": 94, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2376:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 90, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2357:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 95, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2357:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 96, "nodeType": "ExpressionStatement", "src": "2357:30:0"}]}, "documentation": {"id": 85, "nodeType": "StructuredDocumentation", "src": "1964:324:0", "text": " @dev Leaves the contract without owner. It will not be possible to call\n `onlyOwner` functions. Can only be called by the current owner.\n NOTE: Renouncing ownership will leave the contract without an owner,\n thereby disabling any functionality that is only available to the owner."}, "functionSelector": "715018a6", "id": 98, "implemented": true, "kind": "function", "modifiers": [{"id": 88, "kind": "modifierInvocation", "modifierName": {"id": 87, "name": "only<PERSON><PERSON>er", "nameLocations": ["2337:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2337:9:0"}, "nodeType": "ModifierInvocation", "src": "2337:9:0"}], "name": "renounceOwnership", "nameLocation": "2302:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 86, "nodeType": "ParameterList", "parameters": [], "src": "2319:2:0"}, "returnParameters": {"id": 89, "nodeType": "ParameterList", "parameters": [], "src": "2347:0:0"}, "scope": 147, "src": "2293:101:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 125, "nodeType": "Block", "src": "2613:145:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 106, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2627:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 109, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2647:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 108, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2639:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 107, "name": "address", "nodeType": "ElementaryTypeName", "src": "2639:7:0", "typeDescriptions": {}}}, "id": 110, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2639:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2627:22:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 120, "nodeType": "IfStatement", "src": "2623:91:0", "trueBody": {"id": 119, "nodeType": "Block", "src": "2651:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 115, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2700:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 114, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2692:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 113, "name": "address", "nodeType": "ElementaryTypeName", "src": "2692:7:0", "typeDescriptions": {}}}, "id": 116, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2692:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 112, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "2672:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2672:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 118, "nodeType": "RevertStatement", "src": "2665:38:0"}]}}, {"expression": {"arguments": [{"id": 122, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2742:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 121, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2723:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 123, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2723:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 124, "nodeType": "ExpressionStatement", "src": "2723:28:0"}]}, "documentation": {"id": 99, "nodeType": "StructuredDocumentation", "src": "2400:138:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Can only be called by the current owner."}, "functionSelector": "f2fde38b", "id": 126, "implemented": true, "kind": "function", "modifiers": [{"id": 104, "kind": "modifierInvocation", "modifierName": {"id": 103, "name": "only<PERSON><PERSON>er", "nameLocations": ["2603:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2603:9:0"}, "nodeType": "ModifierInvocation", "src": "2603:9:0"}], "name": "transferOwnership", "nameLocation": "2552:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 101, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2578:8:0", "nodeType": "VariableDeclaration", "scope": 126, "src": "2570:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 100, "name": "address", "nodeType": "ElementaryTypeName", "src": "2570:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2569:18:0"}, "returnParameters": {"id": 105, "nodeType": "ParameterList", "parameters": [], "src": "2613:0:0"}, "scope": 147, "src": "2543:215:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 145, "nodeType": "Block", "src": "2975:124:0", "statements": [{"assignments": [133], "declarations": [{"constant": false, "id": 133, "mutability": "mutable", "name": "old<PERSON>wner", "nameLocation": "2993:8:0", "nodeType": "VariableDeclaration", "scope": 145, "src": "2985:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 132, "name": "address", "nodeType": "ElementaryTypeName", "src": "2985:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 135, "initialValue": {"id": 134, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3004:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "2985:25:0"}, {"expression": {"id": 138, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 136, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3020:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 137, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3029:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3020:17:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 139, "nodeType": "ExpressionStatement", "src": "3020:17:0"}, {"eventCall": {"arguments": [{"id": 141, "name": "old<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 133, "src": "3073:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 142, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3083:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 140, "name": "OwnershipTransferred", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 24, "src": "3052:20:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$returns$__$", "typeString": "function (address,address)"}}, "id": 143, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3052:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 144, "nodeType": "EmitStatement", "src": "3047:45:0"}]}, "documentation": {"id": 127, "nodeType": "StructuredDocumentation", "src": "2764:143:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Internal function without access restriction."}, "id": 146, "implemented": true, "kind": "function", "modifiers": [], "name": "_transferOwnership", "nameLocation": "2921:18:0", "nodeType": "FunctionDefinition", "parameters": {"id": 130, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 129, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2948:8:0", "nodeType": "VariableDeclaration", "scope": 146, "src": "2940:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 128, "name": "address", "nodeType": "ElementaryTypeName", "src": "2940:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2939:18:0"}, "returnParameters": {"id": 131, "nodeType": "ParameterList", "parameters": [], "src": "2975:0:0"}, "scope": 147, "src": "2912:187:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 148, "src": "663:2438:0", "usedErrors": [13, 18], "usedEvents": [24]}], "src": "102:3000:0"}, "id": 0}, "@openzeppelin/contracts/utils/Context.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "exportedSymbols": {"Context": [177]}, "id": 178, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 149, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "101:24:1"}, {"abstract": true, "baseContracts": [], "canonicalName": "Context", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 150, "nodeType": "StructuredDocumentation", "src": "127:496:1", "text": " @dev Provides information about the current execution context, including the\n sender of the transaction and its data. While these are generally available\n via msg.sender and msg.data, they should not be accessed in such a direct\n manner, since when dealing with meta-transactions the account sending and\n paying for execution may not be the actual sender (as far as an application\n is concerned).\n This contract is only required for intermediate, library-like contracts."}, "fullyImplemented": true, "id": 177, "linearizedBaseContracts": [177], "name": "Context", "nameLocation": "642:7:1", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 158, "nodeType": "Block", "src": "718:34:1", "statements": [{"expression": {"expression": {"id": 155, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "735:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 156, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "739:6:1", "memberName": "sender", "nodeType": "MemberAccess", "src": "735:10:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 154, "id": 157, "nodeType": "Return", "src": "728:17:1"}]}, "id": 159, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgSender", "nameLocation": "665:10:1", "nodeType": "FunctionDefinition", "parameters": {"id": 151, "nodeType": "ParameterList", "parameters": [], "src": "675:2:1"}, "returnParameters": {"id": 154, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 153, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 159, "src": "709:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 152, "name": "address", "nodeType": "ElementaryTypeName", "src": "709:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "708:9:1"}, "scope": 177, "src": "656:96:1", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 167, "nodeType": "Block", "src": "825:32:1", "statements": [{"expression": {"expression": {"id": 164, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "842:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 165, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "846:4:1", "memberName": "data", "nodeType": "MemberAccess", "src": "842:8:1", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, "functionReturnParameters": 163, "id": 166, "nodeType": "Return", "src": "835:15:1"}]}, "id": 168, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgData", "nameLocation": "767:8:1", "nodeType": "FunctionDefinition", "parameters": {"id": 160, "nodeType": "ParameterList", "parameters": [], "src": "775:2:1"}, "returnParameters": {"id": 163, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 162, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 168, "src": "809:14:1", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 161, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "809:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "808:16:1"}, "scope": 177, "src": "758:99:1", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 175, "nodeType": "Block", "src": "935:25:1", "statements": [{"expression": {"hexValue": "30", "id": 173, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "952:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 172, "id": 174, "nodeType": "Return", "src": "945:8:1"}]}, "id": 176, "implemented": true, "kind": "function", "modifiers": [], "name": "_contextSuffixLength", "nameLocation": "872:20:1", "nodeType": "FunctionDefinition", "parameters": {"id": 169, "nodeType": "ParameterList", "parameters": [], "src": "892:2:1"}, "returnParameters": {"id": 172, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 171, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 176, "src": "926:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 170, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "926:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "925:9:1"}, "scope": 177, "src": "863:97:1", "stateMutability": "view", "virtual": true, "visibility": "internal"}], "scope": 178, "src": "624:338:1", "usedErrors": [], "usedEvents": []}], "src": "101:862:1"}, "id": 1}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "exportedSymbols": {"ReentrancyGuard": [246]}, "id": 247, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 179, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "109:24:2"}, {"abstract": true, "baseContracts": [], "canonicalName": "Reentrancy<PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 180, "nodeType": "StructuredDocumentation", "src": "135:894:2", "text": " @dev Contract module that helps prevent reentrant calls to a function.\n Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n available, which can be applied to functions to make sure there are no nested\n (reentrant) calls to them.\n Note that because there is a single `nonReentrant` guard, functions marked as\n `nonReentrant` may not call one another. This can be worked around by making\n those functions `private`, and then adding `external` `nonReentrant` entry\n points to them.\n TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n consider using {ReentrancyGuardTransient} instead.\n TIP: If you would like to learn more about reentrancy and alternative ways\n to protect against it, check out our blog post\n https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul]."}, "fullyImplemented": true, "id": 246, "linearizedBaseContracts": [246], "name": "Reentrancy<PERSON><PERSON>", "nameLocation": "1048:15:2", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 183, "mutability": "constant", "name": "NOT_ENTERED", "nameLocation": "1843:11:2", "nodeType": "VariableDeclaration", "scope": 246, "src": "1818:40:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 181, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1818:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31", "id": 182, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1857:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "visibility": "private"}, {"constant": true, "id": 186, "mutability": "constant", "name": "ENTERED", "nameLocation": "1889:7:2", "nodeType": "VariableDeclaration", "scope": 246, "src": "1864:36:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 184, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1864:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "32", "id": 185, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1899:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "visibility": "private"}, {"constant": false, "id": 188, "mutability": "mutable", "name": "_status", "nameLocation": "1923:7:2", "nodeType": "VariableDeclaration", "scope": 246, "src": "1907:23:2", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 187, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1907:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"documentation": {"id": 189, "nodeType": "StructuredDocumentation", "src": "1937:52:2", "text": " @dev Unauthorized reentrant call."}, "errorSelector": "3ee5aeb5", "id": 191, "name": "ReentrancyGuardReentrantCall", "nameLocation": "2000:28:2", "nodeType": "ErrorDefinition", "parameters": {"id": 190, "nodeType": "ParameterList", "parameters": [], "src": "2028:2:2"}, "src": "1994:37:2"}, {"body": {"id": 198, "nodeType": "Block", "src": "2051:38:2", "statements": [{"expression": {"id": 196, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 194, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 188, "src": "2061:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 195, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 183, "src": "2071:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2061:21:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 197, "nodeType": "ExpressionStatement", "src": "2061:21:2"}]}, "id": 199, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 192, "nodeType": "ParameterList", "parameters": [], "src": "2048:2:2"}, "returnParameters": {"id": 193, "nodeType": "ParameterList", "parameters": [], "src": "2051:0:2"}, "scope": 246, "src": "2037:52:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 209, "nodeType": "Block", "src": "2490:79:2", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 202, "name": "_nonReentrantBefore", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 226, "src": "2500:19:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 203, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2500:21:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 204, "nodeType": "ExpressionStatement", "src": "2500:21:2"}, {"id": 205, "nodeType": "PlaceholderStatement", "src": "2531:1:2"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 206, "name": "_nonReentrantAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 234, "src": "2542:18:2", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 207, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2542:20:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 208, "nodeType": "ExpressionStatement", "src": "2542:20:2"}]}, "documentation": {"id": 200, "nodeType": "StructuredDocumentation", "src": "2095:366:2", "text": " @dev Prevents a contract from calling itself, directly or indirectly.\n Calling a `nonReentrant` function from another `nonReentrant`\n function is not supported. It is possible to prevent this from happening\n by making the `nonReentrant` function external, and making it call a\n `private` function that does the actual work."}, "id": 210, "name": "nonReentrant", "nameLocation": "2475:12:2", "nodeType": "ModifierDefinition", "parameters": {"id": 201, "nodeType": "ParameterList", "parameters": [], "src": "2487:2:2"}, "src": "2466:103:2", "virtual": false, "visibility": "internal"}, {"body": {"id": 225, "nodeType": "Block", "src": "2614:268:2", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 215, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 213, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 188, "src": "2702:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 214, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 186, "src": "2713:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2702:18:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 220, "nodeType": "IfStatement", "src": "2698:86:2", "trueBody": {"id": 219, "nodeType": "Block", "src": "2722:62:2", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 216, "name": "ReentrancyGuardReentrantCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2743:28:2", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 217, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2743:30:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 218, "nodeType": "RevertStatement", "src": "2736:37:2"}]}}, {"expression": {"id": 223, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 221, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 188, "src": "2858:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 222, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 186, "src": "2868:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2858:17:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 224, "nodeType": "ExpressionStatement", "src": "2858:17:2"}]}, "id": 226, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantBefore", "nameLocation": "2584:19:2", "nodeType": "FunctionDefinition", "parameters": {"id": 211, "nodeType": "ParameterList", "parameters": [], "src": "2603:2:2"}, "returnParameters": {"id": 212, "nodeType": "ParameterList", "parameters": [], "src": "2614:0:2"}, "scope": 246, "src": "2575:307:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 233, "nodeType": "Block", "src": "2926:170:2", "statements": [{"expression": {"id": 231, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 229, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 188, "src": "3068:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 230, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 183, "src": "3078:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3068:21:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 232, "nodeType": "ExpressionStatement", "src": "3068:21:2"}]}, "id": 234, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantAfter", "nameLocation": "2897:18:2", "nodeType": "FunctionDefinition", "parameters": {"id": 227, "nodeType": "ParameterList", "parameters": [], "src": "2915:2:2"}, "returnParameters": {"id": 228, "nodeType": "ParameterList", "parameters": [], "src": "2926:0:2"}, "scope": 246, "src": "2888:208:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 244, "nodeType": "Block", "src": "3339:42:2", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 242, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 240, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 188, "src": "3356:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 241, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 186, "src": "3367:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3356:18:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 239, "id": 243, "nodeType": "Return", "src": "3349:25:2"}]}, "documentation": {"id": 235, "nodeType": "StructuredDocumentation", "src": "3102:168:2", "text": " @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n `nonReentrant` function in the call stack."}, "id": 245, "implemented": true, "kind": "function", "modifiers": [], "name": "_reentrancyGuardEntered", "nameLocation": "3284:23:2", "nodeType": "FunctionDefinition", "parameters": {"id": 236, "nodeType": "ParameterList", "parameters": [], "src": "3307:2:2"}, "returnParameters": {"id": 239, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 238, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 245, "src": "3333:4:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 237, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3333:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3332:6:2"}, "scope": 246, "src": "3275:106:2", "stateMutability": "view", "virtual": false, "visibility": "internal"}], "scope": 247, "src": "1030:2353:2", "usedErrors": [191], "usedEvents": []}], "src": "109:3275:2"}, "id": 2}, "contracts/WerantWalletConnect.sol": {"ast": {"absolutePath": "contracts/WerantWalletConnect.sol", "exportedSymbols": {"Context": [177], "Ownable": [147], "ReentrancyGuard": [246], "WerantWalletConnect": [568]}, "id": 569, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 248, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "32:24:3"}, {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "id": 249, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 569, "sourceUnit": 148, "src": "58:52:3", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "file": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "id": 250, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 569, "sourceUnit": 247, "src": "111:59:3", "symbolAliases": [], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 252, "name": "Ownable", "nameLocations": ["321:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "321:7:3"}, "id": 253, "nodeType": "InheritanceSpecifier", "src": "321:7:3"}, {"baseName": {"id": 254, "name": "Reentrancy<PERSON><PERSON>", "nameLocations": ["330:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 246, "src": "330:15:3"}, "id": 255, "nodeType": "InheritanceSpecifier", "src": "330:15:3"}], "canonicalName": "WerantWalletConnect", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 251, "nodeType": "StructuredDocumentation", "src": "172:116:3", "text": " @title WerantWalletConnect\n @dev Smart contract for handling wallet connections with gas fee of 0.1 MON"}, "fullyImplemented": true, "id": 568, "linearizedBaseContracts": [568, 246, 147, 177], "name": "WerantWalletConnect", "nameLocation": "298:19:3", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "functionSelector": "e81cecde", "id": 258, "mutability": "constant", "name": "CONNECTION_FEE", "nameLocation": "393:14:3", "nodeType": "VariableDeclaration", "scope": 568, "src": "369:50:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 256, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "369:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "302e31", "id": 257, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "410:9:3", "subdenomination": "ether", "typeDescriptions": {"typeIdentifier": "t_rational_100000000000000000_by_1", "typeString": "int_const 100000000000000000"}, "value": "0.1"}, "visibility": "public"}, {"constant": false, "id": 260, "mutability": "mutable", "name": "_connectionIds", "nameLocation": "476:14:3", "nodeType": "VariableDeclaration", "scope": 568, "src": "460:30:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 259, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "460:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"constant": false, "functionSelector": "184009b0", "id": 264, "mutability": "mutable", "name": "connectedWallets", "nameLocation": "528:16:3", "nodeType": "VariableDeclaration", "scope": 568, "src": "496:48:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 263, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 261, "name": "address", "nodeType": "ElementaryTypeName", "src": "504:7:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "496:24:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 262, "name": "bool", "nodeType": "ElementaryTypeName", "src": "515:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "ccb2bdbd", "id": 268, "mutability": "mutable", "name": "connectionTimestamp", "nameLocation": "585:19:3", "nodeType": "VariableDeclaration", "scope": 568, "src": "550:54:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 267, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 265, "name": "address", "nodeType": "ElementaryTypeName", "src": "558:7:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "550:27:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 266, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "569:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "80ca5a05", "id": 272, "mutability": "mutable", "name": "connectionId", "nameLocation": "645:12:3", "nodeType": "VariableDeclaration", "scope": 568, "src": "610:47:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 271, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 269, "name": "address", "nodeType": "ElementaryTypeName", "src": "618:7:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "610:27:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 270, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "629:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"anonymous": false, "eventSelector": "ff69af0e8f9dda82bb1c4be69d354048e8e5d59546b9cc2fedc70921bf979374", "id": 282, "name": "WalletConnected", "nameLocation": "688:15:3", "nodeType": "EventDefinition", "parameters": {"id": 281, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 274, "indexed": true, "mutability": "mutable", "name": "wallet", "nameLocation": "729:6:3", "nodeType": "VariableDeclaration", "scope": 282, "src": "713:22:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 273, "name": "address", "nodeType": "ElementaryTypeName", "src": "713:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 276, "indexed": true, "mutability": "mutable", "name": "connectionId", "nameLocation": "761:12:3", "nodeType": "VariableDeclaration", "scope": 282, "src": "745:28:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 275, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "745:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 278, "indexed": false, "mutability": "mutable", "name": "timestamp", "nameLocation": "791:9:3", "nodeType": "VariableDeclaration", "scope": 282, "src": "783:17:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 277, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "783:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 280, "indexed": false, "mutability": "mutable", "name": "feePaid", "nameLocation": "818:7:3", "nodeType": "VariableDeclaration", "scope": 282, "src": "810:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 279, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "810:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "703:128:3"}, "src": "682:150:3"}, {"anonymous": false, "eventSelector": "e6c9ad9c1dcb7029a6dda83ad7fe369d1d509d01674340bb8762d43336ad4256", "id": 288, "name": "WalletDisconnected", "nameLocation": "848:18:3", "nodeType": "EventDefinition", "parameters": {"id": 287, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 284, "indexed": true, "mutability": "mutable", "name": "wallet", "nameLocation": "892:6:3", "nodeType": "VariableDeclaration", "scope": 288, "src": "876:22:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 283, "name": "address", "nodeType": "ElementaryTypeName", "src": "876:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 286, "indexed": false, "mutability": "mutable", "name": "timestamp", "nameLocation": "916:9:3", "nodeType": "VariableDeclaration", "scope": 288, "src": "908:17:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 285, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "908:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "866:65:3"}, "src": "842:90:3"}, {"anonymous": false, "eventSelector": "78473f3f373f7673597f4f0fa5873cb4d375fea6d4339ad6b56dbd411513cb3f", "id": 294, "name": "FeeWithdrawn", "nameLocation": "948:12:3", "nodeType": "EventDefinition", "parameters": {"id": 293, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 290, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "986:5:3", "nodeType": "VariableDeclaration", "scope": 294, "src": "970:21:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 289, "name": "address", "nodeType": "ElementaryTypeName", "src": "970:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 292, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "1009:6:3", "nodeType": "VariableDeclaration", "scope": 294, "src": "1001:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 291, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1001:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "960:61:3"}, "src": "942:80:3"}, {"body": {"id": 305, "nodeType": "Block", "src": "1080:89:3", "statements": [{"expression": {"arguments": [{"baseExpression": {"id": 297, "name": "connectedWallets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "1098:16:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 300, "indexExpression": {"expression": {"id": 298, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1115:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 299, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1119:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1115:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1098:28:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "57616c6c6574206e6f7420636f6e6e6563746564", "id": 301, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1128:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3ec13f7a4e7e85d370265c29e2240388125c7f1d03f18b18312372a2dd5e57df", "typeString": "literal_string \"Wallet not connected\""}, "value": "Wallet not connected"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3ec13f7a4e7e85d370265c29e2240388125c7f1d03f18b18312372a2dd5e57df", "typeString": "literal_string \"Wallet not connected\""}], "id": 296, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1090:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 302, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1090:61:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 303, "nodeType": "ExpressionStatement", "src": "1090:61:3"}, {"id": 304, "nodeType": "PlaceholderStatement", "src": "1161:1:3"}]}, "id": 306, "name": "onlyConnectedWallet", "nameLocation": "1058:19:3", "nodeType": "ModifierDefinition", "parameters": {"id": 295, "nodeType": "ParameterList", "parameters": [], "src": "1077:2:3"}, "src": "1049:120:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 318, "nodeType": "Block", "src": "1203:94:3", "statements": [{"expression": {"arguments": [{"id": 313, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1221:29:3", "subExpression": {"baseExpression": {"id": 309, "name": "connectedWallets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "1222:16:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 312, "indexExpression": {"expression": {"id": 310, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1239:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 311, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1243:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1239:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1222:28:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "57616c6c657420616c726561647920636f6e6e6563746564", "id": 314, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1252:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_084201e288392ddfa08ffbd740f812d6e04ee781b0829fd5c59af755524158a7", "typeString": "literal_string \"Wallet already connected\""}, "value": "Wallet already connected"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_084201e288392ddfa08ffbd740f812d6e04ee781b0829fd5c59af755524158a7", "typeString": "literal_string \"Wallet already connected\""}], "id": 308, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1213:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 315, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1213:66:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 316, "nodeType": "ExpressionStatement", "src": "1213:66:3"}, {"id": 317, "nodeType": "PlaceholderStatement", "src": "1289:1:3"}]}, "id": 319, "name": "notConnected", "nameLocation": "1188:12:3", "nodeType": "ModifierDefinition", "parameters": {"id": 307, "nodeType": "ParameterList", "parameters": [], "src": "1200:2:3"}, "src": "1179:118:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 326, "nodeType": "Block", "src": "1341:2:3", "statements": []}, "id": 327, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"expression": {"id": 322, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1329:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 323, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1333:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1329:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 324, "kind": "baseConstructorSpecifier", "modifierName": {"id": 321, "name": "Ownable", "nameLocations": ["1321:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "1321:7:3"}, "nodeType": "ModifierInvocation", "src": "1321:19:3"}], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 320, "nodeType": "ParameterList", "parameters": [], "src": "1318:2:3"}, "returnParameters": {"id": 325, "nodeType": "ParameterList", "parameters": [], "src": "1341:0:3"}, "scope": 568, "src": "1307:36:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 400, "nodeType": "Block", "src": "1493:652:3", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 339, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 336, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1511:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1515:5:3", "memberName": "value", "nodeType": "MemberAccess", "src": "1511:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 338, "name": "CONNECTION_FEE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 258, "src": "1524:14:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1511:27:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e73756666696369656e7420636f6e6e656374696f6e20666565", "id": 340, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1540:29:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c1d6398b786853beeabe95287b691ff4327a69edd058593c625f082639ecb427", "typeString": "literal_string \"Insufficient connection fee\""}, "value": "Insufficient connection fee"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_c1d6398b786853beeabe95287b691ff4327a69edd058593c625f082639ecb427", "typeString": "literal_string \"Insufficient connection fee\""}], "id": 335, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "1503:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 341, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1503:67:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 342, "nodeType": "ExpressionStatement", "src": "1503:67:3"}, {"expression": {"id": 344, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1589:16:3", "subExpression": {"id": 343, "name": "_connectionIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1589:14:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 345, "nodeType": "ExpressionStatement", "src": "1589:16:3"}, {"assignments": [347], "declarations": [{"constant": false, "id": 347, "mutability": "mutable", "name": "newConnectionId", "nameLocation": "1623:15:3", "nodeType": "VariableDeclaration", "scope": 400, "src": "1615:23:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 346, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1615:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 349, "initialValue": {"id": 348, "name": "_connectionIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1641:14:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1615:40:3"}, {"expression": {"id": 355, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 350, "name": "connectedWallets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "1674:16:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 353, "indexExpression": {"expression": {"id": 351, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1691:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 352, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1695:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1691:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1674:28:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 354, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1705:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "1674:35:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 356, "nodeType": "ExpressionStatement", "src": "1674:35:3"}, {"expression": {"id": 363, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 357, "name": "connectionTimestamp", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "1719:19:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 360, "indexExpression": {"expression": {"id": 358, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1739:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 359, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1743:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1739:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1719:31:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 361, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "1753:5:3", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 362, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1759:9:3", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "1753:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1719:49:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 364, "nodeType": "ExpressionStatement", "src": "1719:49:3"}, {"expression": {"id": 370, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 365, "name": "connectionId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 272, "src": "1778:12:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 368, "indexExpression": {"expression": {"id": 366, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1791:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 367, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1795:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1791:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1778:24:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 369, "name": "newConnectionId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 347, "src": "1805:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1778:42:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 371, "nodeType": "ExpressionStatement", "src": "1778:42:3"}, {"eventCall": {"arguments": [{"expression": {"id": 373, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1873:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 374, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1877:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1873:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 375, "name": "newConnectionId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 347, "src": "1897:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 376, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "1926:5:3", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 377, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1932:9:3", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "1926:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 378, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1955:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 379, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1959:5:3", "memberName": "value", "nodeType": "MemberAccess", "src": "1955:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 372, "name": "WalletConnected", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 282, "src": "1844:15:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (address,uint256,uint256,uint256)"}}, "id": 380, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1844:130:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 381, "nodeType": "EmitStatement", "src": "1839:135:3"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 385, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 382, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2030:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 383, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2034:5:3", "memberName": "value", "nodeType": "MemberAccess", "src": "2030:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 384, "name": "CONNECTION_FEE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 258, "src": "2042:14:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2030:26:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 399, "nodeType": "IfStatement", "src": "2026:113:3", "trueBody": {"id": 398, "nodeType": "Block", "src": "2058:81:3", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 395, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 392, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2101:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 393, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2105:5:3", "memberName": "value", "nodeType": "MemberAccess", "src": "2101:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 394, "name": "CONNECTION_FEE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 258, "src": "2113:14:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2101:26:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"expression": {"id": 388, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2080:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 389, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2084:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "2080:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 387, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2072:8:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_payable_$", "typeString": "type(address payable)"}, "typeName": {"id": 386, "name": "address", "nodeType": "ElementaryTypeName", "src": "2072:8:3", "stateMutability": "payable", "typeDescriptions": {}}}, "id": 390, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2072:19:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 391, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2092:8:3", "memberName": "transfer", "nodeType": "MemberAccess", "src": "2072:28:3", "typeDescriptions": {"typeIdentifier": "t_function_transfer_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 396, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2072:56:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 397, "nodeType": "ExpressionStatement", "src": "2072:56:3"}]}}]}, "documentation": {"id": 328, "nodeType": "StructuredDocumentation", "src": "1353:67:3", "text": " @dev Connect wallet by paying the connection fee"}, "functionSelector": "2d66f063", "id": 401, "implemented": true, "kind": "function", "modifiers": [{"id": 331, "kind": "modifierInvocation", "modifierName": {"id": 330, "name": "nonReentrant", "nameLocations": ["1467:12:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 210, "src": "1467:12:3"}, "nodeType": "ModifierInvocation", "src": "1467:12:3"}, {"id": 333, "kind": "modifierInvocation", "modifierName": {"id": 332, "name": "notConnected", "nameLocations": ["1480:12:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 319, "src": "1480:12:3"}, "nodeType": "ModifierInvocation", "src": "1480:12:3"}], "name": "connectWallet", "nameLocation": "1434:13:3", "nodeType": "FunctionDefinition", "parameters": {"id": 329, "nodeType": "ParameterList", "parameters": [], "src": "1447:2:3"}, "returnParameters": {"id": 334, "nodeType": "ParameterList", "parameters": [], "src": "1493:0:3"}, "scope": 568, "src": "1425:720:3", "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"body": {"id": 435, "nodeType": "Block", "src": "2258:207:3", "statements": [{"expression": {"id": 412, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 407, "name": "connectedWallets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2268:16:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 410, "indexExpression": {"expression": {"id": 408, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2285:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 409, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2289:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "2285:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2268:28:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 411, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2299:5:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "2268:36:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 413, "nodeType": "ExpressionStatement", "src": "2268:36:3"}, {"expression": {"id": 419, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 414, "name": "connectionTimestamp", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "2314:19:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 417, "indexExpression": {"expression": {"id": 415, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2334:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 416, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2338:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "2334:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2314:31:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "30", "id": 418, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2348:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2314:35:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 420, "nodeType": "ExpressionStatement", "src": "2314:35:3"}, {"expression": {"id": 426, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 421, "name": "connectionId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 272, "src": "2359:12:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 424, "indexExpression": {"expression": {"id": 422, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2372:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 423, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2376:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "2372:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2359:24:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "30", "id": 425, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2386:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2359:28:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 427, "nodeType": "ExpressionStatement", "src": "2359:28:3"}, {"eventCall": {"arguments": [{"expression": {"id": 429, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2430:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 430, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2434:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "2430:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 431, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "2442:5:3", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 432, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2448:9:3", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "2442:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 428, "name": "WalletDisconnected", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 288, "src": "2411:18:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 433, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2411:47:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 434, "nodeType": "EmitStatement", "src": "2406:52:3"}]}, "documentation": {"id": 402, "nodeType": "StructuredDocumentation", "src": "2155:41:3", "text": " @dev Disconnect wallet"}, "functionSelector": "63e680ae", "id": 436, "implemented": true, "kind": "function", "modifiers": [{"id": 405, "kind": "modifierInvocation", "modifierName": {"id": 404, "name": "onlyConnectedWallet", "nameLocations": ["2238:19:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 306, "src": "2238:19:3"}, "nodeType": "ModifierInvocation", "src": "2238:19:3"}], "name": "disconnectWallet", "nameLocation": "2210:16:3", "nodeType": "FunctionDefinition", "parameters": {"id": 403, "nodeType": "ParameterList", "parameters": [], "src": "2226:2:3"}, "returnParameters": {"id": 406, "nodeType": "ParameterList", "parameters": [], "src": "2258:0:3"}, "scope": 568, "src": "2201:264:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 448, "nodeType": "Block", "src": "2604:48:3", "statements": [{"expression": {"baseExpression": {"id": 444, "name": "connectedWallets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2621:16:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 446, "indexExpression": {"id": 445, "name": "wallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 439, "src": "2638:6:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2621:24:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 443, "id": 447, "nodeType": "Return", "src": "2614:31:3"}]}, "documentation": {"id": 437, "nodeType": "StructuredDocumentation", "src": "2475:52:3", "text": " @dev Check if wallet is connected"}, "functionSelector": "78961f94", "id": 449, "implemented": true, "kind": "function", "modifiers": [], "name": "isWalletConnected", "nameLocation": "2541:17:3", "nodeType": "FunctionDefinition", "parameters": {"id": 440, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 439, "mutability": "mutable", "name": "wallet", "nameLocation": "2567:6:3", "nodeType": "VariableDeclaration", "scope": 449, "src": "2559:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 438, "name": "address", "nodeType": "ElementaryTypeName", "src": "2559:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2558:16:3"}, "returnParameters": {"id": 443, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 442, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 449, "src": "2598:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 441, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2598:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2597:6:3"}, "scope": 568, "src": "2532:120:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 472, "nodeType": "Block", "src": "2916:147:3", "statements": [{"expression": {"components": [{"baseExpression": {"id": 461, "name": "connectedWallets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2947:16:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 463, "indexExpression": {"id": 462, "name": "wallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 452, "src": "2964:6:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2947:24:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"baseExpression": {"id": 464, "name": "connectionTimestamp", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "2985:19:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 466, "indexExpression": {"id": 465, "name": "wallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 452, "src": "3005:6:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2985:27:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"baseExpression": {"id": 467, "name": "connectionId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 272, "src": "3026:12:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 469, "indexExpression": {"id": 468, "name": "wallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 452, "src": "3039:6:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3026:20:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 470, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2933:123:3", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_uint256_$_t_uint256_$", "typeString": "tuple(bool,uint256,uint256)"}}, "functionReturnParameters": 460, "id": 471, "nodeType": "Return", "src": "2926:130:3"}]}, "documentation": {"id": 450, "nodeType": "StructuredDocumentation", "src": "2662:53:3", "text": " @dev Get wallet connection details"}, "functionSelector": "4bbec111", "id": 473, "implemented": true, "kind": "function", "modifiers": [], "name": "getConnectionDetails", "nameLocation": "2729:20:3", "nodeType": "FunctionDefinition", "parameters": {"id": 453, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 452, "mutability": "mutable", "name": "wallet", "nameLocation": "2758:6:3", "nodeType": "VariableDeclaration", "scope": 473, "src": "2750:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 451, "name": "address", "nodeType": "ElementaryTypeName", "src": "2750:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2749:16:3"}, "returnParameters": {"id": 460, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 455, "mutability": "mutable", "name": "isConnected", "nameLocation": "2834:11:3", "nodeType": "VariableDeclaration", "scope": 473, "src": "2829:16:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 454, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2829:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 457, "mutability": "mutable", "name": "timestamp", "nameLocation": "2867:9:3", "nodeType": "VariableDeclaration", "scope": 473, "src": "2859:17:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 456, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2859:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 459, "mutability": "mutable", "name": "id", "nameLocation": "2898:2:3", "nodeType": "VariableDeclaration", "scope": 473, "src": "2890:10:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 458, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2890:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2815:95:3"}, "scope": 568, "src": "2720:343:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 481, "nodeType": "Block", "src": "3198:38:3", "statements": [{"expression": {"id": 479, "name": "_connectionIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "3215:14:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 478, "id": 480, "nodeType": "Return", "src": "3208:21:3"}]}, "documentation": {"id": 474, "nodeType": "StructuredDocumentation", "src": "3073:57:3", "text": " @dev Get total connected wallets count"}, "functionSelector": "328d1e60", "id": 482, "implemented": true, "kind": "function", "modifiers": [], "name": "getTotalConnections", "nameLocation": "3144:19:3", "nodeType": "FunctionDefinition", "parameters": {"id": 475, "nodeType": "ParameterList", "parameters": [], "src": "3163:2:3"}, "returnParameters": {"id": 478, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 477, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 482, "src": "3189:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 476, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3189:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3188:9:3"}, "scope": 568, "src": "3135:101:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 494, "nodeType": "Block", "src": "3357:45:3", "statements": [{"expression": {"expression": {"arguments": [{"id": 490, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "3382:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_WerantWalletConnect_$568", "typeString": "contract WerantWalletConnect"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_WerantWalletConnect_$568", "typeString": "contract WerantWalletConnect"}], "id": 489, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3374:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 488, "name": "address", "nodeType": "ElementaryTypeName", "src": "3374:7:3", "typeDescriptions": {}}}, "id": 491, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3374:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 492, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3388:7:3", "memberName": "balance", "nodeType": "MemberAccess", "src": "3374:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 487, "id": 493, "nodeType": "Return", "src": "3367:28:3"}]}, "documentation": {"id": 483, "nodeType": "StructuredDocumentation", "src": "3246:44:3", "text": " @dev Get contract balance"}, "functionSelector": "6f9fb98a", "id": 495, "implemented": true, "kind": "function", "modifiers": [], "name": "getContractBalance", "nameLocation": "3304:18:3", "nodeType": "FunctionDefinition", "parameters": {"id": 484, "nodeType": "ParameterList", "parameters": [], "src": "3322:2:3"}, "returnParameters": {"id": 487, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 486, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 495, "src": "3348:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 485, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3348:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3347:9:3"}, "scope": 568, "src": "3295:107:3", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 533, "nodeType": "Block", "src": "3533:216:3", "statements": [{"assignments": [504], "declarations": [{"constant": false, "id": 504, "mutability": "mutable", "name": "balance", "nameLocation": "3551:7:3", "nodeType": "VariableDeclaration", "scope": 533, "src": "3543:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 503, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3543:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 510, "initialValue": {"expression": {"arguments": [{"id": 507, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "3569:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_WerantWalletConnect_$568", "typeString": "contract WerantWalletConnect"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_WerantWalletConnect_$568", "typeString": "contract WerantWalletConnect"}], "id": 506, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3561:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 505, "name": "address", "nodeType": "ElementaryTypeName", "src": "3561:7:3", "typeDescriptions": {}}}, "id": 508, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3561:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 509, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3575:7:3", "memberName": "balance", "nodeType": "MemberAccess", "src": "3561:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3543:39:3"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 514, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 512, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "3600:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 513, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3610:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3600:11:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4e6f206665657320746f207769746864726177", "id": 515, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3613:21:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_524becf2bd3d6f3c74c4c55f46993b5de22a6261a1a6108cc85fed135e73299c", "typeString": "literal_string \"No fees to withdraw\""}, "value": "No fees to withdraw"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_524becf2bd3d6f3c74c4c55f46993b5de22a6261a1a6108cc85fed135e73299c", "typeString": "literal_string \"No fees to withdraw\""}], "id": 511, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3592:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 516, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3592:43:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 517, "nodeType": "ExpressionStatement", "src": "3592:43:3"}, {"expression": {"arguments": [{"id": 524, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "3680:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 520, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "3662:5:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 521, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3662:7:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 519, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3654:8:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_payable_$", "typeString": "type(address payable)"}, "typeName": {"id": 518, "name": "address", "nodeType": "ElementaryTypeName", "src": "3654:8:3", "stateMutability": "payable", "typeDescriptions": {}}}, "id": 522, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3654:16:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 523, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3671:8:3", "memberName": "transfer", "nodeType": "MemberAccess", "src": "3654:25:3", "typeDescriptions": {"typeIdentifier": "t_function_transfer_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 525, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3654:34:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 526, "nodeType": "ExpressionStatement", "src": "3654:34:3"}, {"eventCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 528, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "3725:5:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 529, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3725:7:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 530, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "3734:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 527, "name": "FeeWithdrawn", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 294, "src": "3712:12:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 531, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3712:30:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 532, "nodeType": "EmitStatement", "src": "3707:35:3"}]}, "documentation": {"id": 496, "nodeType": "StructuredDocumentation", "src": "3412:60:3", "text": " @dev Withdraw collected fees (only owner)"}, "functionSelector": "476343ee", "id": 534, "implemented": true, "kind": "function", "modifiers": [{"id": 499, "kind": "modifierInvocation", "modifierName": {"id": 498, "name": "only<PERSON><PERSON>er", "nameLocations": ["3510:9:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "3510:9:3"}, "nodeType": "ModifierInvocation", "src": "3510:9:3"}, {"id": 501, "kind": "modifierInvocation", "modifierName": {"id": 500, "name": "nonReentrant", "nameLocations": ["3520:12:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 210, "src": "3520:12:3"}, "nodeType": "ModifierInvocation", "src": "3520:12:3"}], "name": "withdrawFees", "nameLocation": "3486:12:3", "nodeType": "FunctionDefinition", "parameters": {"id": 497, "nodeType": "ParameterList", "parameters": [], "src": "3498:2:3"}, "returnParameters": {"id": 502, "nodeType": "ParameterList", "parameters": [], "src": "3533:0:3"}, "scope": 568, "src": "3477:272:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 553, "nodeType": "Block", "src": "3867:65:3", "statements": [{"expression": {"arguments": [{"expression": {"arguments": [{"id": 548, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "3911:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_WerantWalletConnect_$568", "typeString": "contract WerantWalletConnect"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_WerantWalletConnect_$568", "typeString": "contract WerantWalletConnect"}], "id": 547, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3903:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 546, "name": "address", "nodeType": "ElementaryTypeName", "src": "3903:7:3", "typeDescriptions": {}}}, "id": 549, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3903:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 550, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3917:7:3", "memberName": "balance", "nodeType": "MemberAccess", "src": "3903:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 542, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "3885:5:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 543, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3885:7:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 541, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3877:8:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_payable_$", "typeString": "type(address payable)"}, "typeName": {"id": 540, "name": "address", "nodeType": "ElementaryTypeName", "src": "3877:8:3", "stateMutability": "payable", "typeDescriptions": {}}}, "id": 544, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3877:16:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 545, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3894:8:3", "memberName": "transfer", "nodeType": "MemberAccess", "src": "3877:25:3", "typeDescriptions": {"typeIdentifier": "t_function_transfer_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 551, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3877:48:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 552, "nodeType": "ExpressionStatement", "src": "3877:48:3"}]}, "documentation": {"id": 535, "nodeType": "StructuredDocumentation", "src": "3759:55:3", "text": " @dev Emergency withdraw (only owner)"}, "functionSelector": "db2e21bc", "id": 554, "implemented": true, "kind": "function", "modifiers": [{"id": 538, "kind": "modifierInvocation", "modifierName": {"id": 537, "name": "only<PERSON><PERSON>er", "nameLocations": ["3857:9:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "3857:9:3"}, "nodeType": "ModifierInvocation", "src": "3857:9:3"}], "name": "emergencyWithdraw", "nameLocation": "3828:17:3", "nodeType": "FunctionDefinition", "parameters": {"id": 536, "nodeType": "ParameterList", "parameters": [], "src": "3845:2:3"}, "returnParameters": {"id": 539, "nodeType": "ParameterList", "parameters": [], "src": "3867:0:3"}, "scope": 568, "src": "3819:113:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 566, "nodeType": "Block", "src": "4069:185:3", "statements": [{"expression": {"arguments": [{"hexValue": "436f6e6e656374696f6e2066656520697320666978656420617420302e3235204d4f4e", "id": 563, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4209:37:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_989296466e6c9787786f32fc0a5e1096dc0afe6f5a546919b3bfa93d96c4df7c", "typeString": "literal_string \"Connection fee is fixed at 0.25 MON\""}, "value": "Connection fee is fixed at 0.25 MON"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_989296466e6c9787786f32fc0a5e1096dc0afe6f5a546919b3bfa93d96c4df7c", "typeString": "literal_string \"Connection fee is fixed at 0.25 MON\""}], "id": 562, "name": "revert", "nodeType": "Identifier", "overloadedDeclarations": [-19, -19], "referencedDeclaration": -19, "src": "4202:6:3", "typeDescriptions": {"typeIdentifier": "t_function_revert_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 564, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4202:45:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 565, "nodeType": "ExpressionStatement", "src": "4202:45:3"}]}, "documentation": {"id": 555, "nodeType": "StructuredDocumentation", "src": "3942:58:3", "text": " @dev Update connection fee (only owner)"}, "functionSelector": "1a18aeef", "id": 567, "implemented": true, "kind": "function", "modifiers": [{"id": 560, "kind": "modifierInvocation", "modifierName": {"id": 559, "name": "only<PERSON><PERSON>er", "nameLocations": ["4059:9:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "4059:9:3"}, "nodeType": "ModifierInvocation", "src": "4059:9:3"}], "name": "updateConnectionFee", "nameLocation": "4014:19:3", "nodeType": "FunctionDefinition", "parameters": {"id": 558, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 557, "mutability": "mutable", "name": "new<PERSON>ee", "nameLocation": "4042:6:3", "nodeType": "VariableDeclaration", "scope": 567, "src": "4034:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 556, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4034:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4033:16:3"}, "returnParameters": {"id": 561, "nodeType": "ParameterList", "parameters": [], "src": "4069:0:3"}, "scope": 568, "src": "4005:249:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "scope": 569, "src": "289:3967:3", "usedErrors": [13, 18, 191], "usedEvents": [24, 282, 288, 294]}], "src": "32:4225:3"}, "id": 3}}, "contracts": {"@openzeppelin/contracts/access/Ownable.sol": {"Ownable": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which provides a basic access control mechanism, where there is an account (an owner) that can be granted exclusive access to specific functions. The initial owner is set to the address provided by the deployer. This can later be changed with {transferOwnership}. This module is used through inheritance. It will make available the modifier `onlyOwner`, which can be applied to your functions to restrict their use to the owner.\",\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"Initializes the contract setting the address provided by the deployer as the initial owner.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/access/Ownable.sol\":\"Ownable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/Context.sol": {"Context": {"abi": [], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Provides information about the current execution context, including the sender of the transaction and its data. While these are generally available via msg.sender and msg.data, they should not be accessed in such a direct manner, since when dealing with meta-transactions the account sending and paying for execution may not be the actual sender (as far as an application is concerned). This contract is only required for intermediate, library-like contracts.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/Context.sol\":\"Context\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"ReentrancyGuard": {"abi": [{"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Contract module that helps prevent reentrant calls to a function. Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier available, which can be applied to functions to make sure there are no nested (reentrant) calls to them. Note that because there is a single `nonReentrant` guard, functions marked as `nonReentrant` may not call one another. This can be worked around by making those functions `private`, and then adding `external` `nonReentrant` entry points to them. TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at, consider using {ReentrancyGuardTransient} instead. TIP: If you would like to learn more about reentrancy and alternative ways to protect against it, check out our blog post https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\",\"errors\":{\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":\"ReentrancyGuard\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]}},\"version\":1}"}}, "contracts/WerantWalletConnect.sol": {"WerantWalletConnect": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FeeWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "connectionId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feePaid", "type": "uint256"}], "name": "WalletConnected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "WalletDisconnected", "type": "event"}, {"inputs": [], "name": "CONNECTION_FEE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "connectWallet", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "connectedWallets", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "connectionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "connectionTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "disconnectWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}], "name": "getConnectionDetails", "outputs": [{"internalType": "bool", "name": "isConnected", "type": "bool"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalConnections", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}], "name": "isWalletConnected", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "new<PERSON>ee", "type": "uint256"}], "name": "updateConnectionFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {"@_199": {"entryPoint": null, "id": 199, "parameterSlots": 0, "returnSlots": 0}, "@_327": {"entryPoint": null, "id": 327, "parameterSlots": 0, "returnSlots": 0}, "@_50": {"entryPoint": null, "id": 50, "parameterSlots": 1, "returnSlots": 0}, "@_transferOwnership_146": {"entryPoint": 74, "id": 146, "parameterSlots": 1, "returnSlots": 0}, "abi_encode_tuple_t_address__to_t_address__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}}, "generatedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:219:4", "statements": [{"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:4", "statements": []}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "115:102:4", "statements": [{"nodeType": "YulAssignment", "src": "125:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "137:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "148:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "133:3:4"}, "nodeType": "YulFunctionCall", "src": "133:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "125:4:4"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "167:9:4"}, {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "182:6:4"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "198:3:4", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "203:1:4", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "194:3:4"}, "nodeType": "YulFunctionCall", "src": "194:11:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "207:1:4", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "190:3:4"}, "nodeType": "YulFunctionCall", "src": "190:19:4"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "178:3:4"}, "nodeType": "YulFunctionCall", "src": "178:32:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "160:6:4"}, "nodeType": "YulFunctionCall", "src": "160:51:4"}, "nodeType": "YulExpressionStatement", "src": "160:51:4"}]}, "name": "abi_encode_tuple_t_address__to_t_address__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "84:9:4", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "95:6:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "106:4:4", "type": ""}], "src": "14:203:4"}]}, "contents": "{\n    { }\n    function abi_encode_tuple_t_address__to_t_address__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, and(value0, sub(shl(160, 1), 1)))\n    }\n}", "id": 4, "language": "<PERSON>l", "name": "#utility.yul"}], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLER DUP1 PUSH2 0x37 JUMPI PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0x40 DUP2 PUSH2 0x4A JUMP JUMPDEST POP PUSH1 0x1 DUP1 SSTORE PUSH2 0x9A JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP4 DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP4 AND DUP2 OR DUP5 SSTORE PUSH1 0x40 MLOAD SWAP2 SWAP1 SWAP3 AND SWAP3 DUP4 SWAP2 PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 SWAP2 SWAP1 LOG3 POP POP JUMP JUMPDEST PUSH2 0x8F3 DUP1 PUSH2 0xA9 PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT PUSH2 0xF3 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x715018A6 GT PUSH2 0x8A JUMPI DUP1 PUSH4 0xCCB2BDBD GT PUSH2 0x59 JUMPI DUP1 PUSH4 0xCCB2BDBD EQ PUSH2 0x2D4 JUMPI DUP1 PUSH4 0xDB2E21BC EQ PUSH2 0x301 JUMPI DUP1 PUSH4 0xE81CECDE EQ PUSH2 0x316 JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x332 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x715018A6 EQ PUSH2 0x231 JUMPI DUP1 PUSH4 0x78961F94 EQ PUSH2 0x246 JUMPI DUP1 PUSH4 0x80CA5A05 EQ PUSH2 0x27F JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x2AC JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x476343EE GT PUSH2 0xC6 JUMPI DUP1 PUSH4 0x476343EE EQ PUSH2 0x186 JUMPI DUP1 PUSH4 0x4BBEC111 EQ PUSH2 0x19B JUMPI DUP1 PUSH4 0x63E680AE EQ PUSH2 0x209 JUMPI DUP1 PUSH4 0x6F9FB98A EQ PUSH2 0x21E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x184009B0 EQ PUSH2 0xF8 JUMPI DUP1 PUSH4 0x1A18AEEF EQ PUSH2 0x13D JUMPI DUP1 PUSH4 0x2D66F063 EQ PUSH2 0x15F JUMPI DUP1 PUSH4 0x328D1E60 EQ PUSH2 0x167 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x104 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x128 PUSH2 0x113 CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND DUP2 JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x149 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x158 CALLDATASIZE PUSH1 0x4 PUSH2 0x85C JUMP JUMPDEST PUSH2 0x352 JUMP JUMPDEST STOP JUMPDEST PUSH2 0x15D PUSH2 0x3B3 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x173 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x2 SLOAD JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x134 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x192 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x556 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1A7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x1EC PUSH2 0x1B6 CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 SLOAD PUSH1 0x4 DUP4 MSTORE DUP2 DUP5 KECCAK256 SLOAD PUSH1 0x5 SWAP1 SWAP4 MSTORE SWAP3 KECCAK256 SLOAD PUSH1 0xFF SWAP1 SWAP3 AND SWAP3 SWAP1 SWAP2 SWAP1 JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD SWAP4 ISZERO ISZERO DUP5 MSTORE PUSH1 0x20 DUP5 ADD SWAP3 SWAP1 SWAP3 MSTORE SWAP1 DUP3 ADD MSTORE PUSH1 0x60 ADD PUSH2 0x134 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x215 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x63C JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x22A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP SELFBALANCE PUSH2 0x178 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x23D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x6F3 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x252 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x128 PUSH2 0x261 CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND SWAP1 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x28B JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x178 PUSH2 0x29A CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2B8 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x134 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2E0 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x178 PUSH2 0x2EF CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x30D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x705 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x322 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x178 PUSH8 0x16345785D8A0000 DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x33E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x34D CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH2 0x74A JUMP JUMPDEST PUSH2 0x35A PUSH2 0x785 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x23 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436F6E6E656374696F6E2066656520697320666978656420617420302E323520 PUSH1 0x44 DUP3 ADD MSTORE PUSH3 0x26A7A7 PUSH1 0xE9 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0x3BB PUSH2 0x7B2 JUMP JUMPDEST CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND ISZERO PUSH2 0x41B JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x57616C6C657420616C726561647920636F6E6E65637465640000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH8 0x16345785D8A0000 CALLVALUE LT ISZERO PUSH2 0x473 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E73756666696369656E7420636F6E6E656374696F6E206665650000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH1 0x2 DUP1 SLOAD SWAP1 PUSH1 0x0 PUSH2 0x483 DUP4 PUSH2 0x88B JUMP JUMPDEST SWAP1 SWAP2 SSTORE POP POP PUSH1 0x2 SLOAD CALLER PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR SWAP1 SSTORE PUSH1 0x4 DUP3 MSTORE DUP1 DUP4 KECCAK256 TIMESTAMP SWAP1 DUP2 SWAP1 SSTORE PUSH1 0x5 DUP4 MSTORE SWAP3 DUP2 SWAP1 KECCAK256 DUP6 SWAP1 SSTORE DUP1 MLOAD SWAP3 DUP4 MSTORE CALLVALUE SWAP2 DUP4 ADD SWAP2 SWAP1 SWAP2 MSTORE DUP4 SWAP3 SWAP2 PUSH32 0xFF69AF0E8F9DDA82BB1C4BE69D354048E8E5D59546B9CC2FEDC70921BF979374 SWAP2 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 PUSH8 0x16345785D8A0000 CALLVALUE GT ISZERO PUSH2 0x54A JUMPI CALLER PUSH2 0x8FC PUSH2 0x520 PUSH8 0x16345785D8A0000 CALLVALUE PUSH2 0x8A4 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP2 ISZERO SWAP1 SWAP3 MUL SWAP2 PUSH1 0x0 DUP2 DUP2 DUP2 DUP6 DUP9 DUP9 CALL SWAP4 POP POP POP POP ISZERO DUP1 ISZERO PUSH2 0x548 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP JUMPDEST POP PUSH2 0x554 PUSH1 0x1 DUP1 SSTORE JUMP JUMPDEST JUMP JUMPDEST PUSH2 0x55E PUSH2 0x785 JUMP JUMPDEST PUSH2 0x566 PUSH2 0x7B2 JUMP JUMPDEST SELFBALANCE DUP1 PUSH2 0x5AA JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x13 PUSH1 0x24 DUP3 ADD MSTORE PUSH19 0x4E6F206665657320746F207769746864726177 PUSH1 0x68 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND SWAP2 DUP4 ISZERO PUSH2 0x8FC MUL SWAP2 DUP5 SWAP2 DUP2 DUP2 DUP2 DUP6 DUP9 DUP9 CALL SWAP4 POP POP POP POP ISZERO DUP1 ISZERO PUSH2 0x5E3 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x78473F3F373F7673597F4F0FA5873CB4D375FEA6D4339AD6B56DBD411513CB3F DUP3 PUSH1 0x40 MLOAD PUSH2 0x62A SWAP2 DUP2 MSTORE PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP PUSH2 0x554 PUSH1 0x1 DUP1 SSTORE JUMP JUMPDEST CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND PUSH2 0x692 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x14 PUSH1 0x24 DUP3 ADD MSTORE PUSH20 0x15D85B1B195D081B9BDD0818DBDB9B9958DD1959 PUSH1 0x62 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST CALLER PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND SWAP1 SSTORE PUSH1 0x4 DUP3 MSTORE DUP1 DUP4 KECCAK256 DUP4 SWAP1 SSTORE PUSH1 0x5 DUP3 MSTORE DUP1 DUP4 KECCAK256 SWAP3 SWAP1 SWAP3 SSTORE SWAP1 MLOAD TIMESTAMP DUP2 MSTORE PUSH32 0xE6C9AD9C1DCB7029A6DDA83AD7FE369D1D509D01674340BB8762D43336AD4256 SWAP2 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 JUMP JUMPDEST PUSH2 0x6FB PUSH2 0x785 JUMP JUMPDEST PUSH2 0x554 PUSH1 0x0 PUSH2 0x7DC JUMP JUMPDEST PUSH2 0x70D PUSH2 0x785 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND SWAP2 SELFBALANCE DUP1 ISZERO PUSH2 0x8FC MUL SWAP3 SWAP1 SWAP2 DUP2 DUP2 DUP2 DUP6 DUP9 DUP9 CALL SWAP4 POP POP POP POP ISZERO DUP1 ISZERO PUSH2 0x747 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP JUMP JUMPDEST PUSH2 0x752 PUSH2 0x785 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH2 0x77C JUMPI PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH2 0x747 DUP2 PUSH2 0x7DC JUMP JUMPDEST PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER EQ PUSH2 0x554 JUMPI PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH1 0x2 PUSH1 0x1 SLOAD SUB PUSH2 0x7D5 JUMPI PUSH1 0x40 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x2 PUSH1 0x1 SSTORE JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP4 DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP4 AND DUP2 OR DUP5 SSTORE PUSH1 0x40 MLOAD SWAP2 SWAP1 SWAP3 AND SWAP3 DUP4 SWAP2 PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 SWAP2 SWAP1 LOG3 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x83E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND DUP2 EQ PUSH2 0x855 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x86E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLDATALOAD SWAP2 SWAP1 POP JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH1 0x1 DUP3 ADD PUSH2 0x89D JUMPI PUSH2 0x89D PUSH2 0x875 JUMP JUMPDEST POP PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST DUP2 DUP2 SUB DUP2 DUP2 GT ISZERO PUSH2 0x8B7 JUMPI PUSH2 0x8B7 PUSH2 0x875 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 LOG1 0x2A 0xBC ADDMOD XOR 0xAE EQ 0x22 0x1F 0xE8 DUP12 JUMPI 0xA6 MSTORE PUSH26 0x4159381592E50431CBB4C5C12F990D081C64736F6C6343000814 STOP CALLER ", "sourceMap": "289:3967:3:-:0;;;1307:36;;;;;;;;;-1:-1:-1;1329:10:3;;1269:95:0;;1322:31;;-1:-1:-1;;;1322:31:0;;1350:1;1322:31;;;160:51:4;133:18;;1322:31:0;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;1857:1:2;2061:21;;289:3967:3;;2912:187:0;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:0;;;-1:-1:-1;;;;;;3020:17:0;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:203:4:-;289:3967:3;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@CONNECTION_FEE_258": {"entryPoint": null, "id": 258, "parameterSlots": 0, "returnSlots": 0}, "@_checkOwner_84": {"entryPoint": 1925, "id": 84, "parameterSlots": 0, "returnSlots": 0}, "@_msgSender_159": {"entryPoint": null, "id": 159, "parameterSlots": 0, "returnSlots": 1}, "@_nonReentrantAfter_234": {"entryPoint": null, "id": 234, "parameterSlots": 0, "returnSlots": 0}, "@_nonReentrantBefore_226": {"entryPoint": 1970, "id": 226, "parameterSlots": 0, "returnSlots": 0}, "@_transferOwnership_146": {"entryPoint": 2012, "id": 146, "parameterSlots": 1, "returnSlots": 0}, "@connectWallet_401": {"entryPoint": 947, "id": 401, "parameterSlots": 0, "returnSlots": 0}, "@connectedWallets_264": {"entryPoint": null, "id": 264, "parameterSlots": 0, "returnSlots": 0}, "@connectionId_272": {"entryPoint": null, "id": 272, "parameterSlots": 0, "returnSlots": 0}, "@connectionTimestamp_268": {"entryPoint": null, "id": 268, "parameterSlots": 0, "returnSlots": 0}, "@disconnectWallet_436": {"entryPoint": 1596, "id": 436, "parameterSlots": 0, "returnSlots": 0}, "@emergencyWithdraw_554": {"entryPoint": 1797, "id": 554, "parameterSlots": 0, "returnSlots": 0}, "@getConnectionDetails_473": {"entryPoint": null, "id": 473, "parameterSlots": 1, "returnSlots": 3}, "@getContractBalance_495": {"entryPoint": null, "id": 495, "parameterSlots": 0, "returnSlots": 1}, "@getTotalConnections_482": {"entryPoint": null, "id": 482, "parameterSlots": 0, "returnSlots": 1}, "@isWalletConnected_449": {"entryPoint": null, "id": 449, "parameterSlots": 1, "returnSlots": 1}, "@owner_67": {"entryPoint": null, "id": 67, "parameterSlots": 0, "returnSlots": 1}, "@renounceOwnership_98": {"entryPoint": 1779, "id": 98, "parameterSlots": 0, "returnSlots": 0}, "@transferOwnership_126": {"entryPoint": 1866, "id": 126, "parameterSlots": 1, "returnSlots": 0}, "@updateConnectionFee_567": {"entryPoint": 850, "id": 567, "parameterSlots": 1, "returnSlots": 0}, "@withdrawFees_534": {"entryPoint": 1366, "id": 534, "parameterSlots": 0, "returnSlots": 0}, "abi_decode_tuple_t_address": {"entryPoint": 2092, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint256": {"entryPoint": 2140, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_address__to_t_address__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_bool_t_uint256_t_uint256__to_t_bool_t_uint256_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 4, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_084201e288392ddfa08ffbd740f812d6e04ee781b0829fd5c59af755524158a7__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_3ec13f7a4e7e85d370265c29e2240388125c7f1d03f18b18312372a2dd5e57df__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_524becf2bd3d6f3c74c4c55f46993b5de22a6261a1a6108cc85fed135e73299c__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_989296466e6c9787786f32fc0a5e1096dc0afe6f5a546919b3bfa93d96c4df7c__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_c1d6398b786853beeabe95287b691ff4327a69edd058593c625f082639ecb427__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 3, "returnSlots": 1}, "checked_sub_t_uint256": {"entryPoint": 2212, "id": null, "parameterSlots": 2, "returnSlots": 1}, "increment_t_uint256": {"entryPoint": 2187, "id": null, "parameterSlots": 1, "returnSlots": 1}, "panic_error_0x11": {"entryPoint": 2165, "id": null, "parameterSlots": 0, "returnSlots": 0}}, "generatedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:3871:4", "statements": [{"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:4", "statements": []}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "84:216:4", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "130:16:4", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "139:1:4", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "142:1:4", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "132:6:4"}, "nodeType": "YulFunctionCall", "src": "132:12:4"}, "nodeType": "YulExpressionStatement", "src": "132:12:4"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "105:7:4"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "114:9:4"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "101:3:4"}, "nodeType": "YulFunctionCall", "src": "101:23:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "126:2:4", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "97:3:4"}, "nodeType": "YulFunctionCall", "src": "97:32:4"}, "nodeType": "YulIf", "src": "94:52:4"}, {"nodeType": "YulVariableDeclaration", "src": "155:36:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "181:9:4"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "168:12:4"}, "nodeType": "YulFunctionCall", "src": "168:23:4"}, "variables": [{"name": "value", "nodeType": "YulTypedName", "src": "159:5:4", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "254:16:4", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "263:1:4", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "266:1:4", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "256:6:4"}, "nodeType": "YulFunctionCall", "src": "256:12:4"}, "nodeType": "YulExpressionStatement", "src": "256:12:4"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "213:5:4"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "224:5:4"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "239:3:4", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "244:1:4", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "235:3:4"}, "nodeType": "YulFunctionCall", "src": "235:11:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "248:1:4", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "231:3:4"}, "nodeType": "YulFunctionCall", "src": "231:19:4"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "220:3:4"}, "nodeType": "YulFunctionCall", "src": "220:31:4"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "210:2:4"}, "nodeType": "YulFunctionCall", "src": "210:42:4"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "203:6:4"}, "nodeType": "YulFunctionCall", "src": "203:50:4"}, "nodeType": "YulIf", "src": "200:70:4"}, {"nodeType": "YulAssignment", "src": "279:15:4", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "289:5:4"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "279:6:4"}]}]}, "name": "abi_decode_tuple_t_address", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "50:9:4", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "61:7:4", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "73:6:4", "type": ""}], "src": "14:286:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "400:92:4", "statements": [{"nodeType": "YulAssignment", "src": "410:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "422:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "433:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "418:3:4"}, "nodeType": "YulFunctionCall", "src": "418:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "410:4:4"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "452:9:4"}, {"arguments": [{"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "477:6:4"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "470:6:4"}, "nodeType": "YulFunctionCall", "src": "470:14:4"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "463:6:4"}, "nodeType": "YulFunctionCall", "src": "463:22:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "445:6:4"}, "nodeType": "YulFunctionCall", "src": "445:41:4"}, "nodeType": "YulExpressionStatement", "src": "445:41:4"}]}, "name": "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "369:9:4", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "380:6:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "391:4:4", "type": ""}], "src": "305:187:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "567:110:4", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "613:16:4", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "622:1:4", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "625:1:4", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "615:6:4"}, "nodeType": "YulFunctionCall", "src": "615:12:4"}, "nodeType": "YulExpressionStatement", "src": "615:12:4"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "588:7:4"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "597:9:4"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "584:3:4"}, "nodeType": "YulFunctionCall", "src": "584:23:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "609:2:4", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "580:3:4"}, "nodeType": "YulFunctionCall", "src": "580:32:4"}, "nodeType": "YulIf", "src": "577:52:4"}, {"nodeType": "YulAssignment", "src": "638:33:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "661:9:4"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "648:12:4"}, "nodeType": "YulFunctionCall", "src": "648:23:4"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "638:6:4"}]}]}, "name": "abi_decode_tuple_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "533:9:4", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "544:7:4", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "556:6:4", "type": ""}], "src": "497:180:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "783:76:4", "statements": [{"nodeType": "YulAssignment", "src": "793:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "805:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "816:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "801:3:4"}, "nodeType": "YulFunctionCall", "src": "801:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "793:4:4"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "835:9:4"}, {"name": "value0", "nodeType": "YulIdentifier", "src": "846:6:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "828:6:4"}, "nodeType": "YulFunctionCall", "src": "828:25:4"}, "nodeType": "YulExpressionStatement", "src": "828:25:4"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "752:9:4", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "763:6:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "774:4:4", "type": ""}], "src": "682:177:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1015:178:4", "statements": [{"nodeType": "YulAssignment", "src": "1025:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1037:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1048:2:4", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1033:3:4"}, "nodeType": "YulFunctionCall", "src": "1033:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1025:4:4"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1067:9:4"}, {"arguments": [{"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "1092:6:4"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "1085:6:4"}, "nodeType": "YulFunctionCall", "src": "1085:14:4"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "1078:6:4"}, "nodeType": "YulFunctionCall", "src": "1078:22:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1060:6:4"}, "nodeType": "YulFunctionCall", "src": "1060:41:4"}, "nodeType": "YulExpressionStatement", "src": "1060:41:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1121:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1132:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1117:3:4"}, "nodeType": "YulFunctionCall", "src": "1117:18:4"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "1137:6:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1110:6:4"}, "nodeType": "YulFunctionCall", "src": "1110:34:4"}, "nodeType": "YulExpressionStatement", "src": "1110:34:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1164:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1175:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1160:3:4"}, "nodeType": "YulFunctionCall", "src": "1160:18:4"}, {"name": "value2", "nodeType": "YulIdentifier", "src": "1180:6:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1153:6:4"}, "nodeType": "YulFunctionCall", "src": "1153:34:4"}, "nodeType": "YulExpressionStatement", "src": "1153:34:4"}]}, "name": "abi_encode_tuple_t_bool_t_uint256_t_uint256__to_t_bool_t_uint256_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "968:9:4", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "979:6:4", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "987:6:4", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "995:6:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1006:4:4", "type": ""}], "src": "864:329:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1299:102:4", "statements": [{"nodeType": "YulAssignment", "src": "1309:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1321:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1332:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1317:3:4"}, "nodeType": "YulFunctionCall", "src": "1317:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1309:4:4"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1351:9:4"}, {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "1366:6:4"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1382:3:4", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1387:1:4", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "1378:3:4"}, "nodeType": "YulFunctionCall", "src": "1378:11:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1391:1:4", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1374:3:4"}, "nodeType": "YulFunctionCall", "src": "1374:19:4"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "1362:3:4"}, "nodeType": "YulFunctionCall", "src": "1362:32:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1344:6:4"}, "nodeType": "YulFunctionCall", "src": "1344:51:4"}, "nodeType": "YulExpressionStatement", "src": "1344:51:4"}]}, "name": "abi_encode_tuple_t_address__to_t_address__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1268:9:4", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "1279:6:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1290:4:4", "type": ""}], "src": "1198:203:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1580:225:4", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1597:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1608:2:4", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1590:6:4"}, "nodeType": "YulFunctionCall", "src": "1590:21:4"}, "nodeType": "YulExpressionStatement", "src": "1590:21:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1631:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1642:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1627:3:4"}, "nodeType": "YulFunctionCall", "src": "1627:18:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1647:2:4", "type": "", "value": "35"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1620:6:4"}, "nodeType": "YulFunctionCall", "src": "1620:30:4"}, "nodeType": "YulExpressionStatement", "src": "1620:30:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1670:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1681:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1666:3:4"}, "nodeType": "YulFunctionCall", "src": "1666:18:4"}, {"hexValue": "436f6e6e656374696f6e2066656520697320666978656420617420302e323520", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1686:34:4", "type": "", "value": "Connection fee is fixed at 0.25 "}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1659:6:4"}, "nodeType": "YulFunctionCall", "src": "1659:62:4"}, "nodeType": "YulExpressionStatement", "src": "1659:62:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1741:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1752:2:4", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1737:3:4"}, "nodeType": "YulFunctionCall", "src": "1737:18:4"}, {"hexValue": "4d4f4e", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1757:5:4", "type": "", "value": "MON"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1730:6:4"}, "nodeType": "YulFunctionCall", "src": "1730:33:4"}, "nodeType": "YulExpressionStatement", "src": "1730:33:4"}, {"nodeType": "YulAssignment", "src": "1772:27:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1784:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1795:3:4", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1780:3:4"}, "nodeType": "YulFunctionCall", "src": "1780:19:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1772:4:4"}]}]}, "name": "abi_encode_tuple_t_stringliteral_989296466e6c9787786f32fc0a5e1096dc0afe6f5a546919b3bfa93d96c4df7c__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1557:9:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1571:4:4", "type": ""}], "src": "1406:399:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1984:174:4", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2001:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2012:2:4", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1994:6:4"}, "nodeType": "YulFunctionCall", "src": "1994:21:4"}, "nodeType": "YulExpressionStatement", "src": "1994:21:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2035:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2046:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2031:3:4"}, "nodeType": "YulFunctionCall", "src": "2031:18:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2051:2:4", "type": "", "value": "24"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2024:6:4"}, "nodeType": "YulFunctionCall", "src": "2024:30:4"}, "nodeType": "YulExpressionStatement", "src": "2024:30:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2074:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2085:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2070:3:4"}, "nodeType": "YulFunctionCall", "src": "2070:18:4"}, {"hexValue": "57616c6c657420616c726561647920636f6e6e6563746564", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2090:26:4", "type": "", "value": "Wallet already connected"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2063:6:4"}, "nodeType": "YulFunctionCall", "src": "2063:54:4"}, "nodeType": "YulExpressionStatement", "src": "2063:54:4"}, {"nodeType": "YulAssignment", "src": "2126:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2138:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2149:2:4", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2134:3:4"}, "nodeType": "YulFunctionCall", "src": "2134:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2126:4:4"}]}]}, "name": "abi_encode_tuple_t_stringliteral_084201e288392ddfa08ffbd740f812d6e04ee781b0829fd5c59af755524158a7__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1961:9:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1975:4:4", "type": ""}], "src": "1810:348:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2337:177:4", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2354:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2365:2:4", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2347:6:4"}, "nodeType": "YulFunctionCall", "src": "2347:21:4"}, "nodeType": "YulExpressionStatement", "src": "2347:21:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2388:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2399:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2384:3:4"}, "nodeType": "YulFunctionCall", "src": "2384:18:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2404:2:4", "type": "", "value": "27"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2377:6:4"}, "nodeType": "YulFunctionCall", "src": "2377:30:4"}, "nodeType": "YulExpressionStatement", "src": "2377:30:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2427:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2438:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2423:3:4"}, "nodeType": "YulFunctionCall", "src": "2423:18:4"}, {"hexValue": "496e73756666696369656e7420636f6e6e656374696f6e20666565", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2443:29:4", "type": "", "value": "Insufficient connection fee"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2416:6:4"}, "nodeType": "YulFunctionCall", "src": "2416:57:4"}, "nodeType": "YulExpressionStatement", "src": "2416:57:4"}, {"nodeType": "YulAssignment", "src": "2482:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2494:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2505:2:4", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2490:3:4"}, "nodeType": "YulFunctionCall", "src": "2490:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2482:4:4"}]}]}, "name": "abi_encode_tuple_t_stringliteral_c1d6398b786853beeabe95287b691ff4327a69edd058593c625f082639ecb427__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2314:9:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2328:4:4", "type": ""}], "src": "2163:351:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2551:95:4", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2568:1:4", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2575:3:4", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2580:10:4", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "2571:3:4"}, "nodeType": "YulFunctionCall", "src": "2571:20:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2561:6:4"}, "nodeType": "YulFunctionCall", "src": "2561:31:4"}, "nodeType": "YulExpressionStatement", "src": "2561:31:4"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2608:1:4", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2611:4:4", "type": "", "value": "0x11"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2601:6:4"}, "nodeType": "YulFunctionCall", "src": "2601:15:4"}, "nodeType": "YulExpressionStatement", "src": "2601:15:4"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2632:1:4", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2635:4:4", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2625:6:4"}, "nodeType": "YulFunctionCall", "src": "2625:15:4"}, "nodeType": "YulExpressionStatement", "src": "2625:15:4"}]}, "name": "panic_error_0x11", "nodeType": "YulFunctionDefinition", "src": "2519:127:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2698:88:4", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2729:22:4", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x11", "nodeType": "YulIdentifier", "src": "2731:16:4"}, "nodeType": "YulFunctionCall", "src": "2731:18:4"}, "nodeType": "YulExpressionStatement", "src": "2731:18:4"}]}, "condition": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2714:5:4"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2725:1:4", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "2721:3:4"}, "nodeType": "YulFunctionCall", "src": "2721:6:4"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "2711:2:4"}, "nodeType": "YulFunctionCall", "src": "2711:17:4"}, "nodeType": "YulIf", "src": "2708:43:4"}, {"nodeType": "YulAssignment", "src": "2760:20:4", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "2771:5:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2778:1:4", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2767:3:4"}, "nodeType": "YulFunctionCall", "src": "2767:13:4"}, "variableNames": [{"name": "ret", "nodeType": "YulIdentifier", "src": "2760:3:4"}]}]}, "name": "increment_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "2680:5:4", "type": ""}], "returnVariables": [{"name": "ret", "nodeType": "YulTypedName", "src": "2690:3:4", "type": ""}], "src": "2651:135:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2920:119:4", "statements": [{"nodeType": "YulAssignment", "src": "2930:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2942:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2953:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2938:3:4"}, "nodeType": "YulFunctionCall", "src": "2938:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2930:4:4"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2972:9:4"}, {"name": "value0", "nodeType": "YulIdentifier", "src": "2983:6:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2965:6:4"}, "nodeType": "YulFunctionCall", "src": "2965:25:4"}, "nodeType": "YulExpressionStatement", "src": "2965:25:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3010:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3021:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3006:3:4"}, "nodeType": "YulFunctionCall", "src": "3006:18:4"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "3026:6:4"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2999:6:4"}, "nodeType": "YulFunctionCall", "src": "2999:34:4"}, "nodeType": "YulExpressionStatement", "src": "2999:34:4"}]}, "name": "abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2881:9:4", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "2892:6:4", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "2900:6:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2911:4:4", "type": ""}], "src": "2791:248:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3093:79:4", "statements": [{"nodeType": "YulAssignment", "src": "3103:17:4", "value": {"arguments": [{"name": "x", "nodeType": "YulIdentifier", "src": "3115:1:4"}, {"name": "y", "nodeType": "YulIdentifier", "src": "3118:1:4"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "3111:3:4"}, "nodeType": "YulFunctionCall", "src": "3111:9:4"}, "variableNames": [{"name": "diff", "nodeType": "YulIdentifier", "src": "3103:4:4"}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3144:22:4", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x11", "nodeType": "YulIdentifier", "src": "3146:16:4"}, "nodeType": "YulFunctionCall", "src": "3146:18:4"}, "nodeType": "YulExpressionStatement", "src": "3146:18:4"}]}, "condition": {"arguments": [{"name": "diff", "nodeType": "YulIdentifier", "src": "3135:4:4"}, {"name": "x", "nodeType": "YulIdentifier", "src": "3141:1:4"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "3132:2:4"}, "nodeType": "YulFunctionCall", "src": "3132:11:4"}, "nodeType": "YulIf", "src": "3129:37:4"}]}, "name": "checked_sub_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "x", "nodeType": "YulTypedName", "src": "3075:1:4", "type": ""}, {"name": "y", "nodeType": "YulTypedName", "src": "3078:1:4", "type": ""}], "returnVariables": [{"name": "diff", "nodeType": "YulTypedName", "src": "3084:4:4", "type": ""}], "src": "3044:128:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3351:169:4", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3368:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3379:2:4", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3361:6:4"}, "nodeType": "YulFunctionCall", "src": "3361:21:4"}, "nodeType": "YulExpressionStatement", "src": "3361:21:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3402:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3413:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3398:3:4"}, "nodeType": "YulFunctionCall", "src": "3398:18:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3418:2:4", "type": "", "value": "19"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3391:6:4"}, "nodeType": "YulFunctionCall", "src": "3391:30:4"}, "nodeType": "YulExpressionStatement", "src": "3391:30:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3441:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3452:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3437:3:4"}, "nodeType": "YulFunctionCall", "src": "3437:18:4"}, {"hexValue": "4e6f206665657320746f207769746864726177", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3457:21:4", "type": "", "value": "No fees to withdraw"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3430:6:4"}, "nodeType": "YulFunctionCall", "src": "3430:49:4"}, "nodeType": "YulExpressionStatement", "src": "3430:49:4"}, {"nodeType": "YulAssignment", "src": "3488:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3500:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3511:2:4", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3496:3:4"}, "nodeType": "YulFunctionCall", "src": "3496:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "3488:4:4"}]}]}, "name": "abi_encode_tuple_t_stringliteral_524becf2bd3d6f3c74c4c55f46993b5de22a6261a1a6108cc85fed135e73299c__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "3328:9:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "3342:4:4", "type": ""}], "src": "3177:343:4"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3699:170:4", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3716:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3727:2:4", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3709:6:4"}, "nodeType": "YulFunctionCall", "src": "3709:21:4"}, "nodeType": "YulExpressionStatement", "src": "3709:21:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3750:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3761:2:4", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3746:3:4"}, "nodeType": "YulFunctionCall", "src": "3746:18:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3766:2:4", "type": "", "value": "20"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3739:6:4"}, "nodeType": "YulFunctionCall", "src": "3739:30:4"}, "nodeType": "YulExpressionStatement", "src": "3739:30:4"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3789:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3800:2:4", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3785:3:4"}, "nodeType": "YulFunctionCall", "src": "3785:18:4"}, {"hexValue": "57616c6c6574206e6f7420636f6e6e6563746564", "kind": "string", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3805:22:4", "type": "", "value": "Wallet not connected"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3778:6:4"}, "nodeType": "YulFunctionCall", "src": "3778:50:4"}, "nodeType": "YulExpressionStatement", "src": "3778:50:4"}, {"nodeType": "YulAssignment", "src": "3837:26:4", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "3849:9:4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3860:2:4", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3845:3:4"}, "nodeType": "YulFunctionCall", "src": "3845:18:4"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "3837:4:4"}]}]}, "name": "abi_encode_tuple_t_stringliteral_3ec13f7a4e7e85d370265c29e2240388125c7f1d03f18b18312372a2dd5e57df__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "3676:9:4", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "3690:4:4", "type": ""}], "src": "3525:344:4"}]}, "contents": "{\n    { }\n    function abi_decode_tuple_t_address(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        let value := calldataload(headStart)\n        if iszero(eq(value, and(value, sub(shl(160, 1), 1)))) { revert(0, 0) }\n        value0 := value\n    }\n    function abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, iszero(iszero(value0)))\n    }\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        value0 := calldataload(headStart)\n    }\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, value0)\n    }\n    function abi_encode_tuple_t_bool_t_uint256_t_uint256__to_t_bool_t_uint256_t_uint256__fromStack_reversed(headStart, value2, value1, value0) -> tail\n    {\n        tail := add(headStart, 96)\n        mstore(headStart, iszero(iszero(value0)))\n        mstore(add(headStart, 32), value1)\n        mstore(add(headStart, 64), value2)\n    }\n    function abi_encode_tuple_t_address__to_t_address__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, and(value0, sub(shl(160, 1), 1)))\n    }\n    function abi_encode_tuple_t_stringliteral_989296466e6c9787786f32fc0a5e1096dc0afe6f5a546919b3bfa93d96c4df7c__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 35)\n        mstore(add(headStart, 64), \"Connection fee is fixed at 0.25 \")\n        mstore(add(headStart, 96), \"MON\")\n        tail := add(headStart, 128)\n    }\n    function abi_encode_tuple_t_stringliteral_084201e288392ddfa08ffbd740f812d6e04ee781b0829fd5c59af755524158a7__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 24)\n        mstore(add(headStart, 64), \"Wallet already connected\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_c1d6398b786853beeabe95287b691ff4327a69edd058593c625f082639ecb427__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 27)\n        mstore(add(headStart, 64), \"Insufficient connection fee\")\n        tail := add(headStart, 96)\n    }\n    function panic_error_0x11()\n    {\n        mstore(0, shl(224, 0x4e487b71))\n        mstore(4, 0x11)\n        revert(0, 0x24)\n    }\n    function increment_t_uint256(value) -> ret\n    {\n        if eq(value, not(0)) { panic_error_0x11() }\n        ret := add(value, 1)\n    }\n    function abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed(headStart, value1, value0) -> tail\n    {\n        tail := add(headStart, 64)\n        mstore(headStart, value0)\n        mstore(add(headStart, 32), value1)\n    }\n    function checked_sub_t_uint256(x, y) -> diff\n    {\n        diff := sub(x, y)\n        if gt(diff, x) { panic_error_0x11() }\n    }\n    function abi_encode_tuple_t_stringliteral_524becf2bd3d6f3c74c4c55f46993b5de22a6261a1a6108cc85fed135e73299c__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 19)\n        mstore(add(headStart, 64), \"No fees to withdraw\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_3ec13f7a4e7e85d370265c29e2240388125c7f1d03f18b18312372a2dd5e57df__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 20)\n        mstore(add(headStart, 64), \"Wallet not connected\")\n        tail := add(headStart, 96)\n    }\n}", "id": 4, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT PUSH2 0xF3 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x715018A6 GT PUSH2 0x8A JUMPI DUP1 PUSH4 0xCCB2BDBD GT PUSH2 0x59 JUMPI DUP1 PUSH4 0xCCB2BDBD EQ PUSH2 0x2D4 JUMPI DUP1 PUSH4 0xDB2E21BC EQ PUSH2 0x301 JUMPI DUP1 PUSH4 0xE81CECDE EQ PUSH2 0x316 JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x332 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x715018A6 EQ PUSH2 0x231 JUMPI DUP1 PUSH4 0x78961F94 EQ PUSH2 0x246 JUMPI DUP1 PUSH4 0x80CA5A05 EQ PUSH2 0x27F JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x2AC JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x476343EE GT PUSH2 0xC6 JUMPI DUP1 PUSH4 0x476343EE EQ PUSH2 0x186 JUMPI DUP1 PUSH4 0x4BBEC111 EQ PUSH2 0x19B JUMPI DUP1 PUSH4 0x63E680AE EQ PUSH2 0x209 JUMPI DUP1 PUSH4 0x6F9FB98A EQ PUSH2 0x21E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x184009B0 EQ PUSH2 0xF8 JUMPI DUP1 PUSH4 0x1A18AEEF EQ PUSH2 0x13D JUMPI DUP1 PUSH4 0x2D66F063 EQ PUSH2 0x15F JUMPI DUP1 PUSH4 0x328D1E60 EQ PUSH2 0x167 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x104 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x128 PUSH2 0x113 CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND DUP2 JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x149 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x158 CALLDATASIZE PUSH1 0x4 PUSH2 0x85C JUMP JUMPDEST PUSH2 0x352 JUMP JUMPDEST STOP JUMPDEST PUSH2 0x15D PUSH2 0x3B3 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x173 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x2 SLOAD JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x134 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x192 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x556 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1A7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x1EC PUSH2 0x1B6 CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 SLOAD PUSH1 0x4 DUP4 MSTORE DUP2 DUP5 KECCAK256 SLOAD PUSH1 0x5 SWAP1 SWAP4 MSTORE SWAP3 KECCAK256 SLOAD PUSH1 0xFF SWAP1 SWAP3 AND SWAP3 SWAP1 SWAP2 SWAP1 JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD SWAP4 ISZERO ISZERO DUP5 MSTORE PUSH1 0x20 DUP5 ADD SWAP3 SWAP1 SWAP3 MSTORE SWAP1 DUP3 ADD MSTORE PUSH1 0x60 ADD PUSH2 0x134 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x215 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x63C JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x22A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP SELFBALANCE PUSH2 0x178 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x23D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x6F3 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x252 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x128 PUSH2 0x261 CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND SWAP1 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x28B JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x178 PUSH2 0x29A CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2B8 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x134 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2E0 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x178 PUSH2 0x2EF CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x30D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x705 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x322 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x178 PUSH8 0x16345785D8A0000 DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x33E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x15D PUSH2 0x34D CALLDATASIZE PUSH1 0x4 PUSH2 0x82C JUMP JUMPDEST PUSH2 0x74A JUMP JUMPDEST PUSH2 0x35A PUSH2 0x785 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x23 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436F6E6E656374696F6E2066656520697320666978656420617420302E323520 PUSH1 0x44 DUP3 ADD MSTORE PUSH3 0x26A7A7 PUSH1 0xE9 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0x3BB PUSH2 0x7B2 JUMP JUMPDEST CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND ISZERO PUSH2 0x41B JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x57616C6C657420616C726561647920636F6E6E65637465640000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH8 0x16345785D8A0000 CALLVALUE LT ISZERO PUSH2 0x473 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1B PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E73756666696369656E7420636F6E6E656374696F6E206665650000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH1 0x2 DUP1 SLOAD SWAP1 PUSH1 0x0 PUSH2 0x483 DUP4 PUSH2 0x88B JUMP JUMPDEST SWAP1 SWAP2 SSTORE POP POP PUSH1 0x2 SLOAD CALLER PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR SWAP1 SSTORE PUSH1 0x4 DUP3 MSTORE DUP1 DUP4 KECCAK256 TIMESTAMP SWAP1 DUP2 SWAP1 SSTORE PUSH1 0x5 DUP4 MSTORE SWAP3 DUP2 SWAP1 KECCAK256 DUP6 SWAP1 SSTORE DUP1 MLOAD SWAP3 DUP4 MSTORE CALLVALUE SWAP2 DUP4 ADD SWAP2 SWAP1 SWAP2 MSTORE DUP4 SWAP3 SWAP2 PUSH32 0xFF69AF0E8F9DDA82BB1C4BE69D354048E8E5D59546B9CC2FEDC70921BF979374 SWAP2 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 PUSH8 0x16345785D8A0000 CALLVALUE GT ISZERO PUSH2 0x54A JUMPI CALLER PUSH2 0x8FC PUSH2 0x520 PUSH8 0x16345785D8A0000 CALLVALUE PUSH2 0x8A4 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP2 ISZERO SWAP1 SWAP3 MUL SWAP2 PUSH1 0x0 DUP2 DUP2 DUP2 DUP6 DUP9 DUP9 CALL SWAP4 POP POP POP POP ISZERO DUP1 ISZERO PUSH2 0x548 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP JUMPDEST POP PUSH2 0x554 PUSH1 0x1 DUP1 SSTORE JUMP JUMPDEST JUMP JUMPDEST PUSH2 0x55E PUSH2 0x785 JUMP JUMPDEST PUSH2 0x566 PUSH2 0x7B2 JUMP JUMPDEST SELFBALANCE DUP1 PUSH2 0x5AA JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x13 PUSH1 0x24 DUP3 ADD MSTORE PUSH19 0x4E6F206665657320746F207769746864726177 PUSH1 0x68 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND SWAP2 DUP4 ISZERO PUSH2 0x8FC MUL SWAP2 DUP5 SWAP2 DUP2 DUP2 DUP2 DUP6 DUP9 DUP9 CALL SWAP4 POP POP POP POP ISZERO DUP1 ISZERO PUSH2 0x5E3 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x78473F3F373F7673597F4F0FA5873CB4D375FEA6D4339AD6B56DBD411513CB3F DUP3 PUSH1 0x40 MLOAD PUSH2 0x62A SWAP2 DUP2 MSTORE PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP PUSH2 0x554 PUSH1 0x1 DUP1 SSTORE JUMP JUMPDEST CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND PUSH2 0x692 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x14 PUSH1 0x24 DUP3 ADD MSTORE PUSH20 0x15D85B1B195D081B9BDD0818DBDB9B9958DD1959 PUSH1 0x62 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD PUSH2 0x3AA JUMP JUMPDEST CALLER PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND SWAP1 SSTORE PUSH1 0x4 DUP3 MSTORE DUP1 DUP4 KECCAK256 DUP4 SWAP1 SSTORE PUSH1 0x5 DUP3 MSTORE DUP1 DUP4 KECCAK256 SWAP3 SWAP1 SWAP3 SSTORE SWAP1 MLOAD TIMESTAMP DUP2 MSTORE PUSH32 0xE6C9AD9C1DCB7029A6DDA83AD7FE369D1D509D01674340BB8762D43336AD4256 SWAP2 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 JUMP JUMPDEST PUSH2 0x6FB PUSH2 0x785 JUMP JUMPDEST PUSH2 0x554 PUSH1 0x0 PUSH2 0x7DC JUMP JUMPDEST PUSH2 0x70D PUSH2 0x785 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND SWAP2 SELFBALANCE DUP1 ISZERO PUSH2 0x8FC MUL SWAP3 SWAP1 SWAP2 DUP2 DUP2 DUP2 DUP6 DUP9 DUP9 CALL SWAP4 POP POP POP POP ISZERO DUP1 ISZERO PUSH2 0x747 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP JUMP JUMPDEST PUSH2 0x752 PUSH2 0x785 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH2 0x77C JUMPI PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH2 0x747 DUP2 PUSH2 0x7DC JUMP JUMPDEST PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER EQ PUSH2 0x554 JUMPI PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3AA JUMP JUMPDEST PUSH1 0x2 PUSH1 0x1 SLOAD SUB PUSH2 0x7D5 JUMPI PUSH1 0x40 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x2 PUSH1 0x1 SSTORE JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP4 DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP4 AND DUP2 OR DUP5 SSTORE PUSH1 0x40 MLOAD SWAP2 SWAP1 SWAP3 AND SWAP3 DUP4 SWAP2 PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 SWAP2 SWAP1 LOG3 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x83E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND DUP2 EQ PUSH2 0x855 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x86E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLDATALOAD SWAP2 SWAP1 POP JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH1 0x1 DUP3 ADD PUSH2 0x89D JUMPI PUSH2 0x89D PUSH2 0x875 JUMP JUMPDEST POP PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST DUP2 DUP2 SUB DUP2 DUP2 GT ISZERO PUSH2 0x8B7 JUMPI PUSH2 0x8B7 PUSH2 0x875 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 LOG1 0x2A 0xBC ADDMOD XOR 0xAE EQ 0x22 0x1F 0xE8 DUP12 JUMPI 0xA6 MSTORE PUSH26 0x4159381592E50431CBB4C5C12F990D081C64736F6C6343000814 STOP CALLER ", "sourceMap": "289:3967:3:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;496:48;;;;;;;;;;-1:-1:-1;496:48:3;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;470:14:4;;463:22;445:41;;433:2;418:18;496:48:3;;;;;;;;4005:249;;;;;;;;;;-1:-1:-1;4005:249:3;;;;;:::i;:::-;;:::i;:::-;;1425:720;;;:::i;3135:101::-;;;;;;;;;;-1:-1:-1;3215:14:3;;3135:101;;;828:25:4;;;816:2;801:18;3135:101:3;682:177:4;3477:272:3;;;;;;;;;;;;;:::i;2720:343::-;;;;;;;;;;-1:-1:-1;2720:343:3;;;;;:::i;:::-;-1:-1:-1;;;;;2947:24:3;2829:16;2947:24;;;:16;:24;;;;;;;;;2985:19;:27;;;;;;3026:12;:20;;;;;;2947:24;;;;;2985:27;;3026:20;2720:343;;;;;1085:14:4;;1078:22;1060:41;;1132:2;1117:18;;1110:34;;;;1160:18;;;1153:34;1048:2;1033:18;2720:343:3;864:329:4;2201:264:3;;;;;;;;;;;;;:::i;3295:107::-;;;;;;;;;;-1:-1:-1;3374:21:3;3295:107;;2293:101:0;;;;;;;;;;;;;:::i;2532:120:3:-;;;;;;;;;;-1:-1:-1;2532:120:3;;;;;:::i;:::-;-1:-1:-1;;;;;2621:24:3;2598:4;2621:24;;;:16;:24;;;;;;;;;2532:120;610:47;;;;;;;;;;-1:-1:-1;610:47:3;;;;;:::i;:::-;;;;;;;;;;;;;;1638:85:0;;;;;;;;;;-1:-1:-1;1684:7:0;1710:6;1638:85;;-1:-1:-1;;;;;1710:6:0;;;1344:51:4;;1332:2;1317:18;1638:85:0;1198:203:4;550:54:3;;;;;;;;;;-1:-1:-1;550:54:3;;;;;:::i;:::-;;;;;;;;;;;;;;3819:113;;;;;;;;;;;;;:::i;369:50::-;;;;;;;;;;;;410:9;369:50;;2543:215:0;;;;;;;;;;-1:-1:-1;2543:215:0;;;;;:::i;:::-;;:::i;4005:249:3:-;1531:13:0;:11;:13::i;:::-;4202:45:3::1;::::0;-1:-1:-1;;;4202:45:3;;1608:2:4;4202:45:3::1;::::0;::::1;1590:21:4::0;1647:2;1627:18;;;1620:30;1686:34;1666:18;;;1659:62;-1:-1:-1;;;1737:18:4;;;1730:33;1780:19;;4202:45:3::1;;;;;;;;1425:720:::0;2500:21:2;:19;:21::i;:::-;1239:10:3::1;1222:28;::::0;;;:16:::1;:28;::::0;;;;;::::1;;1221:29;1213:66;;;::::0;-1:-1:-1;;;1213:66:3;;2012:2:4;1213:66:3::1;::::0;::::1;1994:21:4::0;2051:2;2031:18;;;2024:30;2090:26;2070:18;;;2063:54;2134:18;;1213:66:3::1;1810:348:4::0;1213:66:3::1;410:9:::2;1511;:27;;1503:67;;;::::0;-1:-1:-1;;;1503:67:3;;2365:2:4;1503:67:3::2;::::0;::::2;2347:21:4::0;2404:2;2384:18;;;2377:30;2443:29;2423:18;;;2416:57;2490:18;;1503:67:3::2;2163:351:4::0;1503:67:3::2;1589:14;:16:::0;;;:14:::2;:16;::::0;::::2;:::i;:::-;::::0;;;-1:-1:-1;;1641:14:3::2;::::0;1691:10:::2;1615:23;1674:28:::0;;;:16:::2;:28;::::0;;;;;;;:35;;-1:-1:-1;;1674:35:3::2;1705:4;1674:35;::::0;;1719:19:::2;:31:::0;;;;;1753:15:::2;1719:49:::0;;;;1778:12:::2;:24:::0;;;;;;:42;;;1844:130;;2965:25:4;;;1955:9:3::2;3006:18:4::0;;;2999:34;;;;1641:14:3;;1691:10;1844:130:::2;::::0;2938:18:4;1844:130:3::2;;;;;;;410:9;2030;:26;2026:113;;;2080:10;2072:56;2101:26;410:9;2101;:26;:::i;:::-;2072:56;::::0;;::::2;::::0;;::::2;::::0;::::2;::::0;;;;;;::::2;;;;;;;;;;;;;::::0;::::2;;;;;;2026:113;1493:652;2542:20:2::0;1857:1;3068:21;;2888:208;2542:20;1425:720:3:o;3477:272::-;1531:13:0;:11;:13::i;:::-;2500:21:2::1;:19;:21::i;:::-;3561::3::2;3600:11:::0;3592:43:::2;;;::::0;-1:-1:-1;;;3592:43:3;;3379:2:4;3592:43:3::2;::::0;::::2;3361:21:4::0;3418:2;3398:18;;;3391:30;-1:-1:-1;;;3437:18:4;;;3430:49;3496:18;;3592:43:3::2;3177:343:4::0;3592:43:3::2;1684:7:0::0;1710:6;;3654:34:3::2;::::0;-1:-1:-1;;;;;1710:6:0;;;;3654:34:3;::::2;;;::::0;3680:7;;3654:34;1684:7:0;3654:34:3;3680:7;1710:6:0;3654:34:3;::::2;;;;;;;;;;;;;::::0;::::2;;;;;-1:-1:-1::0;1684:7:0;1710:6;-1:-1:-1;;;;;1710:6:0;-1:-1:-1;;;;;3712:30:3::2;;3734:7;3712:30;;;;828:25:4::0;;816:2;801:18;;682:177;3712:30:3::2;;;;;;;;3533:216;2542:20:2::1;1857:1:::0;3068:21;;2888:208;2201:264:3;1115:10;1098:28;;;;:16;:28;;;;;;;;1090:61;;;;-1:-1:-1;;;1090:61:3;;3727:2:4;1090:61:3;;;3709:21:4;3766:2;3746:18;;;3739:30;-1:-1:-1;;;3785:18:4;;;3778:50;3845:18;;1090:61:3;3525:344:4;1090:61:3;2285:10:::1;2299:5;2268:28:::0;;;:16:::1;:28;::::0;;;;;;;:36;;-1:-1:-1;;2268:36:3::1;::::0;;2314:19:::1;:31:::0;;;;;:35;;;2359:12:::1;:24:::0;;;;;:28;;;;2411:47;;2442:15:::1;828:25:4::0;;2411:47:3::1;::::0;801:18:4;2411:47:3::1;;;;;;;2201:264::o:0;2293:101:0:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;3819:113:3:-:0;1531:13:0;:11;:13::i;:::-;1684:7;1710:6;;3877:48:3::1;::::0;-1:-1:-1;;;;;1710:6:0;;;;3903:21:3::1;3877:48:::0;::::1;;;::::0;3903:21;;3877:48;1684:7:0;3877:48:3;3903:21;1710:6:0;3877:48:3;::::1;;;;;;;;;;;;;::::0;::::1;;;;;;3819:113::o:0;2543:215:0:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:0;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:0;;2700:1:::1;2672:31;::::0;::::1;1344:51:4::0;1317:18;;2672:31:0::1;1198:203:4::0;2623:91:0::1;2723:28;2742:8;2723:18;:28::i;1796:162::-:0;1684:7;1710:6;-1:-1:-1;;;;;1710:6:0;735:10:1;1855:23:0;1851:101;;1901:40;;-1:-1:-1;;;1901:40:0;;735:10:1;1901:40:0;;;1344:51:4;1317:18;;1901:40:0;1198:203:4;2575:307:2;1899:1;2702:7;;:18;2698:86;;2743:30;;-1:-1:-1;;;2743:30:2;;;;;;;;;;;2698:86;1899:1;2858:7;:17;2575:307::o;2912:187:0:-;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:0;;;-1:-1:-1;;;;;;3020:17:0;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:286:4:-;73:6;126:2;114:9;105:7;101:23;97:32;94:52;;;142:1;139;132:12;94:52;168:23;;-1:-1:-1;;;;;220:31:4;;210:42;;200:70;;266:1;263;256:12;200:70;289:5;14:286;-1:-1:-1;;;14:286:4:o;497:180::-;556:6;609:2;597:9;588:7;584:23;580:32;577:52;;;625:1;622;615:12;577:52;-1:-1:-1;648:23:4;;497:180;-1:-1:-1;497:180:4:o;2519:127::-;2580:10;2575:3;2571:20;2568:1;2561:31;2611:4;2608:1;2601:15;2635:4;2632:1;2625:15;2651:135;2690:3;2711:17;;;2708:43;;2731:18;;:::i;:::-;-1:-1:-1;2778:1:4;2767:13;;2651:135::o;3044:128::-;3111:9;;;3132:11;;;3129:37;;;3146:18;;:::i;:::-;3044:128;;;;:::o"}, "methodIdentifiers": {"CONNECTION_FEE()": "e81cecde", "connectWallet()": "2d66f063", "connectedWallets(address)": "184009b0", "connectionId(address)": "80ca5a05", "connectionTimestamp(address)": "ccb2bdbd", "disconnectWallet()": "63e680ae", "emergencyWithdraw()": "db2e21bc", "getConnectionDetails(address)": "4bbec111", "getContractBalance()": "6f9fb98a", "getTotalConnections()": "328d1e60", "isWalletConnected(address)": "78961f94", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b", "updateConnectionFee(uint256)": "1a18aeef", "withdrawFees()": "476343ee"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"FeeWithdrawn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"connectionId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"feePaid\",\"type\":\"uint256\"}],\"name\":\"WalletConnected\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"WalletDisconnected\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CONNECTION_FEE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"connectWallet\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"connectedWallets\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"connectionId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"connectionTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"disconnectWallet\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"emergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"}],\"name\":\"getConnectionDetails\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isConnected\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getContractBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTotalConnections\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"}],\"name\":\"isWalletConnected\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newFee\",\"type\":\"uint256\"}],\"name\":\"updateConnectionFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Smart contract for handling wallet connections with gas fee of 0.1 MON\",\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{\"connectWallet()\":{\"details\":\"Connect wallet by paying the connection fee\"},\"disconnectWallet()\":{\"details\":\"Disconnect wallet\"},\"emergencyWithdraw()\":{\"details\":\"Emergency withdraw (only owner)\"},\"getConnectionDetails(address)\":{\"details\":\"Get wallet connection details\"},\"getContractBalance()\":{\"details\":\"Get contract balance\"},\"getTotalConnections()\":{\"details\":\"Get total connected wallets count\"},\"isWalletConnected(address)\":{\"details\":\"Check if wallet is connected\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updateConnectionFee(uint256)\":{\"details\":\"Update connection fee (only owner)\"},\"withdrawFees()\":{\"details\":\"Withdraw collected fees (only owner)\"}},\"title\":\"WerantWalletConnect\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/WerantWalletConnect.sol\":\"WerantWalletConnect\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]},\"contracts/WerantWalletConnect.sol\":{\"keccak256\":\"0x5485b3508960978403f6359a0efea345229c903596e701689d8d4a0af944386b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://52b3ef149fe39062bdba40132fee8feea1031807606520ae9782c14aadb9d5c7\",\"dweb:/ipfs/QmTZvCrqhw49n4WUDdaadanSr2bH4R8RaHWcbZiXn58Xky\"]}},\"version\":1}"}}}}}