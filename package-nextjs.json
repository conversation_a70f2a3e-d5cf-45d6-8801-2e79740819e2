{"name": "werant-nextjs-wallet-modal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "wagmi": "^1.4.12", "viem": "^1.19.11", "@rainbow-me/rainbowkit": "^1.3.5", "@tanstack/react-query": "^4.36.1", "@walletconnect/modal": "^2.7.0", "ethers": "^5.8.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "typescript": "^5.3.3"}, "keywords": ["nextjs", "react", "wagmi", "rainbowkit", "wallet-connect", "web3", "ethereum", "monad", "blockchain"]}