import React, { useState, useEffect } from 'react';
import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { metaMask } from 'wagmi/connectors/metaMask';
import { walletConnect } from 'wagmi/connectors/walletConnect';
import { injected } from 'wagmi/connectors/injected';
import './CustomWalletModal.css';

interface CustomWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CustomWalletModal: React.FC<CustomWalletModalProps> = ({ isOpen, onClose }) => {
  const { connect, connectors, isLoading, pendingConnector } = useConnect();
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const [hasMetaMask, setHasMetaMask] = useState(false);
  const [hasTrustWallet, setHasTrustWallet] = useState(false);

  useEffect(() => {
    // Check for MetaMask
    setHasMetaMask(
      typeof window !== 'undefined' && 
      typeof window.ethereum !== 'undefined' && 
      window.ethereum.isMetaMask
    );

    // Check for Trust Wallet
    setHasTrustWallet(
      typeof window !== 'undefined' && 
      typeof window.ethereum !== 'undefined' && 
      window.ethereum.isTrust
    );
  }, []);

  const walletOptions = [
    {
      id: 'walletconnect',
      name: 'WalletConnect',
      icon: '🔗',
      tag: 'QR CODE',
      tagColor: '#2d8fff',
      connector: walletConnect({
        projectId: '3203e3196ca7682fb5394a53b725d357', // Your Reown project ID
        showQrModal: true,
      }),
      available: true,
    },
    {
      id: 'metamask',
      name: 'MetaMask',
      icon: '🦊',
      tag: hasMetaMask ? 'INSTALLED' : 'NOT INSTALLED',
      tagColor: hasMetaMask ? '#28a745' : '#dc3545',
      connector: metaMask(),
      available: hasMetaMask,
    },
    {
      id: 'trustwallet',
      name: 'Trust Wallet',
      icon: '🛡️',
      tag: hasTrustWallet ? 'INSTALLED' : 'NOT INSTALLED',
      tagColor: hasTrustWallet ? '#28a745' : '#dc3545',
      connector: injected({ target: 'trust' }),
      available: hasTrustWallet,
    },
    {
      id: 'allwallets',
      name: 'All Wallets',
      icon: '💳',
      tag: '460+',
      tagColor: '#666',
      connector: injected(),
      available: typeof window !== 'undefined' && typeof window.ethereum !== 'undefined',
    },
  ];

  const handleConnect = async (connector: any) => {
    try {
      await connect({ connector });
      onClose();
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  const handleDisconnect = () => {
    disconnect();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="wallet-modal-overlay" onClick={onClose}>
      <div className="wallet-modal" onClick={(e) => e.stopPropagation()}>
        <div className="wallet-modal-header">
          <h2>Connect Wallet</h2>
          <button className="close-button" onClick={onClose}>
            ✕
          </button>
        </div>

        {isConnected ? (
          <div className="connected-state">
            <div className="connected-info">
              <div className="connection-status">
                <span className="status-dot"></span>
                <span>Connected</span>
              </div>
              <div className="wallet-address">
                {address?.slice(0, 6)}...{address?.slice(-4)}
              </div>
            </div>
            <button className="disconnect-button" onClick={handleDisconnect}>
              Disconnect
            </button>
          </div>
        ) : (
          <div className="wallet-options">
            {walletOptions.map((wallet) => (
              <div
                key={wallet.id}
                className={`wallet-option ${!wallet.available ? 'disabled' : ''}`}
                onClick={() => wallet.available && handleConnect(wallet.connector)}
              >
                <div className="wallet-info">
                  <div className="wallet-icon">{wallet.icon}</div>
                  <span className="wallet-name">{wallet.name}</span>
                </div>
                <span 
                  className="wallet-tag" 
                  style={{ backgroundColor: wallet.tagColor }}
                >
                  {wallet.tag}
                </span>
              </div>
            ))}
          </div>
        )}

        {isLoading && pendingConnector && (
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Connecting to {pendingConnector.name}...</p>
          </div>
        )}

        <div className="modal-footer">
          <p className="terms-text">
            By connecting a wallet, you agree to Werant's{' '}
            <a href="#" className="terms-link">Terms of Service</a> and{' '}
            <a href="#" className="terms-link">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default CustomWalletModal;
