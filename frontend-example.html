<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - Wallet Connect Demo</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <script src="https://unpkg.com/@walletconnect/web3-provider@1.8.0/dist/umd/index.min.js"></script>
    <script src="https://unpkg.com/@walletconnect/qrcode-modal@1.8.0/dist/umd/index.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        .warning { background: rgba(255, 193, 7, 0.3); }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .wallet-options {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .wallet-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 150px;
        }
        .wallet-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        .wallet-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .wallet-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
        }

        /* RainbowKit-style Modal */
        .wallet-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-content {
            background: white;
            border-radius: 24px;
            padding: 32px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            color: #333;
            animation: slideIn 0.3s ease-out;
            position: relative;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            color: #1a1b23;
        }

        .modal-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 8px 0 0 0;
        }

        .wallet-option {
            width: 100%;
            padding: 16px;
            margin: 8px 0;
            border: 2px solid #f3f4f6;
            background: white;
            border-radius: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            position: relative;
        }

        .wallet-option:hover {
            border-color: #667eea;
            background: #f8faff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .wallet-option:active {
            transform: translateY(0);
        }

        .wallet-option.unavailable {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }

        .wallet-option.unavailable:hover {
            border-color: #e5e7eb;
            background: white;
            transform: none;
            box-shadow: none;
        }

        .wallet-icon-large {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .wallet-info {
            flex: 1;
            text-align: left;
        }

        .wallet-name {
            font-weight: 600;
            color: #1a1b23;
            margin: 0;
        }

        .wallet-description {
            font-size: 14px;
            color: #6b7280;
            margin: 2px 0 0 0;
        }

        .wallet-status {
            color: #10b981;
            font-size: 18px;
        }

        .wallet-status.unavailable {
            color: #ef4444;
        }

        .close-button {
            width: 100%;
            padding: 12px;
            margin-top: 20px;
            border: 2px solid #f3f4f6;
            background: #f9fafb;
            border-radius: 12px;
            cursor: pointer;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .close-button:hover {
            background: #f3f4f6;
            border-color: #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Werant - Wallet Connect Demo</h1>
        <p>Connect your wallet to Monad Testnet and test the Werant voting system</p>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <div id="status" class="status info">
            Click "Connect Wallet" to get started
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <button class="btn" onclick="openWalletModal()" style="
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                font-size: 18px;
                padding: 18px 36px;
                border-radius: 15px;
                box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
                border: none;
                color: white;
                cursor: pointer;
                font-weight: 600;
                transition: all 0.3s ease;
            " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                🌈 Connect Wallet
            </button>
        </div>

        <div id="walletInfo" style="display: none;">
            <h3>Wallet Information</h3>
            <div class="code">
                <strong>Address:</strong> <span id="walletAddress"></span><br>
                <strong>Balance:</strong> <span id="walletBalance"></span> MON<br>
                <strong>Network:</strong> <span id="networkName"></span>
            </div>
        </div>

        <div id="contractInfo" style="display: none;">
            <h3>Contract Information</h3>
            <div class="code">
                <strong>Wallet Contract:</strong> <span id="walletContract"></span><br>
                <strong>Leaderboard Contract:</strong> <span id="leaderboardContract"></span><br>
                <strong>Connection Fee:</strong> 0.25 MON<br>
                <strong>Voting Fee:</strong> 0.1 MON
            </div>
        </div>

        <div id="transactionInfo" style="display: none;">
            <h3>Transaction Details</h3>
            <div class="code" id="txDetails"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3002';
        const MONAD_TESTNET = {
            chainId: '0x279F', // 10143 in hex
            chainName: 'Monad Testnet',
            rpcUrls: ['https://testnet-rpc.monad.xyz'],
            nativeCurrency: {
                name: 'MON',
                symbol: 'MON',
                decimals: 18
            },
            blockExplorerUrls: ['https://testnet-explorer.monad.xyz']
        };

        let provider, signer, userAddress, currentWalletType = null;

        // RainbowKit-style wallet configurations
        const wallets = [
            {
                name: 'MetaMask',
                description: 'Connect using browser extension',
                icon: '🦊',
                iconBg: 'linear-gradient(45deg, #f6851b, #e2761b)',
                id: 'metamask',
                available: () => window.ethereum && window.ethereum.isMetaMask
            },
            {
                name: 'Rainbow',
                description: 'Connect to Rainbow wallet',
                icon: '🌈',
                iconBg: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
                id: 'rainbow',
                available: () => window.ethereum
            },
            {
                name: 'Coinbase Wallet',
                description: 'Connect using Coinbase Wallet',
                icon: '💼',
                iconBg: 'linear-gradient(45deg, #0052ff, #0041cc)',
                id: 'coinbase',
                available: () => window.ethereum && window.ethereum.isCoinbaseWallet
            },
            {
                name: 'Trust Wallet',
                description: 'Connect using Trust Wallet',
                icon: '🛡️',
                iconBg: 'linear-gradient(45deg, #3375bb, #1a5490)',
                id: 'trust',
                available: () => window.ethereum && window.ethereum.isTrust
            },
            {
                name: 'WalletConnect',
                description: 'Scan with WalletConnect to connect',
                icon: '🔗',
                iconBg: 'linear-gradient(45deg, #3b99fc, #1a73e8)',
                id: 'walletconnect',
                available: () => true
            },
            {
                name: 'Injected Wallet',
                description: 'Connect using injected wallet',
                icon: '💳',
                iconBg: 'linear-gradient(45deg, #8b5cf6, #7c3aed)',
                id: 'injected',
                available: () => window.ethereum
            }
        ];

        async function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function openWalletModal() {
            const modal = document.createElement('div');
            modal.className = 'wallet-modal';

            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';

            modalContent.innerHTML = `
                <div class="modal-header">
                    <h2 class="modal-title">Connect a Wallet</h2>
                    <p class="modal-subtitle">Choose how you want to connect</p>
                </div>
                <div id="wallet-options"></div>
                <button class="close-button" onclick="this.parentElement.parentElement.remove()">
                    Cancel
                </button>
            `;

            const walletOptions = modalContent.querySelector('#wallet-options');

            wallets.forEach(wallet => {
                const isAvailable = wallet.available();
                const button = document.createElement('button');
                button.className = `wallet-option ${!isAvailable ? 'unavailable' : ''}`;

                button.innerHTML = `
                    <div class="wallet-icon-large" style="background: ${wallet.iconBg};">
                        ${wallet.icon}
                    </div>
                    <div class="wallet-info">
                        <div class="wallet-name">${wallet.name}</div>
                        <div class="wallet-description">
                            ${isAvailable ? wallet.description : 'Not available'}
                        </div>
                    </div>
                    <div class="wallet-status ${!isAvailable ? 'unavailable' : ''}">
                        ${isAvailable ? '✓' : '✗'}
                    </div>
                `;

                if (isAvailable) {
                    button.onclick = () => {
                        modal.remove();
                        connectWallet(wallet.id, wallet.name);
                    };
                }

                walletOptions.appendChild(button);
            });

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Close on backdrop click
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };

            // Close on escape key
            const escapeHandler = (e) => {
                if (e.key === 'Escape') {
                    modal.remove();
                    document.removeEventListener('keydown', escapeHandler);
                }
            };
            document.addEventListener('keydown', escapeHandler);
        }

        async function connectWallet(walletId, walletName) {
            try {
                updateStatus(`Connecting to ${walletName}...`, 'info');

                if (!window.ethereum) {
                    updateStatus(`No wallet found! Please install ${walletName} or another Web3 wallet.`, 'error');
                    return;
                }

                // Request account access
                await window.ethereum.request({ method: 'eth_requestAccounts' });

                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                currentWalletType = walletName;

                // Check if we're on the right network
                const network = await provider.getNetwork();
                if (network.chainId !== 10143) {
                    updateStatus('Please switch to Monad Testnet. Click "Add Monad Testnet" if needed.', 'warning');
                    // Auto-add Monad Testnet
                    await addMonadNetwork();
                } else {
                    updateStatus(`${walletName} connected successfully! ${getWalletEmoji(walletId)}`, 'success');
                }

                await updateWalletInfo();

            } catch (error) {
                console.error('Wallet connection error:', error);
                if (error.code === 4001) {
                    updateStatus('Connection rejected by user', 'warning');
                } else {
                    updateStatus(`${walletName} connection failed: ${error.message}`, 'error');
                }
            }
        }

        function getWalletEmoji(walletId) {
            const emojis = {
                'metamask': '🦊',
                'rainbow': '🌈',
                'coinbase': '💼',
                'trust': '🛡️',
                'walletconnect': '🔗',
                'injected': '💳'
            };
            return emojis[walletId] || '✅';
        }

        async function updateWalletInfo() {
            try {
                const balance = await provider.getBalance(userAddress);
                const network = await provider.getNetwork();

                document.getElementById('walletAddress').textContent = userAddress;
                document.getElementById('walletBalance').textContent = ethers.utils.formatEther(balance);
                document.getElementById('networkName').textContent = `${currentWalletType} - ${network.name || `Chain ID: ${network.chainId}`}`;
                document.getElementById('walletInfo').style.display = 'block';
            } catch (error) {
                console.error('Error updating wallet info:', error);
            }
        }

        async function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        async function addMonadNetwork() {
            try {
                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [MONAD_TESTNET]
                });
                await updateStatus('Monad Testnet added to MetaMask!', 'success');
            } catch (error) {
                console.error('Add network error:', error);
                await updateStatus(`Failed to add network: ${error.message}`, 'error');
            }
        }

        // Listen for account changes
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', function (accounts) {
                if (accounts.length === 0) {
                    // User disconnected
                    userAddress = null;
                    currentWalletType = null;
                    document.getElementById('walletInfo').style.display = 'none';
                    updateStatus('Wallet disconnected', 'info');
                } else {
                    // User switched accounts
                    location.reload();
                }
            });

            window.ethereum.on('chainChanged', function (chainId) {
                // Reload the page when network changes
                location.reload();
            });
        }

    </script>
</body>
</html>
    </script>
</body>
</html>
