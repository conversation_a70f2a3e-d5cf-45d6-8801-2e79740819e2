/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmrteesoft%2FWerank&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmrteesoft%2FWerank&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Werank/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmrteesoft%2FWerank&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fcomponents%2FSiteGatekeeper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fcomponents%2FSiteGatekeeper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SiteGatekeeper.tsx */ \"(ssr)/./src/components/SiteGatekeeper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtcnRlZXNvZnQlMkZXZXJhbmslMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRm1ydGVlc29mdCUyRldlcmFuayUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtcnRlZXNvZnQlMkZXZXJhbmslMkZzcmMlMkZhcHAlMkZwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtcnRlZXNvZnQlMkZXZXJhbmslMkZzcmMlMkZjb21wb25lbnRzJTJGU2l0ZUdhdGVrZWVwZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQWdIO0FBQ2hIO0FBQ0Esa0xBQTBIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvP2JmMTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCIvaG9tZS9tcnRlZXNvZnQvV2VyYW5rL3NyYy9hcHAvcHJvdmlkZXJzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9ob21lL21ydGVlc29mdC9XZXJhbmsvc3JjL2NvbXBvbmVudHMvU2l0ZUdhdGVrZWVwZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fcomponents%2FSiteGatekeeper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _lib_appkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/appkit */ \"(ssr)/./src/lib/appkit.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n// 0. Setup queryClient\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient();\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_4__.WagmiProvider, {\n        config: _lib_appkit__WEBPACK_IMPORTED_MODULE_2__.wagmiAdapter.wagmiConfig,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Werank/src/app/providers.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Werank/src/app/providers.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUV5QjtBQUNZO0FBQ21DO0FBQzdCO0FBRTNDLHVCQUF1QjtBQUN2QixNQUFNSyxjQUFjLElBQUlILDhEQUFXQTtBQUU1QixTQUFTSSxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNOLGdEQUFhQTtRQUFDTyxRQUFRSixxREFBWUEsQ0FBQ0ssV0FBVztrQkFDN0MsNEVBQUNOLHNFQUFtQkE7WUFBQ08sUUFBUUw7c0JBQzFCRTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vc3JjL2FwcC9wcm92aWRlcnMudHN4PzkzMjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFdhZ21pUHJvdmlkZXIgfSBmcm9tICd3YWdtaSdcbmltcG9ydCB7IFF1ZXJ5Q2xpZW50LCBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5J1xuaW1wb3J0IHsgd2FnbWlBZGFwdGVyIH0gZnJvbSAnQC9saWIvYXBwa2l0J1xuXG4vLyAwLiBTZXR1cCBxdWVyeUNsaWVudFxuY29uc3QgcXVlcnlDbGllbnQgPSBuZXcgUXVlcnlDbGllbnQoKVxuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8V2FnbWlQcm92aWRlciBjb25maWc9e3dhZ21pQWRhcHRlci53YWdtaUNvbmZpZ30+XG4gICAgICA8UXVlcnlDbGllbnRQcm92aWRlciBjbGllbnQ9e3F1ZXJ5Q2xpZW50fT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9RdWVyeUNsaWVudFByb3ZpZGVyPlxuICAgIDwvV2FnbWlQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiV2FnbWlQcm92aWRlciIsIlF1ZXJ5Q2xpZW50IiwiUXVlcnlDbGllbnRQcm92aWRlciIsIndhZ21pQWRhcHRlciIsInF1ZXJ5Q2xpZW50IiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJjb25maWciLCJ3YWdtaUNvbmZpZyIsImNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/EthereumVerificationModal.tsx":
/*!******************************************************!*\
  !*** ./src/components/EthereumVerificationModal.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _hooks_useEthereumVerification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useEthereumVerification */ \"(ssr)/./src/hooks/useEthereumVerification.ts\");\n/* harmony import */ var _services_ethereumVerification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/ethereumVerification */ \"(ssr)/./src/services/ethereumVerification.ts\");\n/* harmony import */ var _EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EthereumVerificationModal.module.css */ \"(ssr)/./src/components/EthereumVerificationModal.module.css\");\n/* harmony import */ var _EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst EthereumVerificationModal = ({ isOpen, onClose, onVerificationSuccess })=>{\n    const { address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitAccount)();\n    const { verificationStatus, isLoading, error, isEligible, transactionCount, refreshVerification } = (0,_hooks_useEthereumVerification__WEBPACK_IMPORTED_MODULE_3__.useEthereumVerification)();\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleContinue = ()=>{\n        if (isEligible) {\n            onVerificationSuccess();\n            onClose();\n        }\n    };\n    const handleSkipForDemo = ()=>{\n        // For demo purposes, allow skipping verification\n        onVerificationSuccess();\n        onClose();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().overlay),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().modal),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().header),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"\\uD83D\\uDD0D Ethereum Verification\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Verify your wallet activity on Ethereum mainnet\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().requirementInfo),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().requirementCard),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().requirementIcon),\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().requirementText),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Activity Requirement\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Your wallet must have at least \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"5 transactions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 51\n                                                    }, undefined),\n                                                    \" on Ethereum mainnet to create an account\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().walletInfo),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().walletDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().walletLabel),\n                                        children: \"Checking Wallet:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().walletAddress),\n                                        children: (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_4__.formatAddress)(address)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().statusDisplay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().spinner)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Checking Ethereum mainnet history...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().subText),\n                                    children: \"This may take a few seconds\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined),\n                        error && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().statusDisplay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().errorIcon),\n                                    children: \"❌\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Verification Error\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().errorText),\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().actionBtn)} ${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().secondary)}`,\n                                    onClick: refreshVerification,\n                                    children: \"\\uD83D\\uDD04 Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined),\n                        verificationStatus && !isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().statusDisplay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: isEligible ? (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().successIcon) : (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().warningIcon),\n                                    children: isEligible ? \"✅\" : \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_4__.getEligibilityMessage)(verificationStatus)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().transactionInfo),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().txCount),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().txNumber),\n                                                    children: transactionCount\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().txLabel),\n                                                    children: \"Transactions Found\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().txRequirement),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().reqNumber),\n                                                    children: \"5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().reqLabel),\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined),\n                                !showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailsBtn),\n                                    onClick: ()=>setShowDetails(true),\n                                    children: \"\\uD83D\\uDCCA Show Details\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, undefined),\n                                showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().details),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Verification Details\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Wallet Address:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: address\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Network:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ethereum Mainnet\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Transactions:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: transactionCount\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Checked At:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: new Date(verificationStatus.checkedAt).toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: isEligible ? (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().eligible) : (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().notEligible),\n                                                    children: isEligible ? \"Eligible ✅\" : \"Not Eligible ❌\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined),\n                        verificationStatus && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().actions),\n                            children: isEligible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: `${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().actionBtn)} ${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().primary)}`,\n                                onClick: handleContinue,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().icon),\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Continue to Payment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().notEligibleActions),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().helpText),\n                                        children: \"Your wallet needs more transaction history on Ethereum mainnet. Try using a different wallet or complete more transactions.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().actionBtn)} ${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().secondary)}`,\n                                        onClick: refreshVerification,\n                                        children: \"\\uD83D\\uDD04 Check Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().actionBtn)} ${(_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().demo)}`,\n                                        onClick: handleSkipForDemo,\n                                        children: \"⏭️ Skip for Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: (_EthereumVerificationModal_module_css__WEBPACK_IMPORTED_MODULE_5___default().closeBtn),\n                    onClick: onClose,\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Werank/src/components/EthereumVerificationModal.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EthereumVerificationModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/EthereumVerificationModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PaymentModal.tsx":
/*!*****************************************!*\
  !*** ./src/components/PaymentModal.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useWriteContract.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js\");\n/* harmony import */ var _PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PaymentModal.module.css */ \"(ssr)/./src/components/PaymentModal.module.css\");\n/* harmony import */ var _PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Contract constants (defined outside component to avoid re-declaration)\nconst MONAD_TESTNET_ID = 10143;\nconst PAYMENT_AMOUNT = \"250000000000000000\" // 0.25 MON in wei\n;\nconst WALLET_CONNECT_CONTRACT = \"******************************************\";\n// Simplified Contract ABI for WerantWalletConnect - only essential functions\nconst WALLET_CONNECT_ABI = [\n    {\n        \"inputs\": [],\n        \"name\": \"connectWallet\",\n        \"outputs\": [],\n        \"stateMutability\": \"payable\",\n        \"type\": \"function\"\n    },\n    {\n        \"inputs\": [\n            {\n                \"internalType\": \"address\",\n                \"name\": \"wallet\",\n                \"type\": \"address\"\n            }\n        ],\n        \"name\": \"isWalletConnected\",\n        \"outputs\": [\n            {\n                \"internalType\": \"bool\",\n                \"name\": \"\",\n                \"type\": \"bool\"\n            }\n        ],\n        \"stateMutability\": \"view\",\n        \"type\": \"function\"\n    }\n];\nconst PaymentModal = ({ isOpen, onClose, onPaymentSuccess })=>{\n    const { address, isConnected } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitAccount)();\n    const { chainId } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitNetwork)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentStatus, setPaymentStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [txHash, setTxHash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { writeContract, data: hash, error } = (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.useWriteContract)();\n    const { isLoading: isConfirming, isSuccess: isConfirmed } = (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.useWaitForTransactionReceipt)({\n        hash\n    });\n    // Note: Contract verification removed to avoid initialization issues\n    // We'll verify the contract works when we make the actual payment\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConfirmed && hash) {\n            console.log(\"✅ Payment confirmed! Transaction hash:\", hash);\n            setPaymentStatus(\"success\");\n            setIsProcessing(false);\n            setTxHash(hash);\n            // Store payment status in localStorage\n            if (address) {\n                localStorage.setItem(`werant_paid_${address.toLowerCase()}`, \"true\");\n                console.log(\"\\uD83D\\uDCBE Payment status saved to localStorage\");\n            }\n            // Call success callback after a short delay to show success message\n            setTimeout(()=>{\n                console.log(\"\\uD83C\\uDF89 Payment process complete, calling success callback\");\n                onPaymentSuccess();\n                onClose();\n            }, 3000) // Increased delay to show success message longer\n            ;\n        }\n    }, [\n        isConfirmed,\n        hash,\n        address,\n        onPaymentSuccess,\n        onClose\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error) {\n            console.error(\"❌ Transaction error:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                name: error.name,\n                stack: error.stack\n            });\n            setPaymentStatus(\"error\");\n            setIsProcessing(false);\n        }\n    }, [\n        error\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (hash && !isConfirmed && !isConfirming) {\n            console.log(\"\\uD83D\\uDCE1 Transaction submitted, hash:\", hash);\n            console.log(\"⏳ Waiting for confirmation...\");\n        }\n    }, [\n        hash,\n        isConfirmed,\n        isConfirming\n    ]);\n    // Log payment amount for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDCB0 PAYMENT_AMOUNT:\", PAYMENT_AMOUNT, \"wei (0.25 MON)\");\n        console.log(\"\\uD83D\\uDCCD Contract Address:\", WALLET_CONNECT_CONTRACT);\n    }, []);\n    const handlePayment = async ()=>{\n        if (!isConnected || !address) {\n            alert(\"Please connect your wallet first\");\n            return;\n        }\n        // Check if user is on correct network\n        if (chainId !== MONAD_TESTNET_ID) {\n            alert(\"Please switch to Monad Testnet (Chain ID: 10143) to make payment\");\n            return;\n        }\n        try {\n            setIsProcessing(true);\n            setPaymentStatus(\"pending\");\n            console.log(\"\\uD83D\\uDD04 Starting payment process...\");\n            console.log(\"\\uD83D\\uDCCD Contract Address:\", WALLET_CONNECT_CONTRACT);\n            console.log(\"\\uD83D\\uDCB0 Payment Amount:\", PAYMENT_AMOUNT, \"wei (0.25 MON)\");\n            console.log(\"\\uD83D\\uDC64 User Address:\", address);\n            console.log(\"\\uD83C\\uDF10 Network Chain ID:\", chainId);\n            // Call connectWallet function on WerantWalletConnect contract\n            writeContract({\n                address: WALLET_CONNECT_CONTRACT,\n                abi: WALLET_CONNECT_ABI,\n                functionName: \"connectWallet\",\n                value: BigInt(PAYMENT_AMOUNT),\n                gas: BigInt(500000)\n            });\n            console.log(\"✅ Transaction submitted\");\n        } catch (err) {\n            console.error(\"❌ Payment failed:\", err);\n            console.error(\"Error details:\", {\n                message: err instanceof Error ? err.message : \"Unknown error\",\n                code: err?.code,\n                reason: err?.reason,\n                data: err?.data\n            });\n            setPaymentStatus(\"error\");\n            setIsProcessing(false);\n        }\n    };\n    const handleSkip = ()=>{\n        // For demo purposes, allow skipping payment\n        if (address) {\n            localStorage.setItem(`werant_paid_${address.toLowerCase()}`, \"true\");\n            console.log(\"⏭️ Payment skipped for demo purposes\");\n        }\n        onPaymentSuccess();\n        onClose();\n    };\n    const handleTestPayment = async ()=>{\n        // Alternative payment method - direct transfer to contract\n        if (!isConnected || !address) {\n            alert(\"Please connect your wallet first\");\n            return;\n        }\n        if (chainId !== MONAD_TESTNET_ID) {\n            alert(\"Please switch to Monad Testnet (Chain ID: 10143) to make payment\");\n            return;\n        }\n        try {\n            setIsProcessing(true);\n            setPaymentStatus(\"pending\");\n            console.log(\"\\uD83E\\uDDEA Testing direct transfer payment...\");\n            // Use the same connectWallet function but with different gas limit\n            writeContract({\n                address: WALLET_CONNECT_CONTRACT,\n                abi: WALLET_CONNECT_ABI,\n                functionName: \"connectWallet\",\n                value: BigInt(PAYMENT_AMOUNT),\n                gas: BigInt(300000)\n            });\n            console.log(\"✅ Test payment submitted\");\n        } catch (err) {\n            console.error(\"❌ Test payment failed:\", err);\n            setPaymentStatus(\"error\");\n            setIsProcessing(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().overlay),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"\\uD83C\\uDF89 Welcome to Werant!\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"One-time activation fee for new users\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().content),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().paymentInfo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().amountDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().amount),\n                                        children: \"0.25 MON\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().currency),\n                                        children: \"Monad Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().benefits),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"What you get:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Full access to Werant voting platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Participate in community governance\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Create and vote on proposals\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Access to leaderboard features\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, undefined),\n                            paymentStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn)} ${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().primary)}`,\n                                        onClick: handlePayment,\n                                        disabled: !isConnected || isProcessing,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                                children: \"\\uD83D\\uDCB3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Pay 0.25 MON\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn)} ${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondary)}`,\n                                        onClick: handleTestPayment,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                                children: \"\\uD83E\\uDDEA\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Test Payment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn)} ${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondary)}`,\n                                        onClick: handleSkip,\n                                        style: {\n                                            fontSize: \"14px\",\n                                            padding: \"12px 20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                                children: \"⏭️\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Skip for Demo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined),\n                            paymentStatus === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Processing payment...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isConfirming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Waiting for confirmation...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 34\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined),\n                            paymentStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().successIcon),\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Payment successful!\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Welcome to Werant! \\uD83C\\uDF89\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined),\n                            paymentStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorIcon),\n                                        children: \"❌\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Payment failed\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: \"14px\",\n                                            color: \"#ef4444\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: error?.message || \"Transaction failed. Please try again.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"12px\",\n                                            width: \"100%\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn)} ${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().primary)}`,\n                                                onClick: ()=>{\n                                                    setPaymentStatus(\"idle\");\n                                                    setIsProcessing(false);\n                                                },\n                                                children: \"Try Again\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn)} ${(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondary)}`,\n                                                onClick: handleTestPayment,\n                                                children: \"\\uD83E\\uDDEA Try Test Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined),\n                paymentStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeBtn),\n                    onClick: onClose,\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PaymentModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9QYXltZW50TW9kYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQzZCO0FBQ0Y7QUFDeEI7QUFROUMseUVBQXlFO0FBQ3pFLE1BQU1PLG1CQUFtQjtBQUN6QixNQUFNQyxpQkFBaUIscUJBQXFCLGtCQUFrQjs7QUFDOUQsTUFBTUMsMEJBQTBCO0FBRWhDLDZFQUE2RTtBQUM3RSxNQUFNQyxxQkFBcUI7SUFDekI7UUFDRSxVQUFVLEVBQUU7UUFDWixRQUFRO1FBQ1IsV0FBVyxFQUFFO1FBQ2IsbUJBQW1CO1FBQ25CLFFBQVE7SUFDVjtJQUNBO1FBQ0UsVUFBVTtZQUFDO2dCQUFDLGdCQUFnQjtnQkFBVyxRQUFRO2dCQUFVLFFBQVE7WUFBUztTQUFFO1FBQzVFLFFBQVE7UUFDUixXQUFXO1lBQUM7Z0JBQUMsZ0JBQWdCO2dCQUFRLFFBQVE7Z0JBQUksUUFBUTtZQUFNO1NBQUU7UUFDakUsbUJBQW1CO1FBQ25CLFFBQVE7SUFDVjtDQUNEO0FBRUQsTUFBTUMsZUFBNEMsQ0FBQyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsZ0JBQWdCLEVBQUU7SUFDdEYsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLFdBQVcsRUFBRSxHQUFHZCxxRUFBZ0JBO0lBQ2pELE1BQU0sRUFBRWUsT0FBTyxFQUFFLEdBQUdkLHFFQUFnQkE7SUFDcEMsTUFBTSxDQUFDZSxjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ29CLGVBQWVDLGlCQUFpQixHQUFHckIsK0NBQVFBLENBQTJDO0lBQzdGLE1BQU0sQ0FBQ3NCLFFBQVFDLFVBQVUsR0FBR3ZCLCtDQUFRQSxDQUFTO0lBRTdDLE1BQU0sRUFBRXdCLGFBQWEsRUFBRUMsTUFBTUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR3ZCLHVEQUFnQkE7SUFFN0QsTUFBTSxFQUFFd0IsV0FBV0MsWUFBWSxFQUFFQyxXQUFXQyxXQUFXLEVBQUUsR0FBRzFCLG1FQUE0QkEsQ0FBQztRQUN2RnFCO0lBQ0Y7SUFFQSxxRUFBcUU7SUFDckUsa0VBQWtFO0lBRWxFekIsZ0RBQVNBLENBQUM7UUFDUixJQUFJOEIsZUFBZUwsTUFBTTtZQUN2Qk0sUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ1A7WUFDdERMLGlCQUFpQjtZQUNqQkYsZ0JBQWdCO1lBQ2hCSSxVQUFVRztZQUVWLHVDQUF1QztZQUN2QyxJQUFJWCxTQUFTO2dCQUNYbUIsYUFBYUMsT0FBTyxDQUFDLENBQUMsWUFBWSxFQUFFcEIsUUFBUXFCLFdBQVcsR0FBRyxDQUFDLEVBQUU7Z0JBQzdESixRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLG9FQUFvRTtZQUNwRUksV0FBVztnQkFDVEwsUUFBUUMsR0FBRyxDQUFDO2dCQUNabkI7Z0JBQ0FEO1lBQ0YsR0FBRyxNQUFNLGlEQUFpRDs7UUFDNUQ7SUFDRixHQUFHO1FBQUNrQjtRQUFhTDtRQUFNWDtRQUFTRDtRQUFrQkQ7S0FBUTtJQUUxRFosZ0RBQVNBLENBQUM7UUFDUixJQUFJMEIsT0FBTztZQUNUSyxRQUFRTCxLQUFLLENBQUMsd0JBQXdCQTtZQUN0Q0ssUUFBUUwsS0FBSyxDQUFDLGtCQUFrQjtnQkFDOUJXLFNBQVNYLE1BQU1XLE9BQU87Z0JBQ3RCQyxNQUFNWixNQUFNWSxJQUFJO2dCQUNoQkMsT0FBT2IsTUFBTWEsS0FBSztZQUNwQjtZQUNBbkIsaUJBQWlCO1lBQ2pCRixnQkFBZ0I7UUFDbEI7SUFDRixHQUFHO1FBQUNRO0tBQU07SUFFVjFCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXlCLFFBQVEsQ0FBQ0ssZUFBZSxDQUFDRixjQUFjO1lBQ3pDRyxRQUFRQyxHQUFHLENBQUMsNkNBQW1DUDtZQUMvQ00sUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNQO1FBQU1LO1FBQWFGO0tBQWE7SUFFcEMsbUNBQW1DO0lBQ25DNUIsZ0RBQVNBLENBQUM7UUFDUitCLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBc0J6QixnQkFBZ0I7UUFDbER3QixRQUFRQyxHQUFHLENBQUMsa0NBQXdCeEI7SUFDdEMsR0FBRyxFQUFFO0lBRUwsTUFBTWdDLGdCQUFnQjtRQUNwQixJQUFJLENBQUN6QixlQUFlLENBQUNELFNBQVM7WUFDNUIyQixNQUFNO1lBQ047UUFDRjtRQUVBLHNDQUFzQztRQUN0QyxJQUFJekIsWUFBWVYsa0JBQWtCO1lBQ2hDbUMsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJO1lBQ0Z2QixnQkFBZ0I7WUFDaEJFLGlCQUFpQjtZQUVqQlcsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxrQ0FBd0J4QjtZQUNwQ3VCLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBc0J6QixnQkFBZ0I7WUFDbER3QixRQUFRQyxHQUFHLENBQUMsOEJBQW9CbEI7WUFDaENpQixRQUFRQyxHQUFHLENBQUMsa0NBQXdCaEI7WUFFcEMsOERBQThEO1lBQzlETyxjQUFjO2dCQUNaVCxTQUFTTjtnQkFDVGtDLEtBQUtqQztnQkFDTGtDLGNBQWM7Z0JBQ2RDLE9BQU9DLE9BQU90QztnQkFDZHVDLEtBQUtELE9BQU87WUFDZDtZQUVBZCxRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9lLEtBQUs7WUFDWmhCLFFBQVFMLEtBQUssQ0FBQyxxQkFBcUJxQjtZQUNuQ2hCLFFBQVFMLEtBQUssQ0FBQyxrQkFBa0I7Z0JBQzlCVyxTQUFTVSxlQUFlQyxRQUFRRCxJQUFJVixPQUFPLEdBQUc7Z0JBQzlDWSxNQUFPRixLQUFhRTtnQkFDcEJDLFFBQVNILEtBQWFHO2dCQUN0QjFCLE1BQU91QixLQUFhdkI7WUFDdEI7WUFDQUosaUJBQWlCO1lBQ2pCRixnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU1pQyxhQUFhO1FBQ2pCLDRDQUE0QztRQUM1QyxJQUFJckMsU0FBUztZQUNYbUIsYUFBYUMsT0FBTyxDQUFDLENBQUMsWUFBWSxFQUFFcEIsUUFBUXFCLFdBQVcsR0FBRyxDQUFDLEVBQUU7WUFDN0RKLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBQ0FuQjtRQUNBRDtJQUNGO0lBRUEsTUFBTXdDLG9CQUFvQjtRQUN4QiwyREFBMkQ7UUFDM0QsSUFBSSxDQUFDckMsZUFBZSxDQUFDRCxTQUFTO1lBQzVCMkIsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJekIsWUFBWVYsa0JBQWtCO1lBQ2hDbUMsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJO1lBQ0Z2QixnQkFBZ0I7WUFDaEJFLGlCQUFpQjtZQUVqQlcsUUFBUUMsR0FBRyxDQUFDO1lBRVosbUVBQW1FO1lBQ25FVCxjQUFjO2dCQUNaVCxTQUFTTjtnQkFDVGtDLEtBQUtqQztnQkFDTGtDLGNBQWM7Z0JBQ2RDLE9BQU9DLE9BQU90QztnQkFDZHVDLEtBQUtELE9BQU87WUFDZDtZQUVBZCxRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9lLEtBQUs7WUFDWmhCLFFBQVFMLEtBQUssQ0FBQywwQkFBMEJxQjtZQUN4QzNCLGlCQUFpQjtZQUNqQkYsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJLENBQUNQLFFBQVEsT0FBTztJQUVwQixxQkFDRSw4REFBQzBDO1FBQUlDLFdBQVdqRCx5RUFBYztrQkFDNUIsNEVBQUNnRDtZQUFJQyxXQUFXakQsdUVBQVk7OzhCQUMxQiw4REFBQ2dEO29CQUFJQyxXQUFXakQsd0VBQWE7O3NDQUMzQiw4REFBQ3FEO3NDQUFHOzs7Ozs7c0NBQ0osOERBQUNDO3NDQUFFOzs7Ozs7Ozs7Ozs7OEJBR0wsOERBQUNOO29CQUFJQyxXQUFXakQseUVBQWM7OEJBQzVCLDRFQUFDZ0Q7d0JBQUlDLFdBQVdqRCw2RUFBa0I7OzBDQUNoQyw4REFBQ2dEO2dDQUFJQyxXQUFXakQsK0VBQW9COztrREFDbEMsOERBQUMwRDt3Q0FBS1QsV0FBV2pELHdFQUFhO2tEQUFFOzs7Ozs7a0RBQ2hDLDhEQUFDMEQ7d0NBQUtULFdBQVdqRCwwRUFBZTtrREFBRTs7Ozs7Ozs7Ozs7OzBDQUdwQyw4REFBQ2dEO2dDQUFJQyxXQUFXakQsMEVBQWU7O2tEQUM3Qiw4REFBQzhEO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNDOzswREFDQyw4REFBQ0M7MERBQUc7Ozs7OzswREFDSiw4REFBQ0E7MERBQUc7Ozs7OzswREFDSiw4REFBQ0E7MERBQUc7Ozs7OzswREFDSiw4REFBQ0E7MERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFLUGxELGtCQUFrQix3QkFDakIsOERBQUNrQztnQ0FBSUMsV0FBV2pELHlFQUFjOztrREFDNUIsOERBQUNrRTt3Q0FDQ2pCLFdBQVcsQ0FBQyxFQUFFakQsd0VBQWEsQ0FBQyxDQUFDLEVBQUVBLHlFQUFjLENBQUMsQ0FBQzt3Q0FDL0NxRSxTQUFTbEM7d0NBQ1RtQyxVQUFVLENBQUM1RCxlQUFlRTs7MERBRTFCLDhEQUFDOEM7Z0RBQUtULFdBQVdqRCxzRUFBVzswREFBRTs7Ozs7OzRDQUFTOzs7Ozs7O2tEQUl6Qyw4REFBQ2tFO3dDQUNDakIsV0FBVyxDQUFDLEVBQUVqRCx3RUFBYSxDQUFDLENBQUMsRUFBRUEsMkVBQWdCLENBQUMsQ0FBQzt3Q0FDakRxRSxTQUFTdEI7OzBEQUVULDhEQUFDVztnREFBS1QsV0FBV2pELHNFQUFXOzBEQUFFOzs7Ozs7NENBQVM7Ozs7Ozs7a0RBSXpDLDhEQUFDa0U7d0NBQ0NqQixXQUFXLENBQUMsRUFBRWpELHdFQUFhLENBQUMsQ0FBQyxFQUFFQSwyRUFBZ0IsQ0FBQyxDQUFDO3dDQUNqRHFFLFNBQVN2Qjt3Q0FDVDJCLE9BQU87NENBQUVDLFVBQVU7NENBQVFDLFNBQVM7d0NBQVk7OzBEQUVoRCw4REFBQ2pCO2dEQUFLVCxXQUFXakQsc0VBQVc7MERBQUU7Ozs7Ozs0Q0FBUzs7Ozs7Ozs7Ozs7Ozs0QkFNNUNjLGtCQUFrQiwyQkFDakIsOERBQUNrQztnQ0FBSUMsV0FBV2pELCtFQUFvQjs7a0RBQ2xDLDhEQUFDZ0Q7d0NBQUlDLFdBQVdqRCx5RUFBYzs7Ozs7O2tEQUM5Qiw4REFBQ3NEO2tEQUFFOzs7Ozs7b0NBQ0YvQiw4QkFBZ0IsOERBQUMrQjtrREFBRTs7Ozs7Ozs7Ozs7OzRCQUl2QnhDLGtCQUFrQiwyQkFDakIsOERBQUNrQztnQ0FBSUMsV0FBV2pELCtFQUFvQjs7a0RBQ2xDLDhEQUFDZ0Q7d0NBQUlDLFdBQVdqRCw2RUFBa0I7a0RBQUU7Ozs7OztrREFDcEMsOERBQUNzRDtrREFBRTs7Ozs7O2tEQUNILDhEQUFDQTtrREFBRTs7Ozs7Ozs7Ozs7OzRCQUlOeEMsa0JBQWtCLHlCQUNqQiw4REFBQ2tDO2dDQUFJQyxXQUFXakQsK0VBQW9COztrREFDbEMsOERBQUNnRDt3Q0FBSUMsV0FBV2pELDJFQUFnQjtrREFBRTs7Ozs7O2tEQUNsQyw4REFBQ3NEO2tEQUFFOzs7Ozs7a0RBQ0gsOERBQUNBO3dDQUFFbUIsT0FBTzs0Q0FBRUMsVUFBVTs0Q0FBUU0sT0FBTzs0Q0FBV0MsY0FBYzt3Q0FBTztrREFDbEU1RCxPQUFPVyxXQUFXOzs7Ozs7a0RBR3JCLDhEQUFDZ0I7d0NBQUl5QixPQUFPOzRDQUFFUyxTQUFTOzRDQUFRQyxlQUFlOzRDQUFVQyxLQUFLOzRDQUFRQyxPQUFPO3dDQUFPOzswREFDakYsOERBQUNuQjtnREFDQ2pCLFdBQVcsQ0FBQyxFQUFFakQsd0VBQWEsQ0FBQyxDQUFDLEVBQUVBLHlFQUFjLENBQUMsQ0FBQztnREFDL0NxRSxTQUFTO29EQUNQdEQsaUJBQWlCO29EQUNqQkYsZ0JBQWdCO2dEQUNsQjswREFDRDs7Ozs7OzBEQUlELDhEQUFDcUQ7Z0RBQ0NqQixXQUFXLENBQUMsRUFBRWpELHdFQUFhLENBQUMsQ0FBQyxFQUFFQSwyRUFBZ0IsQ0FBQyxDQUFDO2dEQUNqRHFFLFNBQVN0QjswREFDVjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBU1ZqQyxrQkFBa0Isd0JBQ2pCLDhEQUFDb0Q7b0JBQ0NqQixXQUFXakQsMEVBQWU7b0JBQzFCcUUsU0FBUzlEOzhCQUNWOzs7Ozs7Ozs7Ozs7Ozs7OztBQU9YO0FBRUEsaUVBQWVGLFlBQVlBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL3NyYy9jb21wb25lbnRzL1BheW1lbnRNb2RhbC50c3g/ZWY0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlQXBwS2l0QWNjb3VudCwgdXNlQXBwS2l0TmV0d29yayB9IGZyb20gJ0ByZW93bi9hcHBraXQvcmVhY3QnXG5pbXBvcnQgeyB1c2VXcml0ZUNvbnRyYWN0LCB1c2VXYWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0IH0gZnJvbSAnd2FnbWknXG5pbXBvcnQgc3R5bGVzIGZyb20gJy4vUGF5bWVudE1vZGFsLm1vZHVsZS5jc3MnXG5cbmludGVyZmFjZSBQYXltZW50TW9kYWxQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhblxuICBvbkNsb3NlOiAoKSA9PiB2b2lkXG4gIG9uUGF5bWVudFN1Y2Nlc3M6ICgpID0+IHZvaWRcbn1cblxuLy8gQ29udHJhY3QgY29uc3RhbnRzIChkZWZpbmVkIG91dHNpZGUgY29tcG9uZW50IHRvIGF2b2lkIHJlLWRlY2xhcmF0aW9uKVxuY29uc3QgTU9OQURfVEVTVE5FVF9JRCA9IDEwMTQzXG5jb25zdCBQQVlNRU5UX0FNT1VOVCA9ICcyNTAwMDAwMDAwMDAwMDAwMDAnIC8vIDAuMjUgTU9OIGluIHdlaVxuY29uc3QgV0FMTEVUX0NPTk5FQ1RfQ09OVFJBQ1QgPSAnMHg3RGU5ODgxNjg4YTI0MkEyMTRlZWFCMEM4NWU5RTRlMTcxZTBhMDliJ1xuXG4vLyBTaW1wbGlmaWVkIENvbnRyYWN0IEFCSSBmb3IgV2VyYW50V2FsbGV0Q29ubmVjdCAtIG9ubHkgZXNzZW50aWFsIGZ1bmN0aW9uc1xuY29uc3QgV0FMTEVUX0NPTk5FQ1RfQUJJID0gW1xuICB7XG4gICAgXCJpbnB1dHNcIjogW10sXG4gICAgXCJuYW1lXCI6IFwiY29ubmVjdFdhbGxldFwiLFxuICAgIFwib3V0cHV0c1wiOiBbXSxcbiAgICBcInN0YXRlTXV0YWJpbGl0eVwiOiBcInBheWFibGVcIixcbiAgICBcInR5cGVcIjogXCJmdW5jdGlvblwiXG4gIH0sXG4gIHtcbiAgICBcImlucHV0c1wiOiBbe1wiaW50ZXJuYWxUeXBlXCI6IFwiYWRkcmVzc1wiLCBcIm5hbWVcIjogXCJ3YWxsZXRcIiwgXCJ0eXBlXCI6IFwiYWRkcmVzc1wifV0sXG4gICAgXCJuYW1lXCI6IFwiaXNXYWxsZXRDb25uZWN0ZWRcIixcbiAgICBcIm91dHB1dHNcIjogW3tcImludGVybmFsVHlwZVwiOiBcImJvb2xcIiwgXCJuYW1lXCI6IFwiXCIsIFwidHlwZVwiOiBcImJvb2xcIn1dLFxuICAgIFwic3RhdGVNdXRhYmlsaXR5XCI6IFwidmlld1wiLFxuICAgIFwidHlwZVwiOiBcImZ1bmN0aW9uXCJcbiAgfVxuXSBhcyBjb25zdFxuXG5jb25zdCBQYXltZW50TW9kYWw6IFJlYWN0LkZDPFBheW1lbnRNb2RhbFByb3BzPiA9ICh7IGlzT3Blbiwgb25DbG9zZSwgb25QYXltZW50U3VjY2VzcyB9KSA9PiB7XG4gIGNvbnN0IHsgYWRkcmVzcywgaXNDb25uZWN0ZWQgfSA9IHVzZUFwcEtpdEFjY291bnQoKVxuICBjb25zdCB7IGNoYWluSWQgfSA9IHVzZUFwcEtpdE5ldHdvcmsoKVxuICBjb25zdCBbaXNQcm9jZXNzaW5nLCBzZXRJc1Byb2Nlc3NpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtwYXltZW50U3RhdHVzLCBzZXRQYXltZW50U3RhdHVzXSA9IHVzZVN0YXRlPCdpZGxlJyB8ICdwZW5kaW5nJyB8ICdzdWNjZXNzJyB8ICdlcnJvcic+KCdpZGxlJylcbiAgY29uc3QgW3R4SGFzaCwgc2V0VHhIYXNoXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpXG5cbiAgY29uc3QgeyB3cml0ZUNvbnRyYWN0LCBkYXRhOiBoYXNoLCBlcnJvciB9ID0gdXNlV3JpdGVDb250cmFjdCgpXG5cbiAgY29uc3QgeyBpc0xvYWRpbmc6IGlzQ29uZmlybWluZywgaXNTdWNjZXNzOiBpc0NvbmZpcm1lZCB9ID0gdXNlV2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdCh7XG4gICAgaGFzaCxcbiAgfSlcblxuICAvLyBOb3RlOiBDb250cmFjdCB2ZXJpZmljYXRpb24gcmVtb3ZlZCB0byBhdm9pZCBpbml0aWFsaXphdGlvbiBpc3N1ZXNcbiAgLy8gV2UnbGwgdmVyaWZ5IHRoZSBjb250cmFjdCB3b3JrcyB3aGVuIHdlIG1ha2UgdGhlIGFjdHVhbCBwYXltZW50XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNDb25maXJtZWQgJiYgaGFzaCkge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBQYXltZW50IGNvbmZpcm1lZCEgVHJhbnNhY3Rpb24gaGFzaDonLCBoYXNoKVxuICAgICAgc2V0UGF5bWVudFN0YXR1cygnc3VjY2VzcycpXG4gICAgICBzZXRJc1Byb2Nlc3NpbmcoZmFsc2UpXG4gICAgICBzZXRUeEhhc2goaGFzaClcblxuICAgICAgLy8gU3RvcmUgcGF5bWVudCBzdGF0dXMgaW4gbG9jYWxTdG9yYWdlXG4gICAgICBpZiAoYWRkcmVzcykge1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShgd2VyYW50X3BhaWRfJHthZGRyZXNzLnRvTG93ZXJDYXNlKCl9YCwgJ3RydWUnKVxuICAgICAgICBjb25zb2xlLmxvZygn8J+SviBQYXltZW50IHN0YXR1cyBzYXZlZCB0byBsb2NhbFN0b3JhZ2UnKVxuICAgICAgfVxuXG4gICAgICAvLyBDYWxsIHN1Y2Nlc3MgY2FsbGJhY2sgYWZ0ZXIgYSBzaG9ydCBkZWxheSB0byBzaG93IHN1Y2Nlc3MgbWVzc2FnZVxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn46JIFBheW1lbnQgcHJvY2VzcyBjb21wbGV0ZSwgY2FsbGluZyBzdWNjZXNzIGNhbGxiYWNrJylcbiAgICAgICAgb25QYXltZW50U3VjY2VzcygpXG4gICAgICAgIG9uQ2xvc2UoKVxuICAgICAgfSwgMzAwMCkgLy8gSW5jcmVhc2VkIGRlbGF5IHRvIHNob3cgc3VjY2VzcyBtZXNzYWdlIGxvbmdlclxuICAgIH1cbiAgfSwgW2lzQ29uZmlybWVkLCBoYXNoLCBhZGRyZXNzLCBvblBheW1lbnRTdWNjZXNzLCBvbkNsb3NlXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIFRyYW5zYWN0aW9uIGVycm9yOicsIGVycm9yKVxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGV0YWlsczonLCB7XG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIG5hbWU6IGVycm9yLm5hbWUsXG4gICAgICAgIHN0YWNrOiBlcnJvci5zdGFja1xuICAgICAgfSlcbiAgICAgIHNldFBheW1lbnRTdGF0dXMoJ2Vycm9yJylcbiAgICAgIHNldElzUHJvY2Vzc2luZyhmYWxzZSlcbiAgICB9XG4gIH0sIFtlcnJvcl0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaGFzaCAmJiAhaXNDb25maXJtZWQgJiYgIWlzQ29uZmlybWluZykge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6EgVHJhbnNhY3Rpb24gc3VibWl0dGVkLCBoYXNoOicsIGhhc2gpXG4gICAgICBjb25zb2xlLmxvZygn4o+zIFdhaXRpbmcgZm9yIGNvbmZpcm1hdGlvbi4uLicpXG4gICAgfVxuICB9LCBbaGFzaCwgaXNDb25maXJtZWQsIGlzQ29uZmlybWluZ10pXG5cbiAgLy8gTG9nIHBheW1lbnQgYW1vdW50IGZvciBkZWJ1Z2dpbmdcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+SsCBQQVlNRU5UX0FNT1VOVDonLCBQQVlNRU5UX0FNT1VOVCwgJ3dlaSAoMC4yNSBNT04pJylcbiAgICBjb25zb2xlLmxvZygn8J+TjSBDb250cmFjdCBBZGRyZXNzOicsIFdBTExFVF9DT05ORUNUX0NPTlRSQUNUKVxuICB9LCBbXSlcblxuICBjb25zdCBoYW5kbGVQYXltZW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaXNDb25uZWN0ZWQgfHwgIWFkZHJlc3MpIHtcbiAgICAgIGFsZXJ0KCdQbGVhc2UgY29ubmVjdCB5b3VyIHdhbGxldCBmaXJzdCcpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIG9uIGNvcnJlY3QgbmV0d29ya1xuICAgIGlmIChjaGFpbklkICE9PSBNT05BRF9URVNUTkVUX0lEKSB7XG4gICAgICBhbGVydCgnUGxlYXNlIHN3aXRjaCB0byBNb25hZCBUZXN0bmV0IChDaGFpbiBJRDogMTAxNDMpIHRvIG1ha2UgcGF5bWVudCcpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNQcm9jZXNzaW5nKHRydWUpXG4gICAgICBzZXRQYXltZW50U3RhdHVzKCdwZW5kaW5nJylcblxuICAgICAgY29uc29sZS5sb2coJ/CflIQgU3RhcnRpbmcgcGF5bWVudCBwcm9jZXNzLi4uJylcbiAgICAgIGNvbnNvbGUubG9nKCfwn5ONIENvbnRyYWN0IEFkZHJlc3M6JywgV0FMTEVUX0NPTk5FQ1RfQ09OVFJBQ1QpXG4gICAgICBjb25zb2xlLmxvZygn8J+SsCBQYXltZW50IEFtb3VudDonLCBQQVlNRU5UX0FNT1VOVCwgJ3dlaSAoMC4yNSBNT04pJylcbiAgICAgIGNvbnNvbGUubG9nKCfwn5GkIFVzZXIgQWRkcmVzczonLCBhZGRyZXNzKVxuICAgICAgY29uc29sZS5sb2coJ/CfjJAgTmV0d29yayBDaGFpbiBJRDonLCBjaGFpbklkKVxuXG4gICAgICAvLyBDYWxsIGNvbm5lY3RXYWxsZXQgZnVuY3Rpb24gb24gV2VyYW50V2FsbGV0Q29ubmVjdCBjb250cmFjdFxuICAgICAgd3JpdGVDb250cmFjdCh7XG4gICAgICAgIGFkZHJlc3M6IFdBTExFVF9DT05ORUNUX0NPTlRSQUNUIGFzIGAweCR7c3RyaW5nfWAsXG4gICAgICAgIGFiaTogV0FMTEVUX0NPTk5FQ1RfQUJJLFxuICAgICAgICBmdW5jdGlvbk5hbWU6ICdjb25uZWN0V2FsbGV0JyxcbiAgICAgICAgdmFsdWU6IEJpZ0ludChQQVlNRU5UX0FNT1VOVCksIC8vIDAuMjUgTU9OXG4gICAgICAgIGdhczogQmlnSW50KDUwMDAwMCksIC8vIFNldCBnYXMgbGltaXRcbiAgICAgIH0pXG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgVHJhbnNhY3Rpb24gc3VibWl0dGVkJylcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBQYXltZW50IGZhaWxlZDonLCBlcnIpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZXRhaWxzOicsIHtcbiAgICAgICAgbWVzc2FnZTogZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyxcbiAgICAgICAgY29kZTogKGVyciBhcyBhbnkpPy5jb2RlLFxuICAgICAgICByZWFzb246IChlcnIgYXMgYW55KT8ucmVhc29uLFxuICAgICAgICBkYXRhOiAoZXJyIGFzIGFueSk/LmRhdGFcbiAgICAgIH0pXG4gICAgICBzZXRQYXltZW50U3RhdHVzKCdlcnJvcicpXG4gICAgICBzZXRJc1Byb2Nlc3NpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2tpcCA9ICgpID0+IHtcbiAgICAvLyBGb3IgZGVtbyBwdXJwb3NlcywgYWxsb3cgc2tpcHBpbmcgcGF5bWVudFxuICAgIGlmIChhZGRyZXNzKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShgd2VyYW50X3BhaWRfJHthZGRyZXNzLnRvTG93ZXJDYXNlKCl9YCwgJ3RydWUnKVxuICAgICAgY29uc29sZS5sb2coJ+KPre+4jyBQYXltZW50IHNraXBwZWQgZm9yIGRlbW8gcHVycG9zZXMnKVxuICAgIH1cbiAgICBvblBheW1lbnRTdWNjZXNzKClcbiAgICBvbkNsb3NlKClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRlc3RQYXltZW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIEFsdGVybmF0aXZlIHBheW1lbnQgbWV0aG9kIC0gZGlyZWN0IHRyYW5zZmVyIHRvIGNvbnRyYWN0XG4gICAgaWYgKCFpc0Nvbm5lY3RlZCB8fCAhYWRkcmVzcykge1xuICAgICAgYWxlcnQoJ1BsZWFzZSBjb25uZWN0IHlvdXIgd2FsbGV0IGZpcnN0JylcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChjaGFpbklkICE9PSBNT05BRF9URVNUTkVUX0lEKSB7XG4gICAgICBhbGVydCgnUGxlYXNlIHN3aXRjaCB0byBNb25hZCBUZXN0bmV0IChDaGFpbiBJRDogMTAxNDMpIHRvIG1ha2UgcGF5bWVudCcpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNQcm9jZXNzaW5nKHRydWUpXG4gICAgICBzZXRQYXltZW50U3RhdHVzKCdwZW5kaW5nJylcblxuICAgICAgY29uc29sZS5sb2coJ/Cfp6ogVGVzdGluZyBkaXJlY3QgdHJhbnNmZXIgcGF5bWVudC4uLicpXG5cbiAgICAgIC8vIFVzZSB0aGUgc2FtZSBjb25uZWN0V2FsbGV0IGZ1bmN0aW9uIGJ1dCB3aXRoIGRpZmZlcmVudCBnYXMgbGltaXRcbiAgICAgIHdyaXRlQ29udHJhY3Qoe1xuICAgICAgICBhZGRyZXNzOiBXQUxMRVRfQ09OTkVDVF9DT05UUkFDVCBhcyBgMHgke3N0cmluZ31gLFxuICAgICAgICBhYmk6IFdBTExFVF9DT05ORUNUX0FCSSxcbiAgICAgICAgZnVuY3Rpb25OYW1lOiAnY29ubmVjdFdhbGxldCcsXG4gICAgICAgIHZhbHVlOiBCaWdJbnQoUEFZTUVOVF9BTU9VTlQpLFxuICAgICAgICBnYXM6IEJpZ0ludCgzMDAwMDApLCAvLyBMb3dlciBnYXMgbGltaXQgZm9yIHRlc3RcbiAgICAgIH0pXG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgVGVzdCBwYXltZW50IHN1Ym1pdHRlZCcpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgVGVzdCBwYXltZW50IGZhaWxlZDonLCBlcnIpXG4gICAgICBzZXRQYXltZW50U3RhdHVzKCdlcnJvcicpXG4gICAgICBzZXRJc1Byb2Nlc3NpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgaWYgKCFpc09wZW4pIHJldHVybiBudWxsXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm92ZXJsYXl9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5tb2RhbH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuaGVhZGVyfT5cbiAgICAgICAgICA8aDI+8J+OiSBXZWxjb21lIHRvIFdlcmFudCE8L2gyPlxuICAgICAgICAgIDxwPk9uZS10aW1lIGFjdGl2YXRpb24gZmVlIGZvciBuZXcgdXNlcnM8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuY29udGVudH0+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5wYXltZW50SW5mb30+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmFtb3VudERpc3BsYXl9PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5hbW91bnR9PjAuMjUgTU9OPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5jdXJyZW5jeX0+TW9uYWQgVGVzdG5ldDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmJlbmVmaXRzfT5cbiAgICAgICAgICAgICAgPGgzPldoYXQgeW91IGdldDo8L2gzPlxuICAgICAgICAgICAgICA8dWw+XG4gICAgICAgICAgICAgICAgPGxpPuKchSBGdWxsIGFjY2VzcyB0byBXZXJhbnQgdm90aW5nIHBsYXRmb3JtPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4pyFIFBhcnRpY2lwYXRlIGluIGNvbW11bml0eSBnb3Zlcm5hbmNlPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4pyFIENyZWF0ZSBhbmQgdm90ZSBvbiBwcm9wb3NhbHM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7inIUgQWNjZXNzIHRvIGxlYWRlcmJvYXJkIGZlYXR1cmVzPC9saT5cbiAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7cGF5bWVudFN0YXR1cyA9PT0gJ2lkbGUnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5hY3Rpb25zfT5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtzdHlsZXMucGF5QnRufSAke3N0eWxlcy5wcmltYXJ5fWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQYXltZW50fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFpc0Nvbm5lY3RlZCB8fCBpc1Byb2Nlc3Npbmd9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuaWNvbn0+8J+Sszwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIFBheSAwLjI1IE1PTlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7c3R5bGVzLnBheUJ0bn0gJHtzdHlsZXMuc2Vjb25kYXJ5fWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVUZXN0UGF5bWVudH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5pY29ufT7wn6eqPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgVGVzdCBQYXltZW50XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5wYXlCdG59ICR7c3R5bGVzLnNlY29uZGFyeX1gfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2tpcH1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGZvbnRTaXplOiAnMTRweCcsIHBhZGRpbmc6ICcxMnB4IDIwcHgnIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuaWNvbn0+4o+t77iPPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgU2tpcCBmb3IgRGVtb1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtwYXltZW50U3RhdHVzID09PSAncGVuZGluZycgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnN0YXR1c0Rpc3BsYXl9PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuc3Bpbm5lcn0+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHA+UHJvY2Vzc2luZyBwYXltZW50Li4uPC9wPlxuICAgICAgICAgICAgICAgIHtpc0NvbmZpcm1pbmcgJiYgPHA+V2FpdGluZyBmb3IgY29uZmlybWF0aW9uLi4uPC9wPn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cGF5bWVudFN0YXR1cyA9PT0gJ3N1Y2Nlc3MnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5zdGF0dXNEaXNwbGF5fT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnN1Y2Nlc3NJY29ufT7inIU8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cD5QYXltZW50IHN1Y2Nlc3NmdWwhPC9wPlxuICAgICAgICAgICAgICAgIDxwPldlbGNvbWUgdG8gV2VyYW50ISDwn46JPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtwYXltZW50U3RhdHVzID09PSAnZXJyb3InICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5zdGF0dXNEaXNwbGF5fT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmVycm9ySWNvbn0+4p2MPC9kaXY+XG4gICAgICAgICAgICAgICAgPHA+UGF5bWVudCBmYWlsZWQ8L3A+XG4gICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgZm9udFNpemU6ICcxNHB4JywgY29sb3I6ICcjZWY0NDQ0JywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3I/Lm1lc3NhZ2UgfHwgJ1RyYW5zYWN0aW9uIGZhaWxlZC4gUGxlYXNlIHRyeSBhZ2Fpbi4nfVxuICAgICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJywgZ2FwOiAnMTJweCcsIHdpZHRoOiAnMTAwJScgfX0+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7c3R5bGVzLnBheUJ0bn0gJHtzdHlsZXMucHJpbWFyeX1gfVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0UGF5bWVudFN0YXR1cygnaWRsZScpXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SXNQcm9jZXNzaW5nKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBUcnkgQWdhaW5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7c3R5bGVzLnBheUJ0bn0gJHtzdHlsZXMuc2Vjb25kYXJ5fWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVRlc3RQYXltZW50fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDwn6eqIFRyeSBUZXN0IFBheW1lbnRcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHtwYXltZW50U3RhdHVzID09PSAnaWRsZScgJiYgKFxuICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlcy5jbG9zZUJ0bn1cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgw5dcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IFBheW1lbnRNb2RhbFxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXBwS2l0QWNjb3VudCIsInVzZUFwcEtpdE5ldHdvcmsiLCJ1c2VXcml0ZUNvbnRyYWN0IiwidXNlV2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdCIsInN0eWxlcyIsIk1PTkFEX1RFU1RORVRfSUQiLCJQQVlNRU5UX0FNT1VOVCIsIldBTExFVF9DT05ORUNUX0NPTlRSQUNUIiwiV0FMTEVUX0NPTk5FQ1RfQUJJIiwiUGF5bWVudE1vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uUGF5bWVudFN1Y2Nlc3MiLCJhZGRyZXNzIiwiaXNDb25uZWN0ZWQiLCJjaGFpbklkIiwiaXNQcm9jZXNzaW5nIiwic2V0SXNQcm9jZXNzaW5nIiwicGF5bWVudFN0YXR1cyIsInNldFBheW1lbnRTdGF0dXMiLCJ0eEhhc2giLCJzZXRUeEhhc2giLCJ3cml0ZUNvbnRyYWN0IiwiZGF0YSIsImhhc2giLCJlcnJvciIsImlzTG9hZGluZyIsImlzQ29uZmlybWluZyIsImlzU3VjY2VzcyIsImlzQ29uZmlybWVkIiwiY29uc29sZSIsImxvZyIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJ0b0xvd2VyQ2FzZSIsInNldFRpbWVvdXQiLCJtZXNzYWdlIiwibmFtZSIsInN0YWNrIiwiaGFuZGxlUGF5bWVudCIsImFsZXJ0IiwiYWJpIiwiZnVuY3Rpb25OYW1lIiwidmFsdWUiLCJCaWdJbnQiLCJnYXMiLCJlcnIiLCJFcnJvciIsImNvZGUiLCJyZWFzb24iLCJoYW5kbGVTa2lwIiwiaGFuZGxlVGVzdFBheW1lbnQiLCJkaXYiLCJjbGFzc05hbWUiLCJvdmVybGF5IiwibW9kYWwiLCJoZWFkZXIiLCJoMiIsInAiLCJjb250ZW50IiwicGF5bWVudEluZm8iLCJhbW91bnREaXNwbGF5Iiwic3BhbiIsImFtb3VudCIsImN1cnJlbmN5IiwiYmVuZWZpdHMiLCJoMyIsInVsIiwibGkiLCJhY3Rpb25zIiwiYnV0dG9uIiwicGF5QnRuIiwicHJpbWFyeSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImljb24iLCJzZWNvbmRhcnkiLCJzdHlsZSIsImZvbnRTaXplIiwicGFkZGluZyIsInN0YXR1c0Rpc3BsYXkiLCJzcGlubmVyIiwic3VjY2Vzc0ljb24iLCJlcnJvckljb24iLCJjb2xvciIsIm1hcmdpbkJvdHRvbSIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwiZ2FwIiwid2lkdGgiLCJjbG9zZUJ0biJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PaymentModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SiteGatekeeper.tsx":
/*!*******************************************!*\
  !*** ./src/components/SiteGatekeeper.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _hooks_useSiteAccess__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSiteAccess */ \"(ssr)/./src/hooks/useSiteAccess.ts\");\n/* harmony import */ var _PaymentModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PaymentModal */ \"(ssr)/./src/components/PaymentModal.tsx\");\n/* harmony import */ var _EthereumVerificationModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EthereumVerificationModal */ \"(ssr)/./src/components/EthereumVerificationModal.tsx\");\n/* harmony import */ var _hooks_usePaymentStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/usePaymentStatus */ \"(ssr)/./src/hooks/usePaymentStatus.ts\");\n/* harmony import */ var _SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SiteGatekeeper.module.css */ \"(ssr)/./src/components/SiteGatekeeper.module.css\");\n/* harmony import */ var _SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst SiteGatekeeper = ({ children })=>{\n    const { open } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKit)();\n    const { markAsPaid } = (0,_hooks_usePaymentStatus__WEBPACK_IMPORTED_MODULE_6__.usePaymentStatus)();\n    const { accessStage, accessGranted, isLoading, accessMessage, accessIcon, canProceedToPayment, canProceedToEthVerification, refreshAccess, requirements } = (0,_hooks_useSiteAccess__WEBPACK_IMPORTED_MODULE_3__.useSiteAccess)();\n    const [showPaymentModal, setShowPaymentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEthVerificationModal, setShowEthVerificationModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // If access is granted, render the main site\n    if (accessGranted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Handle payment success\n    const handlePaymentSuccess = ()=>{\n        markAsPaid();\n        setShowPaymentModal(false);\n        refreshAccess();\n    };\n    // Handle Ethereum verification success\n    const handleEthVerificationSuccess = ()=>{\n        setShowEthVerificationModal(false);\n        refreshAccess();\n    };\n    // Handle connect wallet\n    const handleConnectWallet = async ()=>{\n        try {\n            await open();\n        } catch (error) {\n            console.error(\"Failed to open wallet modal:\", error);\n        }\n    };\n    // Handle payment button click\n    const handlePaymentClick = ()=>{\n        if (canProceedToPayment) {\n            setShowPaymentModal(true);\n        } else {\n            handleConnectWallet();\n        }\n    };\n    // Handle Ethereum verification button click\n    const handleEthVerificationClick = ()=>{\n        if (canProceedToEthVerification) {\n            setShowEthVerificationModal(true);\n        } else if (!requirements.walletConnected) {\n            handleConnectWallet();\n        } else {\n            setShowPaymentModal(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().gatekeeper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().gatekeeperContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"\\uD83D\\uDE80 Werant\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Blockchain Voting Platform\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().accessCard),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().accessHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().accessIcon),\n                                        children: accessIcon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Site Access Control\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: accessMessage\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().loadingState),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().spinner)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Verifying access requirements...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined),\n                            !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementsList),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Access Requirements:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirement),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementStatus)} ${requirements.walletConnected ? (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().completed) : (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().pending)}`,\n                                                children: requirements.walletConnected ? \"✅\" : \"⏳\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementText),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        children: \"1. Connect Wallet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Connect your wallet using 600+ supported options\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !requirements.walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().actionBtn)} ${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().primary)}`,\n                                                onClick: handleConnectWallet,\n                                                children: \"Connect Wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirement),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementStatus)} ${requirements.paymentCompleted ? (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().completed) : requirements.walletConnected ? (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().pending) : (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().disabled)}`,\n                                                children: requirements.paymentCompleted ? \"✅\" : requirements.walletConnected ? \"⏳\" : \"⚪\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementText),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        children: \"2. Activation Payment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"One-time fee of 0.25 MON to activate your account\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            requirements.walletConnected && !requirements.paymentCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().actionBtn)} ${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().payment)}`,\n                                                onClick: handlePaymentClick,\n                                                children: \"Pay 0.25 MON\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirement),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementStatus)} ${requirements.ethVerificationPassed ? (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().completed) : requirements.paymentCompleted ? (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().pending) : (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().disabled)}`,\n                                                children: requirements.ethVerificationPassed ? \"✅\" : requirements.paymentCompleted ? \"⏳\" : \"⚪\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().requirementText),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        children: \"3. Ethereum Verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Verify 5+ transactions on Ethereum mainnet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            requirements.paymentCompleted && !requirements.ethVerificationPassed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().actionBtn)} ${(_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().verification)}`,\n                                                onClick: handleEthVerificationClick,\n                                                children: \"Verify Ethereum\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, undefined),\n                            accessStage === \"access_granted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().successState),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().successIcon),\n                                        children: \"\\uD83C\\uDF89\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Welcome to Werant!\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"All requirements met. Redirecting to platform...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().footer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Secure • Decentralized • Transparent\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SiteGatekeeper_module_css__WEBPACK_IMPORTED_MODULE_7___default().networkInfo),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Monad Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Ethereum Mainnet Verified\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PaymentModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showPaymentModal,\n                onClose: ()=>setShowPaymentModal(false),\n                onPaymentSuccess: handlePaymentSuccess\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EthereumVerificationModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showEthVerificationModal,\n                onClose: ()=>setShowEthVerificationModal(false),\n                onVerificationSuccess: handleEthVerificationSuccess\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Werank/src/components/SiteGatekeeper.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SiteGatekeeper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SiteGatekeeper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useEthereumVerification.ts":
/*!**********************************************!*\
  !*** ./src/hooks/useEthereumVerification.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEthereumVerification: () => (/* binding */ useEthereumVerification)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _services_ethereumVerification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/ethereumVerification */ \"(ssr)/./src/services/ethereumVerification.ts\");\n/* __next_internal_client_entry_do_not_use__ useEthereumVerification auto */ \n\n\nconst useEthereumVerification = ()=>{\n    const { address, isConnected } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Check verification status when wallet connects\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const checkVerification = async ()=>{\n            if (!isConnected || !address) {\n                setVerificationStatus(null);\n                setIsLoading(false);\n                setError(null);\n                return;\n            }\n            try {\n                setIsLoading(true);\n                setError(null);\n                // Check cache first\n                const cached = (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_2__.getCachedTransactionHistory)(address);\n                if (cached) {\n                    setVerificationStatus(cached);\n                    setIsLoading(false);\n                    return;\n                }\n                // Check Ethereum mainnet transaction history\n                console.log(`🔍 Verifying Ethereum history for: ${address}`);\n                const history = await (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_2__.checkEthereumTransactionHistory)(address);\n                setVerificationStatus(history);\n                // Cache the result\n                (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_2__.cacheTransactionHistory)(history);\n                console.log(`✅ Verification complete:`, history);\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : \"Verification failed\";\n                setError(errorMessage);\n                console.error(\"❌ Ethereum verification error:\", err);\n                // Set failed verification status\n                setVerificationStatus({\n                    address,\n                    transactionCount: 0,\n                    hasMinimumTxs: false,\n                    isEligible: false,\n                    checkedAt: Date.now(),\n                    error: errorMessage\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        checkVerification();\n    }, [\n        address,\n        isConnected\n    ]);\n    // Force refresh verification\n    const refreshVerification = async ()=>{\n        if (!address) return;\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Clear cache\n            const cacheKey = `eth_tx_history_${address.toLowerCase()}`;\n            localStorage.removeItem(cacheKey);\n            // Re-check\n            const history = await (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_2__.checkEthereumTransactionHistory)(address);\n            setVerificationStatus(history);\n            (0,_services_ethereumVerification__WEBPACK_IMPORTED_MODULE_2__.cacheTransactionHistory)(history);\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Verification failed\";\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Clear verification status\n    const clearVerification = ()=>{\n        setVerificationStatus(null);\n        setError(null);\n        setIsLoading(false);\n    };\n    return {\n        verificationStatus,\n        isLoading,\n        error,\n        isEligible: verificationStatus?.isEligible || false,\n        transactionCount: verificationStatus?.transactionCount || 0,\n        hasMinimumTxs: verificationStatus?.hasMinimumTxs || false,\n        refreshVerification,\n        clearVerification\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useEthereumVerification.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/usePaymentStatus.ts":
/*!***************************************!*\
  !*** ./src/hooks/usePaymentStatus.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePaymentStatus: () => (/* binding */ usePaymentStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useReadContract.js\");\n/* __next_internal_client_entry_do_not_use__ usePaymentStatus auto */ \n\n\nconst usePaymentStatus = ()=>{\n    const { address, isConnected } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const [hasPaid, setHasPaid] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    // WerantWalletConnect contract address and ABI\n    const WALLET_CONNECT_CONTRACT = \"******************************************\";\n    const WALLET_CONNECT_ABI = [\n        {\n            \"inputs\": [],\n            \"name\": \"connectWallet\",\n            \"outputs\": [],\n            \"stateMutability\": \"payable\",\n            \"type\": \"function\"\n        },\n        {\n            \"inputs\": [\n                {\n                    \"internalType\": \"address\",\n                    \"name\": \"wallet\",\n                    \"type\": \"address\"\n                }\n            ],\n            \"name\": \"isWalletConnected\",\n            \"outputs\": [\n                {\n                    \"internalType\": \"bool\",\n                    \"name\": \"\",\n                    \"type\": \"bool\"\n                }\n            ],\n            \"stateMutability\": \"view\",\n            \"type\": \"function\"\n        }\n    ];\n    // Check smart contract for payment status\n    const { data: contractPaidStatus, isError, isLoading: contractLoading } = (0,wagmi__WEBPACK_IMPORTED_MODULE_2__.useReadContract)({\n        address: WALLET_CONNECT_CONTRACT,\n        abi: WALLET_CONNECT_ABI,\n        functionName: \"isWalletConnected\",\n        args: address ? [\n            address\n        ] : undefined,\n        query: {\n            enabled: !!address && isConnected,\n            refetchInterval: 5000\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const checkPaymentStatus = ()=>{\n            if (!isConnected || !address) {\n                setHasPaid(false);\n                setIsLoading(false);\n                return;\n            }\n            // If contract data is available, use it as the primary source\n            if (contractPaidStatus !== undefined && !contractLoading) {\n                setHasPaid(Boolean(contractPaidStatus));\n                setIsLoading(false);\n                // Update localStorage to match contract state\n                const paymentKey = `werant_paid_${address.toLowerCase()}`;\n                if (contractPaidStatus) {\n                    localStorage.setItem(paymentKey, \"true\");\n                } else {\n                    localStorage.removeItem(paymentKey);\n                }\n                return;\n            }\n            // Fallback to localStorage while contract is loading\n            if (!contractLoading && isError) {\n                const paymentKey = `werant_paid_${address.toLowerCase()}`;\n                const paidStatus = localStorage.getItem(paymentKey);\n                setHasPaid(paidStatus === \"true\");\n                setIsLoading(false);\n                return;\n            }\n            // Still loading contract data\n            setIsLoading(contractLoading);\n        };\n        checkPaymentStatus();\n    }, [\n        address,\n        isConnected,\n        contractPaidStatus,\n        contractLoading,\n        isError\n    ]);\n    const markAsPaid = ()=>{\n        if (address) {\n            const paymentKey = `werant_paid_${address.toLowerCase()}`;\n            localStorage.setItem(paymentKey, \"true\");\n            setHasPaid(true);\n        }\n    };\n    const resetPaymentStatus = ()=>{\n        if (address) {\n            const paymentKey = `werant_paid_${address.toLowerCase()}`;\n            localStorage.removeItem(paymentKey);\n            setHasPaid(false);\n        }\n    };\n    // Force refresh payment status from contract\n    const refreshPaymentStatus = ()=>{\n        if (address && isConnected) {\n            setIsLoading(true);\n        // The useReadContract will automatically refetch\n        }\n    };\n    return {\n        hasPaid,\n        isLoading,\n        markAsPaid,\n        resetPaymentStatus,\n        refreshPaymentStatus,\n        contractPaidStatus: Boolean(contractPaidStatus),\n        isContractLoading: contractLoading\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/usePaymentStatus.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSiteAccess.ts":
/*!************************************!*\
  !*** ./src/hooks/useSiteAccess.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSiteAccess: () => (/* binding */ useSiteAccess)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _usePaymentStatus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./usePaymentStatus */ \"(ssr)/./src/hooks/usePaymentStatus.ts\");\n/* harmony import */ var _useEthereumVerification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useEthereumVerification */ \"(ssr)/./src/hooks/useEthereumVerification.ts\");\n/* __next_internal_client_entry_do_not_use__ useSiteAccess auto */ \n\n\n\nconst useSiteAccess = ()=>{\n    const { address, isConnected } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const { hasPaid, isLoading: paymentLoading } = (0,_usePaymentStatus__WEBPACK_IMPORTED_MODULE_2__.usePaymentStatus)();\n    const { isEligible: isEthEligible, isLoading: ethLoading } = (0,_useEthereumVerification__WEBPACK_IMPORTED_MODULE_3__.useEthereumVerification)();\n    const [accessStage, setAccessStage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"loading\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [accessGranted, setAccessGranted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const checkAccess = ()=>{\n            // Step 1: Check if wallet is connected\n            if (!isConnected || !address) {\n                setAccessStage(\"connect_wallet\");\n                setAccessGranted(false);\n                setIsLoading(false);\n                return;\n            }\n            // Step 2: Check if still loading payment or ETH verification\n            if (paymentLoading || ethLoading) {\n                setAccessStage(\"loading\");\n                setIsLoading(true);\n                return;\n            }\n            // Step 3: Check payment status first (primary requirement)\n            if (!hasPaid) {\n                setAccessStage(\"payment_required\");\n                setAccessGranted(false);\n                setIsLoading(false);\n                return;\n            }\n            // Step 4: Check Ethereum verification (secondary requirement)\n            if (!isEthEligible) {\n                setAccessStage(\"eth_verification_required\");\n                setAccessGranted(false);\n                setIsLoading(false);\n                return;\n            }\n            // Step 5: All requirements met - grant access\n            setAccessStage(\"access_granted\");\n            setAccessGranted(true);\n            setIsLoading(false);\n        };\n        checkAccess();\n    }, [\n        isConnected,\n        address,\n        hasPaid,\n        paymentLoading,\n        isEthEligible,\n        ethLoading\n    ]);\n    // Force refresh all verifications\n    const refreshAccess = ()=>{\n        setIsLoading(true);\n        setAccessStage(\"loading\");\n    // The useEffect will automatically re-run and check access\n    };\n    // Get access stage message\n    const getAccessMessage = ()=>{\n        switch(accessStage){\n            case \"loading\":\n                return \"Checking access requirements...\";\n            case \"connect_wallet\":\n                return \"Please connect your wallet to access Werant\";\n            case \"payment_required\":\n                return \"Payment required: Pay 0.25 MON activation fee\";\n            case \"eth_verification_required\":\n                return \"Ethereum verification required: Need 5+ transactions on ETH mainnet\";\n            case \"access_granted\":\n                return \"Access granted! Welcome to Werant\";\n            case \"access_denied\":\n                return \"Access denied: Requirements not met\";\n            default:\n                return \"Checking access...\";\n        }\n    };\n    // Get access stage icon\n    const getAccessIcon = ()=>{\n        switch(accessStage){\n            case \"loading\":\n                return \"⏳\";\n            case \"connect_wallet\":\n                return \"\\uD83D\\uDD17\";\n            case \"payment_required\":\n                return \"\\uD83D\\uDCB3\";\n            case \"eth_verification_required\":\n                return \"\\uD83D\\uDD0D\";\n            case \"access_granted\":\n                return \"✅\";\n            case \"access_denied\":\n                return \"❌\";\n            default:\n                return \"⏳\";\n        }\n    };\n    // Check if user can proceed to next step\n    const canProceedToPayment = ()=>{\n        return isConnected && !!address;\n    };\n    const canProceedToEthVerification = ()=>{\n        return isConnected && !!address && hasPaid;\n    };\n    return {\n        accessStage,\n        accessGranted,\n        isLoading,\n        accessMessage: getAccessMessage(),\n        accessIcon: getAccessIcon(),\n        canProceedToPayment: canProceedToPayment(),\n        canProceedToEthVerification: canProceedToEthVerification(),\n        refreshAccess,\n        // Individual status checks\n        isConnected,\n        hasPaid,\n        isEthEligible,\n        paymentLoading,\n        ethLoading,\n        // Requirements summary\n        requirements: {\n            walletConnected: isConnected,\n            paymentCompleted: hasPaid,\n            ethVerificationPassed: isEthEligible\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSiteAccess.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/appkit.ts":
/*!***************************!*\
  !*** ./src/lib/appkit.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n\n\n\n// Define Monad Testnet\nconst monadTestnet = {\n    id: 10143,\n    name: \"Monad Testnet\",\n    nativeCurrency: {\n        decimals: 18,\n        name: \"MON\",\n        symbol: \"MON\"\n    },\n    rpcUrls: {\n        default: {\n            http: [\n                \"https://testnet-rpc.monad.xyz\"\n            ]\n        }\n    },\n    blockExplorers: {\n        default: {\n            name: \"Monad Explorer\",\n            url: \"https://testnet-explorer.monad.xyz\"\n        }\n    },\n    testnet: true\n};\n// 1. Get projectId from https://dashboard.reown.com\nconst projectId = \"3203e3196ca7682fb5394a53b725d357\" || 0;\n// Debug logging\nconsole.log(\"\\uD83D\\uDD27 AppKit Configuration:\");\nconsole.log(\"\\uD83D\\uDCCD Project ID:\", projectId);\nconsole.log(\"\\uD83C\\uDF10 Environment:\", \"development\");\n// 2. Create a metadata object - optional\nconst metadata = {\n    name: \"Werant\",\n    description: \"Smart wallet connection with auto-detection and network switching\",\n    url:  false ? 0 : \"http://localhost:3000\",\n    icons: [\n        \"https://avatars.githubusercontent.com/u/37784886\"\n    ]\n};\n// 3. Set the networks\nconst networks = [\n    monadTestnet,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.mainnet,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.arbitrum,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.polygon,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.base\n];\n// 4. Create Wagmi Adapter\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    networks,\n    projectId,\n    ssr: true\n});\n// 5. Create modal with 600+ wallet support and smart network handling\n(0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter\n    ],\n    networks,\n    defaultNetwork: monadTestnet,\n    projectId,\n    metadata,\n    features: {\n        analytics: true,\n        email: true,\n        socials: [\n            \"google\",\n            \"x\",\n            \"github\",\n            \"discord\",\n            \"apple\"\n        ],\n        emailShowWallets: true,\n        onramp: true,\n        swaps: true,\n        smartSessions: true,\n        allWallets: true // Show all 600+ wallets\n    },\n    allowUnsupportedChain: false,\n    themeMode: \"light\",\n    themeVariables: {\n        \"--w3m-accent\": \"#3b82f6\",\n        \"--w3m-border-radius-master\": \"16px\",\n        \"--w3m-font-family\": '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n    }\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwcGtpdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRDtBQUNRO0FBQ2U7QUFFekUsdUJBQXVCO0FBQ3ZCLE1BQU1NLGVBQWU7SUFDbkJDLElBQUk7SUFDSkMsTUFBTTtJQUNOQyxnQkFBZ0I7UUFDZEMsVUFBVTtRQUNWRixNQUFNO1FBQ05HLFFBQVE7SUFDVjtJQUNBQyxTQUFTO1FBQ1BDLFNBQVM7WUFDUEMsTUFBTTtnQkFBQzthQUFnQztRQUN6QztJQUNGO0lBQ0FDLGdCQUFnQjtRQUNkRixTQUFTO1lBQ1BMLE1BQU07WUFDTlEsS0FBSztRQUNQO0lBQ0Y7SUFDQUMsU0FBUztBQUNYO0FBRUEsb0RBQW9EO0FBQ3BELE1BQU1DLFlBQVlDLGtDQUFnRCxJQUFJO0FBRXRFLGdCQUFnQjtBQUNoQkcsUUFBUUMsR0FBRyxDQUFDO0FBQ1pELFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0JMO0FBQzlCSSxRQUFRQyxHQUFHLENBQUMsNkJBakNaO0FBbUNBLHlDQUF5QztBQUN6QyxNQUFNQyxXQUFXO0lBQ2ZoQixNQUFNO0lBQ05pQixhQUFhO0lBQ2JULEtBQUssTUFBa0IsR0FBY1UsQ0FBc0IsR0FBRztJQUM5REcsT0FBTztRQUFDO0tBQW1EO0FBQzdEO0FBRUEsc0JBQXNCO0FBQ3RCLE1BQU1DLFdBQVc7SUFBQ3hCO0lBQWNKLDJEQUFPQTtJQUFFQyw0REFBUUE7SUFBRUMsMkRBQU9BO0lBQUVDLHdEQUFJQTtDQUFDO0FBRWpFLDBCQUEwQjtBQUMxQixNQUFNMEIsZUFBZSxJQUFJOUIscUVBQVlBLENBQUM7SUFDcEM2QjtJQUNBWjtJQUNBYyxLQUFLO0FBQ1A7QUFFQSxzRUFBc0U7QUFDdEVoQyxpRUFBWUEsQ0FBQztJQUNYaUMsVUFBVTtRQUFDRjtLQUFhO0lBQ3hCRDtJQUNBSSxnQkFBZ0I1QjtJQUNoQlk7SUFDQU07SUFDQVcsVUFBVTtRQUNSQyxXQUFXO1FBQ1hDLE9BQU87UUFDUEMsU0FBUztZQUFDO1lBQVU7WUFBSztZQUFVO1lBQVc7U0FBUTtRQUN0REMsa0JBQWtCO1FBQ2xCQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsZUFBZTtRQUNmQyxZQUFZLEtBQUssd0JBQXdCO0lBQzNDO0lBQ0FDLHVCQUF1QjtJQUN2QkMsV0FBVztJQUNYQyxnQkFBZ0I7UUFDZCxnQkFBZ0I7UUFDaEIsOEJBQThCO1FBQzlCLHFCQUFxQjtJQUN2QjtBQUNGO0FBRWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9zcmMvbGliL2FwcGtpdC50cz84ZDc3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUFwcEtpdCB9IGZyb20gJ0ByZW93bi9hcHBraXQvcmVhY3QnXG5pbXBvcnQgeyBXYWdtaUFkYXB0ZXIgfSBmcm9tICdAcmVvd24vYXBwa2l0LWFkYXB0ZXItd2FnbWknXG5pbXBvcnQgeyBtYWlubmV0LCBhcmJpdHJ1bSwgcG9seWdvbiwgYmFzZSB9IGZyb20gJ0ByZW93bi9hcHBraXQvbmV0d29ya3MnXG5cbi8vIERlZmluZSBNb25hZCBUZXN0bmV0XG5jb25zdCBtb25hZFRlc3RuZXQgPSB7XG4gIGlkOiAxMDE0MyxcbiAgbmFtZTogJ01vbmFkIFRlc3RuZXQnLFxuICBuYXRpdmVDdXJyZW5jeToge1xuICAgIGRlY2ltYWxzOiAxOCxcbiAgICBuYW1lOiAnTU9OJyxcbiAgICBzeW1ib2w6ICdNT04nLFxuICB9LFxuICBycGNVcmxzOiB7XG4gICAgZGVmYXVsdDoge1xuICAgICAgaHR0cDogWydodHRwczovL3Rlc3RuZXQtcnBjLm1vbmFkLnh5eiddXG4gICAgfVxuICB9LFxuICBibG9ja0V4cGxvcmVyczoge1xuICAgIGRlZmF1bHQ6IHtcbiAgICAgIG5hbWU6ICdNb25hZCBFeHBsb3JlcicsXG4gICAgICB1cmw6ICdodHRwczovL3Rlc3RuZXQtZXhwbG9yZXIubW9uYWQueHl6J1xuICAgIH1cbiAgfSxcbiAgdGVzdG5ldDogdHJ1ZVxufVxuXG4vLyAxLiBHZXQgcHJvamVjdElkIGZyb20gaHR0cHM6Ly9kYXNoYm9hcmQucmVvd24uY29tXG5jb25zdCBwcm9qZWN0SWQgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19XQUxMRVRDT05ORUNUX1BST0pFQ1RfSUQgfHwgJzMyMDNlMzE5NmNhNzY4MmZiNTM5NGE1M2I3MjVkMzU3J1xuXG4vLyBEZWJ1ZyBsb2dnaW5nXG5jb25zb2xlLmxvZygn8J+UpyBBcHBLaXQgQ29uZmlndXJhdGlvbjonKVxuY29uc29sZS5sb2coJ/Cfk40gUHJvamVjdCBJRDonLCBwcm9qZWN0SWQpXG5jb25zb2xlLmxvZygn8J+MkCBFbnZpcm9ubWVudDonLCBwcm9jZXNzLmVudi5OT0RFX0VOVilcblxuLy8gMi4gQ3JlYXRlIGEgbWV0YWRhdGEgb2JqZWN0IC0gb3B0aW9uYWxcbmNvbnN0IG1ldGFkYXRhID0ge1xuICBuYW1lOiAnV2VyYW50JyxcbiAgZGVzY3JpcHRpb246ICdTbWFydCB3YWxsZXQgY29ubmVjdGlvbiB3aXRoIGF1dG8tZGV0ZWN0aW9uIGFuZCBuZXR3b3JrIHN3aXRjaGluZycsXG4gIHVybDogdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyB3aW5kb3cubG9jYXRpb24ub3JpZ2luIDogJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCcsIC8vIER5bmFtaWMgVVJMIGZvciBkZXYvcHJvZFxuICBpY29uczogWydodHRwczovL2F2YXRhcnMuZ2l0aHVidXNlcmNvbnRlbnQuY29tL3UvMzc3ODQ4ODYnXVxufVxuXG4vLyAzLiBTZXQgdGhlIG5ldHdvcmtzXG5jb25zdCBuZXR3b3JrcyA9IFttb25hZFRlc3RuZXQsIG1haW5uZXQsIGFyYml0cnVtLCBwb2x5Z29uLCBiYXNlXVxuXG4vLyA0LiBDcmVhdGUgV2FnbWkgQWRhcHRlclxuY29uc3Qgd2FnbWlBZGFwdGVyID0gbmV3IFdhZ21pQWRhcHRlcih7XG4gIG5ldHdvcmtzLFxuICBwcm9qZWN0SWQsXG4gIHNzcjogdHJ1ZVxufSlcblxuLy8gNS4gQ3JlYXRlIG1vZGFsIHdpdGggNjAwKyB3YWxsZXQgc3VwcG9ydCBhbmQgc21hcnQgbmV0d29yayBoYW5kbGluZ1xuY3JlYXRlQXBwS2l0KHtcbiAgYWRhcHRlcnM6IFt3YWdtaUFkYXB0ZXJdLFxuICBuZXR3b3JrcyxcbiAgZGVmYXVsdE5ldHdvcms6IG1vbmFkVGVzdG5ldCxcbiAgcHJvamVjdElkLFxuICBtZXRhZGF0YSxcbiAgZmVhdHVyZXM6IHtcbiAgICBhbmFseXRpY3M6IHRydWUsIC8vIE9wdGlvbmFsIC0gZGVmYXVsdHMgdG8geW91ciBDbG91ZCBjb25maWd1cmF0aW9uXG4gICAgZW1haWw6IHRydWUsIC8vIEVuYWJsZSBlbWFpbCB3YWxsZXRzXG4gICAgc29jaWFsczogWydnb29nbGUnLCAneCcsICdnaXRodWInLCAnZGlzY29yZCcsICdhcHBsZSddLCAvLyBFbmFibGUgc29jaWFsIGxvZ2luc1xuICAgIGVtYWlsU2hvd1dhbGxldHM6IHRydWUsIC8vIFNob3cgd2FsbGV0IG9wdGlvbnMgaW4gZW1haWwgZmxvd1xuICAgIG9ucmFtcDogdHJ1ZSwgLy8gRW5hYmxlIG9uLXJhbXBcbiAgICBzd2FwczogdHJ1ZSwgLy8gRW5hYmxlIHN3YXBzXG4gICAgc21hcnRTZXNzaW9uczogdHJ1ZSwgLy8gRW5hYmxlIHNtYXJ0IHNlc3Npb25zXG4gICAgYWxsV2FsbGV0czogdHJ1ZSAvLyBTaG93IGFsbCA2MDArIHdhbGxldHNcbiAgfSxcbiAgYWxsb3dVbnN1cHBvcnRlZENoYWluOiBmYWxzZSwgLy8gRm9yY2UgbmV0d29yayBzd2l0Y2hpbmdcbiAgdGhlbWVNb2RlOiAnbGlnaHQnLFxuICB0aGVtZVZhcmlhYmxlczoge1xuICAgICctLXczbS1hY2NlbnQnOiAnIzNiODJmNicsXG4gICAgJy0tdzNtLWJvcmRlci1yYWRpdXMtbWFzdGVyJzogJzE2cHgnLFxuICAgICctLXczbS1mb250LWZhbWlseSc6ICctYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsIFwiU2Vnb2UgVUlcIiwgUm9ib3RvLCBzYW5zLXNlcmlmJ1xuICB9XG59KVxuXG5leHBvcnQgeyB3YWdtaUFkYXB0ZXIsIHByb2plY3RJZCB9XG4iXSwibmFtZXMiOlsiY3JlYXRlQXBwS2l0IiwiV2FnbWlBZGFwdGVyIiwibWFpbm5ldCIsImFyYml0cnVtIiwicG9seWdvbiIsImJhc2UiLCJtb25hZFRlc3RuZXQiLCJpZCIsIm5hbWUiLCJuYXRpdmVDdXJyZW5jeSIsImRlY2ltYWxzIiwic3ltYm9sIiwicnBjVXJscyIsImRlZmF1bHQiLCJodHRwIiwiYmxvY2tFeHBsb3JlcnMiLCJ1cmwiLCJ0ZXN0bmV0IiwicHJvamVjdElkIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1dBTExFVENPTk5FQ1RfUFJPSkVDVF9JRCIsImNvbnNvbGUiLCJsb2ciLCJtZXRhZGF0YSIsImRlc2NyaXB0aW9uIiwid2luZG93IiwibG9jYXRpb24iLCJvcmlnaW4iLCJpY29ucyIsIm5ldHdvcmtzIiwid2FnbWlBZGFwdGVyIiwic3NyIiwiYWRhcHRlcnMiLCJkZWZhdWx0TmV0d29yayIsImZlYXR1cmVzIiwiYW5hbHl0aWNzIiwiZW1haWwiLCJzb2NpYWxzIiwiZW1haWxTaG93V2FsbGV0cyIsIm9ucmFtcCIsInN3YXBzIiwic21hcnRTZXNzaW9ucyIsImFsbFdhbGxldHMiLCJhbGxvd1Vuc3VwcG9ydGVkQ2hhaW4iLCJ0aGVtZU1vZGUiLCJ0aGVtZVZhcmlhYmxlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/appkit.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/ethereumVerification.ts":
/*!**********************************************!*\
  !*** ./src/services/ethereumVerification.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cacheTransactionHistory: () => (/* binding */ cacheTransactionHistory),\n/* harmony export */   checkEthereumTransactionHistory: () => (/* binding */ checkEthereumTransactionHistory),\n/* harmony export */   formatAddress: () => (/* binding */ formatAddress),\n/* harmony export */   getCachedTransactionHistory: () => (/* binding */ getCachedTransactionHistory),\n/* harmony export */   getEligibilityMessage: () => (/* binding */ getEligibilityMessage)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createPublicClient.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var viem_chains__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/mainnet.js\");\n/* __next_internal_client_entry_do_not_use__ checkEthereumTransactionHistory,cacheTransactionHistory,getCachedTransactionHistory,formatAddress,getEligibilityMessage auto */ \n\n// Create Ethereum mainnet client\nconst ethereumClient = (0,viem__WEBPACK_IMPORTED_MODULE_0__.createPublicClient)({\n    chain: viem_chains__WEBPACK_IMPORTED_MODULE_1__.mainnet,\n    transport: (0,viem__WEBPACK_IMPORTED_MODULE_2__.http)(\"https://eth-mainnet.g.alchemy.com/v2/demo\") // Using Alchemy's demo endpoint\n});\n// Alternative RPC endpoints (fallbacks)\nconst ETHEREUM_RPC_ENDPOINTS = [\n    \"https://eth-mainnet.g.alchemy.com/v2/demo\",\n    \"https://mainnet.infura.io/v3/********************************\",\n    \"https://ethereum.publicnode.com\",\n    \"https://rpc.ankr.com/eth\",\n    \"https://eth.llamarpc.com\"\n];\n// Minimum transaction requirement\nconst MINIMUM_TX_COUNT = 5;\n/**\n * Check if wallet has minimum transactions on Ethereum mainnet using Etherscan API\n */ const checkEthereumTransactionHistory = async (address)=>{\n    try {\n        // Validate address format\n        if (!address || !address.startsWith(\"0x\") || address.length !== 42) {\n            throw new Error(\"Invalid Ethereum address format\");\n        }\n        console.log(`🔍 Checking Ethereum mainnet history for: ${address}`);\n        // Try Etherscan API first (most reliable)\n        const etherscanResult = await checkWithEtherscan(address);\n        if (etherscanResult.transactionCount >= 0) {\n            return etherscanResult;\n        }\n        // Fallback to RPC method\n        console.log(\"\\uD83D\\uDCE1 Etherscan failed, trying RPC method...\");\n        const rpcResult = await checkWithRPC(address);\n        return rpcResult;\n    } catch (error) {\n        console.error(\"❌ Error checking Ethereum history:\", error);\n        return {\n            address,\n            transactionCount: 0,\n            hasMinimumTxs: false,\n            isEligible: false,\n            checkedAt: Date.now(),\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n};\n/**\n * Check transaction history using Etherscan API\n */ async function checkWithEtherscan(address) {\n    try {\n        // Using Etherscan's free API (no key required for basic queries)\n        const etherscanUrl = `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&page=1&offset=10&sort=desc&apikey=YourApiKeyToken`;\n        const response = await fetch(etherscanUrl);\n        const data = await response.json();\n        if (data.status === \"1\" && Array.isArray(data.result)) {\n            const transactionCount = data.result.length;\n            const hasMinimumTxs = transactionCount >= MINIMUM_TX_COUNT;\n            console.log(`✅ Etherscan: Found ${transactionCount} transactions`);\n            return {\n                address,\n                transactionCount,\n                hasMinimumTxs,\n                isEligible: hasMinimumTxs,\n                checkedAt: Date.now()\n            };\n        } else {\n            throw new Error(`Etherscan API error: ${data.message}`);\n        }\n    } catch (error) {\n        console.error(\"❌ Etherscan check failed:\", error);\n        throw error;\n    }\n}\n/**\n * Check transaction count using RPC method (fallback)\n */ async function checkWithRPC(address) {\n    try {\n        // Get transaction count (nonce) from Ethereum mainnet\n        const transactionCount = await ethereumClient.getTransactionCount({\n            address: address,\n            blockTag: \"latest\"\n        });\n        const txCount = Number(transactionCount);\n        const hasMinimumTxs = txCount >= MINIMUM_TX_COUNT;\n        console.log(`✅ RPC: Found ${txCount} transactions (nonce)`);\n        return {\n            address,\n            transactionCount: txCount,\n            hasMinimumTxs,\n            isEligible: hasMinimumTxs,\n            checkedAt: Date.now()\n        };\n    } catch (error) {\n        console.error(\"❌ RPC check failed:\", error);\n        throw error;\n    }\n}\n/**\n * Cache transaction history in localStorage\n */ const cacheTransactionHistory = (history)=>{\n    try {\n        const cacheKey = `eth_tx_history_${history.address.toLowerCase()}`;\n        const cacheData = {\n            ...history,\n            cachedAt: Date.now()\n        };\n        localStorage.setItem(cacheKey, JSON.stringify(cacheData));\n        console.log(`💾 Cached transaction history for ${history.address}`);\n    } catch (error) {\n        console.error(\"❌ Failed to cache transaction history:\", error);\n    }\n};\n/**\n * Get cached transaction history from localStorage\n */ const getCachedTransactionHistory = (address)=>{\n    try {\n        const cacheKey = `eth_tx_history_${address.toLowerCase()}`;\n        const cached = localStorage.getItem(cacheKey);\n        if (!cached) return null;\n        const cacheData = JSON.parse(cached);\n        const cacheAge = Date.now() - cacheData.cachedAt;\n        const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours\n        ;\n        if (cacheAge > CACHE_DURATION) {\n            localStorage.removeItem(cacheKey);\n            return null;\n        }\n        console.log(`📋 Using cached transaction history for ${address}`);\n        return cacheData;\n    } catch (error) {\n        console.error(\"❌ Failed to get cached transaction history:\", error);\n        return null;\n    }\n};\n/**\n * Format address for display\n */ const formatAddress = (address)=>{\n    if (!address) return \"\";\n    return `${address.slice(0, 6)}...${address.slice(-4)}`;\n};\n/**\n * Get eligibility message\n */ const getEligibilityMessage = (history)=>{\n    if (history.error) {\n        return `❌ Error checking eligibility: ${history.error}`;\n    }\n    if (history.isEligible) {\n        return `✅ Eligible: ${history.transactionCount} transactions on Ethereum mainnet`;\n    } else {\n        const needed = MINIMUM_TX_COUNT - history.transactionCount;\n        return `❌ Not eligible: Need ${needed} more transactions on Ethereum mainnet (currently ${history.transactionCount})`;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ethereumVerification.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7faf21ba66a8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzAwMWUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZmFmMjFiYTY2YThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/components/EthereumVerificationModal.module.css":
/*!*************************************************************!*\
  !*** ./src/components/EthereumVerificationModal.module.css ***!
  \*************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"overlay\": \"EthereumVerificationModal_overlay__1SCfN\",\n\t\"fadeIn\": \"EthereumVerificationModal_fadeIn__krKp_\",\n\t\"modal\": \"EthereumVerificationModal_modal__vPfzY\",\n\t\"slideUp\": \"EthereumVerificationModal_slideUp__plUWY\",\n\t\"header\": \"EthereumVerificationModal_header__g9ues\",\n\t\"content\": \"EthereumVerificationModal_content__J1_wn\",\n\t\"requirementInfo\": \"EthereumVerificationModal_requirementInfo__j073g\",\n\t\"requirementCard\": \"EthereumVerificationModal_requirementCard__8GUvj\",\n\t\"requirementIcon\": \"EthereumVerificationModal_requirementIcon__4WQvq\",\n\t\"requirementText\": \"EthereumVerificationModal_requirementText__nxKl0\",\n\t\"walletInfo\": \"EthereumVerificationModal_walletInfo__MhHh8\",\n\t\"walletDisplay\": \"EthereumVerificationModal_walletDisplay__r8zla\",\n\t\"walletLabel\": \"EthereumVerificationModal_walletLabel__wx75a\",\n\t\"walletAddress\": \"EthereumVerificationModal_walletAddress___jRN9\",\n\t\"statusDisplay\": \"EthereumVerificationModal_statusDisplay__A_m0t\",\n\t\"spinner\": \"EthereumVerificationModal_spinner__CWdYj\",\n\t\"spin\": \"EthereumVerificationModal_spin__wzGYI\",\n\t\"successIcon\": \"EthereumVerificationModal_successIcon__ytU3G\",\n\t\"warningIcon\": \"EthereumVerificationModal_warningIcon__YvlAQ\",\n\t\"errorIcon\": \"EthereumVerificationModal_errorIcon__tkzol\",\n\t\"subText\": \"EthereumVerificationModal_subText__t_u_t\",\n\t\"errorText\": \"EthereumVerificationModal_errorText__a4knO\",\n\t\"transactionInfo\": \"EthereumVerificationModal_transactionInfo___aGxW\",\n\t\"txCount\": \"EthereumVerificationModal_txCount__Y_v2D\",\n\t\"txRequirement\": \"EthereumVerificationModal_txRequirement__MpGtG\",\n\t\"txNumber\": \"EthereumVerificationModal_txNumber__zkA4t\",\n\t\"reqNumber\": \"EthereumVerificationModal_reqNumber__g_za1\",\n\t\"txLabel\": \"EthereumVerificationModal_txLabel__UTTUx\",\n\t\"reqLabel\": \"EthereumVerificationModal_reqLabel__kjapQ\",\n\t\"detailsBtn\": \"EthereumVerificationModal_detailsBtn__AiIcJ\",\n\t\"details\": \"EthereumVerificationModal_details__McF3X\",\n\t\"detailRow\": \"EthereumVerificationModal_detailRow__ZIFhb\",\n\t\"eligible\": \"EthereumVerificationModal_eligible__zwObi\",\n\t\"notEligible\": \"EthereumVerificationModal_notEligible__tbZuh\",\n\t\"actions\": \"EthereumVerificationModal_actions___Ub72\",\n\t\"notEligibleActions\": \"EthereumVerificationModal_notEligibleActions__1SZOG\",\n\t\"helpText\": \"EthereumVerificationModal_helpText__Nb0So\",\n\t\"actionBtn\": \"EthereumVerificationModal_actionBtn__A7IK_\",\n\t\"primary\": \"EthereumVerificationModal_primary___sHSu\",\n\t\"secondary\": \"EthereumVerificationModal_secondary__Og2lQ\",\n\t\"demo\": \"EthereumVerificationModal_demo__fXXFe\",\n\t\"icon\": \"EthereumVerificationModal_icon__s43s5\",\n\t\"closeBtn\": \"EthereumVerificationModal_closeBtn__8N_4V\"\n};\n\nmodule.exports.__checksum = \"11886b9189a5\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/EthereumVerificationModal.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/PaymentModal.module.css":
/*!************************************************!*\
  !*** ./src/components/PaymentModal.module.css ***!
  \************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"overlay\": \"PaymentModal_overlay__hYE9X\",\n\t\"fadeIn\": \"PaymentModal_fadeIn__Erzv1\",\n\t\"modal\": \"PaymentModal_modal__tiAWD\",\n\t\"slideUp\": \"PaymentModal_slideUp__ZFKP7\",\n\t\"header\": \"PaymentModal_header__A_P5v\",\n\t\"content\": \"PaymentModal_content__u3win\",\n\t\"paymentInfo\": \"PaymentModal_paymentInfo__GuuUM\",\n\t\"amountDisplay\": \"PaymentModal_amountDisplay__QKt95\",\n\t\"amount\": \"PaymentModal_amount__vDaMn\",\n\t\"currency\": \"PaymentModal_currency__0hNXT\",\n\t\"benefits\": \"PaymentModal_benefits__EOwzO\",\n\t\"actions\": \"PaymentModal_actions__PC_fo\",\n\t\"payBtn\": \"PaymentModal_payBtn__IB1fD\",\n\t\"primary\": \"PaymentModal_primary__egBwr\",\n\t\"secondary\": \"PaymentModal_secondary__muG9E\",\n\t\"icon\": \"PaymentModal_icon__Cdb9R\",\n\t\"statusDisplay\": \"PaymentModal_statusDisplay__9CSsj\",\n\t\"spinner\": \"PaymentModal_spinner__67rhm\",\n\t\"spin\": \"PaymentModal_spin__qXEXW\",\n\t\"successIcon\": \"PaymentModal_successIcon__oEOn_\",\n\t\"errorIcon\": \"PaymentModal_errorIcon__0dl9t\",\n\t\"closeBtn\": \"PaymentModal_closeBtn__w1HlK\"\n};\n\nmodule.exports.__checksum = \"e430cd6cbc88\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PaymentModal.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/SiteGatekeeper.module.css":
/*!**************************************************!*\
  !*** ./src/components/SiteGatekeeper.module.css ***!
  \**************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"gatekeeper\": \"SiteGatekeeper_gatekeeper__73y3O\",\n\t\"float\": \"SiteGatekeeper_float__toIdQ\",\n\t\"gatekeeperContent\": \"SiteGatekeeper_gatekeeperContent__AagSx\",\n\t\"logo\": \"SiteGatekeeper_logo__EYRp8\",\n\t\"accessCard\": \"SiteGatekeeper_accessCard__h_FR5\",\n\t\"accessHeader\": \"SiteGatekeeper_accessHeader__GZI_M\",\n\t\"accessIcon\": \"SiteGatekeeper_accessIcon__Br_Jc\",\n\t\"loadingState\": \"SiteGatekeeper_loadingState__clFLi\",\n\t\"spinner\": \"SiteGatekeeper_spinner__hMIQW\",\n\t\"spin\": \"SiteGatekeeper_spin__IyVIr\",\n\t\"requirementsList\": \"SiteGatekeeper_requirementsList__npgQQ\",\n\t\"requirement\": \"SiteGatekeeper_requirement__N5psf\",\n\t\"requirementStatus\": \"SiteGatekeeper_requirementStatus__9FKSE\",\n\t\"completed\": \"SiteGatekeeper_completed__cweBz\",\n\t\"pending\": \"SiteGatekeeper_pending__HPY_j\",\n\t\"pulse\": \"SiteGatekeeper_pulse__R8746\",\n\t\"disabled\": \"SiteGatekeeper_disabled__J9mHN\",\n\t\"requirementText\": \"SiteGatekeeper_requirementText__ZDDjL\",\n\t\"actionBtn\": \"SiteGatekeeper_actionBtn__ldUfM\",\n\t\"primary\": \"SiteGatekeeper_primary__O6x4D\",\n\t\"payment\": \"SiteGatekeeper_payment__HJFTz\",\n\t\"verification\": \"SiteGatekeeper_verification__OTmjb\",\n\t\"successState\": \"SiteGatekeeper_successState__mJCi0\",\n\t\"successIcon\": \"SiteGatekeeper_successIcon__VBj0I\",\n\t\"bounce\": \"SiteGatekeeper_bounce__zanWP\",\n\t\"footer\": \"SiteGatekeeper_footer__9dvLe\",\n\t\"networkInfo\": \"SiteGatekeeper_networkInfo__R_3fp\"\n};\n\nmodule.exports.__checksum = \"b598a3307673\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SiteGatekeeper.module.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _components_SiteGatekeeper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SiteGatekeeper */ \"(rsc)/./src/components/SiteGatekeeper.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Werant - Blockchain Voting Platform\",\n    description: \"Secure blockchain voting with on-chain verification and smart contracts\",\n    keywords: [\n        \"blockchain\",\n        \"voting\",\n        \"wallet\",\n        \"web3\",\n        \"monad\",\n        \"ethereum\",\n        \"verification\"\n    ],\n    authors: [\n        {\n            name: \"Werant Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#667eea\",\n    openGraph: {\n        title: \"Werant - Blockchain Voting Platform\",\n        description: \"Secure blockchain voting with on-chain verification\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Werant - Blockchain Voting Platform\",\n        description: \"Secure blockchain voting with on-chain verification\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SiteGatekeeper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Werank/src/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Werank/src/app/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/SiteGatekeeper.tsx":
/*!*******************************************!*\
  !*** ./src/components/SiteGatekeeper.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Werank/src/components/SiteGatekeeper.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/@walletconnect","vendor-chunks/viem","vendor-chunks/ox","vendor-chunks/@wagmi","vendor-chunks/@noble","vendor-chunks/next","vendor-chunks/node-fetch","vendor-chunks/@tanstack","vendor-chunks/pino-pretty","vendor-chunks/@msgpack","vendor-chunks/pino","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/big.js","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/blakejs","vendor-chunks/zustand","vendor-chunks/thread-stream","vendor-chunks/fast-copy","vendor-chunks/tslib","vendor-chunks/sonic-boom","vendor-chunks/dayjs","vendor-chunks/eventemitter3","vendor-chunks/wagmi","vendor-chunks/detect-browser","vendor-chunks/dateformat","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/fast-safe-stringify","vendor-chunks/pino-std-serializers","vendor-chunks/colorette","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/split2","vendor-chunks/secure-json-parse","vendor-chunks/end-of-stream","vendor-chunks/quick-format-unescaped","vendor-chunks/mipd","vendor-chunks/pump","vendor-chunks/@swc","vendor-chunks/destr","vendor-chunks/utf-8-validate","vendor-chunks/process-warning","vendor-chunks/atomic-sleep","vendor-chunks/bufferutil","vendor-chunks/on-exit-leak-free","vendor-chunks/once","vendor-chunks/wrappy","vendor-chunks/bs58"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fmrteesoft%2FWerank%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmrteesoft%2FWerank&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();