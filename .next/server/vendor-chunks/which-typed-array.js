"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/which-typed-array";
exports.ids = ["vendor-chunks/which-typed-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/which-typed-array/index.js":
/*!*************************************************!*\
  !*** ./node_modules/which-typed-array/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar forEach = __webpack_require__(/*! for-each */ \"(ssr)/./node_modules/for-each/index.js\");\nvar availableTypedArrays = __webpack_require__(/*! available-typed-arrays */ \"(ssr)/./node_modules/available-typed-arrays/index.js\");\nvar callBind = __webpack_require__(/*! call-bind */ \"(ssr)/./node_modules/call-bind/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(ssr)/./node_modules/call-bound/index.js\");\nvar gOPD = __webpack_require__(/*! gopd */ \"(ssr)/./node_modules/gopd/index.js\");\nvar getProto = __webpack_require__(/*! get-proto */ \"(ssr)/./node_modules/get-proto/index.js\");\n\nvar $toString = callBound('Object.prototype.toString');\nvar hasToStringTag = __webpack_require__(/*! has-tostringtag/shams */ \"(ssr)/./node_modules/has-tostringtag/shams.js\")();\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\n\nvar $slice = callBound('String.prototype.slice');\n\n/** @type {<T = unknown>(array: readonly T[], value: unknown) => number} */\nvar $indexOf = callBound('Array.prototype.indexOf', true) || function indexOf(array, value) {\n\tfor (var i = 0; i < array.length; i += 1) {\n\t\tif (array[i] === value) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n};\n\n/** @typedef {import('./types').Getter} Getter */\n/** @type {import('./types').Cache} */\nvar cache = { __proto__: null };\nif (hasToStringTag && gOPD && getProto) {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tif (Symbol.toStringTag in arr && getProto) {\n\t\t\tvar proto = getProto(arr);\n\t\t\t// @ts-expect-error TS won't narrow inside a closure\n\t\t\tvar descriptor = gOPD(proto, Symbol.toStringTag);\n\t\t\tif (!descriptor && proto) {\n\t\t\t\tvar superProto = getProto(proto);\n\t\t\t\t// @ts-expect-error TS won't narrow inside a closure\n\t\t\t\tdescriptor = gOPD(superProto, Symbol.toStringTag);\n\t\t\t}\n\t\t\t// @ts-expect-error TODO: fix\n\t\t\tcache['$' + typedArray] = callBind(descriptor.get);\n\t\t}\n\t});\n} else {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tvar fn = arr.slice || arr.set;\n\t\tif (fn) {\n\t\t\tcache[\n\t\t\t\t/** @type {`$${import('.').TypedArrayName}`} */ ('$' + typedArray)\n\t\t\t] = /** @type {import('./types').BoundSlice | import('./types').BoundSet} */ (\n\t\t\t\t// @ts-expect-error TODO FIXME\n\t\t\t\tcallBind(fn)\n\t\t\t);\n\t\t}\n\t});\n}\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n\t/** @type {ReturnType<typeof tryAllTypedArrays>} */ var found = false;\n\tforEach(\n\t\t/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */ (cache),\n\t\t/** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */\n\t\tfunction (getter, typedArray) {\n\t\t\tif (!found) {\n\t\t\t\ttry {\n\t\t\t\t\t// @ts-expect-error a throw is fine here\n\t\t\t\t\tif ('$' + getter(value) === typedArray) {\n\t\t\t\t\t\tfound = /** @type {import('.').TypedArrayName} */ ($slice(typedArray, 1));\n\t\t\t\t\t}\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t}\n\t);\n\treturn found;\n};\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar trySlices = function tryAllSlices(value) {\n\t/** @type {ReturnType<typeof tryAllSlices>} */ var found = false;\n\tforEach(\n\t\t/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */(cache),\n\t\t/** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */ function (getter, name) {\n\t\t\tif (!found) {\n\t\t\t\ttry {\n\t\t\t\t\t// @ts-expect-error a throw is fine here\n\t\t\t\t\tgetter(value);\n\t\t\t\t\tfound = /** @type {import('.').TypedArrayName} */ ($slice(name, 1));\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t}\n\t);\n\treturn found;\n};\n\n/** @type {import('.')} */\nmodule.exports = function whichTypedArray(value) {\n\tif (!value || typeof value !== 'object') { return false; }\n\tif (!hasToStringTag) {\n\t\t/** @type {string} */\n\t\tvar tag = $slice($toString(value), 8, -1);\n\t\tif ($indexOf(typedArrays, tag) > -1) {\n\t\t\treturn tag;\n\t\t}\n\t\tif (tag !== 'Object') {\n\t\t\treturn false;\n\t\t}\n\t\t// node < 0.6 hits here on real Typed Arrays\n\t\treturn trySlices(value);\n\t}\n\tif (!gOPD) { return null; } // unknown engine\n\treturn tryTypedArrays(value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/which-typed-array/index.js\n");

/***/ })

};
;