"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@msgpack";
exports.ids = ["vendor-chunks/@msgpack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CachedKeyDecoder: () => (/* binding */ CachedKeyDecoder)\n/* harmony export */ });\n/* harmony import */ var _utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/utf8.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\");\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\nclass CachedKeyDecoder {\n    constructor(maxKeyLength = DEFAULT_MAX_KEY_LENGTH, maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n        this.hit = 0;\n        this.miss = 0;\n        this.maxKeyLength = maxKeyLength;\n        this.maxLengthPerKey = maxLengthPerKey;\n        // avoid `new Array(N)`, which makes a sparse array,\n        // because a sparse array is typically slower than a non-sparse array.\n        this.caches = [];\n        for (let i = 0; i < this.maxKeyLength; i++) {\n            this.caches.push([]);\n        }\n    }\n    canBeCached(byteLength) {\n        return byteLength > 0 && byteLength <= this.maxKeyLength;\n    }\n    find(bytes, inputOffset, byteLength) {\n        const records = this.caches[byteLength - 1];\n        FIND_CHUNK: for (const record of records) {\n            const recordBytes = record.bytes;\n            for (let j = 0; j < byteLength; j++) {\n                if (recordBytes[j] !== bytes[inputOffset + j]) {\n                    continue FIND_CHUNK;\n                }\n            }\n            return record.str;\n        }\n        return null;\n    }\n    store(bytes, value) {\n        const records = this.caches[bytes.length - 1];\n        const record = { bytes, str: value };\n        if (records.length >= this.maxLengthPerKey) {\n            // `records` are full!\n            // Set `record` to an arbitrary position.\n            records[(Math.random() * records.length) | 0] = record;\n        }\n        else {\n            records.push(record);\n        }\n    }\n    decode(bytes, inputOffset, byteLength) {\n        const cachedValue = this.find(bytes, inputOffset, byteLength);\n        if (cachedValue != null) {\n            this.hit++;\n            return cachedValue;\n        }\n        this.miss++;\n        const str = (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_0__.utf8DecodeJs)(bytes, inputOffset, byteLength);\n        // Ensure to copy a slice of bytes because the bytes may be a NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n        const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n        this.store(slicedCopyOfBytes, str);\n        return str;\n    }\n}\n//# sourceMappingURL=CachedKeyDecoder.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeError: () => (/* binding */ DecodeError)\n/* harmony export */ });\nclass DecodeError extends Error {\n    constructor(message) {\n        super(message);\n        // fix the prototype chain in a cross-platform way\n        const proto = Object.create(DecodeError.prototype);\n        Object.setPrototypeOf(this, proto);\n        Object.defineProperty(this, \"name\", {\n            configurable: true,\n            enumerable: false,\n            value: DecodeError.name,\n        });\n    }\n}\n//# sourceMappingURL=DecodeError.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9EZWNvZGVFcnJvci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vRGVjb2RlRXJyb3IubWpzPzU2MjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIERlY29kZUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIC8vIGZpeCB0aGUgcHJvdG90eXBlIGNoYWluIGluIGEgY3Jvc3MtcGxhdGZvcm0gd2F5XG4gICAgICAgIGNvbnN0IHByb3RvID0gT2JqZWN0LmNyZWF0ZShEZWNvZGVFcnJvci5wcm90b3R5cGUpO1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgcHJvdG8pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgdmFsdWU6IERlY29kZUVycm9yLm5hbWUsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPURlY29kZUVycm9yLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decoder: () => (/* binding */ Decoder)\n/* harmony export */ });\n/* harmony import */ var _utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/prettyByte.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs\");\n/* harmony import */ var _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ExtensionCodec.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\");\n/* harmony import */ var _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/int.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\");\n/* harmony import */ var _utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/utf8.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\");\n/* harmony import */ var _utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/typedArrays.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs\");\n/* harmony import */ var _CachedKeyDecoder_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CachedKeyDecoder.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs\");\n/* harmony import */ var _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DecodeError.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\");\n\n\n\n\n\n\n\nconst STATE_ARRAY = \"array\";\nconst STATE_MAP_KEY = \"map_key\";\nconst STATE_MAP_VALUE = \"map_value\";\nconst mapKeyConverter = (key) => {\n    if (typeof key === \"string\" || typeof key === \"number\") {\n        return key;\n    }\n    throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(\"The type of key must be string or number but \" + typeof key);\n};\nclass StackPool {\n    constructor() {\n        this.stack = [];\n        this.stackHeadPosition = -1;\n    }\n    get length() {\n        return this.stackHeadPosition + 1;\n    }\n    top() {\n        return this.stack[this.stackHeadPosition];\n    }\n    pushArrayState(size) {\n        const state = this.getUninitializedStateFromPool();\n        state.type = STATE_ARRAY;\n        state.position = 0;\n        state.size = size;\n        state.array = new Array(size);\n    }\n    pushMapState(size) {\n        const state = this.getUninitializedStateFromPool();\n        state.type = STATE_MAP_KEY;\n        state.readCount = 0;\n        state.size = size;\n        state.map = {};\n    }\n    getUninitializedStateFromPool() {\n        this.stackHeadPosition++;\n        if (this.stackHeadPosition === this.stack.length) {\n            const partialState = {\n                type: undefined,\n                size: 0,\n                array: undefined,\n                position: 0,\n                readCount: 0,\n                map: undefined,\n                key: null,\n            };\n            this.stack.push(partialState);\n        }\n        return this.stack[this.stackHeadPosition];\n    }\n    release(state) {\n        const topStackState = this.stack[this.stackHeadPosition];\n        if (topStackState !== state) {\n            throw new Error(\"Invalid stack state. Released state is not on top of the stack.\");\n        }\n        if (state.type === STATE_ARRAY) {\n            const partialState = state;\n            partialState.size = 0;\n            partialState.array = undefined;\n            partialState.position = 0;\n            partialState.type = undefined;\n        }\n        if (state.type === STATE_MAP_KEY || state.type === STATE_MAP_VALUE) {\n            const partialState = state;\n            partialState.size = 0;\n            partialState.map = undefined;\n            partialState.readCount = 0;\n            partialState.type = undefined;\n        }\n        this.stackHeadPosition--;\n    }\n    reset() {\n        this.stack.length = 0;\n        this.stackHeadPosition = -1;\n    }\n}\nconst HEAD_BYTE_REQUIRED = -1;\nconst EMPTY_VIEW = new DataView(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);\ntry {\n    // IE11: The spec says it should throw RangeError,\n    // IE11: but in IE11 it throws TypeError.\n    EMPTY_VIEW.getInt8(0);\n}\ncatch (e) {\n    if (!(e instanceof RangeError)) {\n        throw new Error(\"This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access\");\n    }\n}\nconst MORE_DATA = new RangeError(\"Insufficient data\");\nconst sharedCachedKeyDecoder = new _CachedKeyDecoder_mjs__WEBPACK_IMPORTED_MODULE_1__.CachedKeyDecoder();\nclass Decoder {\n    constructor(options) {\n        this.totalPos = 0;\n        this.pos = 0;\n        this.view = EMPTY_VIEW;\n        this.bytes = EMPTY_BYTES;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack = new StackPool();\n        this.entered = false;\n        this.extensionCodec = options?.extensionCodec ?? _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_2__.ExtensionCodec.defaultCodec;\n        this.context = options?.context; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n        this.useBigInt64 = options?.useBigInt64 ?? false;\n        this.rawStrings = options?.rawStrings ?? false;\n        this.maxStrLength = options?.maxStrLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxBinLength = options?.maxBinLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxArrayLength = options?.maxArrayLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxMapLength = options?.maxMapLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxExtLength = options?.maxExtLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.keyDecoder = options?.keyDecoder !== undefined ? options.keyDecoder : sharedCachedKeyDecoder;\n        this.mapKeyConverter = options?.mapKeyConverter ?? mapKeyConverter;\n    }\n    clone() {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        return new Decoder({\n            extensionCodec: this.extensionCodec,\n            context: this.context,\n            useBigInt64: this.useBigInt64,\n            rawStrings: this.rawStrings,\n            maxStrLength: this.maxStrLength,\n            maxBinLength: this.maxBinLength,\n            maxArrayLength: this.maxArrayLength,\n            maxMapLength: this.maxMapLength,\n            maxExtLength: this.maxExtLength,\n            keyDecoder: this.keyDecoder,\n        });\n    }\n    reinitializeState() {\n        this.totalPos = 0;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack.reset();\n        // view, bytes, and pos will be re-initialized in setBuffer()\n    }\n    setBuffer(buffer) {\n        const bytes = (0,_utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_4__.ensureUint8Array)(buffer);\n        this.bytes = bytes;\n        this.view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n        this.pos = 0;\n    }\n    appendBuffer(buffer) {\n        if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n            this.setBuffer(buffer);\n        }\n        else {\n            const remainingData = this.bytes.subarray(this.pos);\n            const newData = (0,_utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_4__.ensureUint8Array)(buffer);\n            // concat remainingData + newData\n            const newBuffer = new Uint8Array(remainingData.length + newData.length);\n            newBuffer.set(remainingData);\n            newBuffer.set(newData, remainingData.length);\n            this.setBuffer(newBuffer);\n        }\n    }\n    hasRemaining(size) {\n        return this.view.byteLength - this.pos >= size;\n    }\n    createExtraByteError(posToShow) {\n        const { view, pos } = this;\n        return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n    }\n    /**\n     * @throws {@link DecodeError}\n     * @throws {@link RangeError}\n     */\n    decode(buffer) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.decode(buffer);\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.setBuffer(buffer);\n            const object = this.doDecodeSync();\n            if (this.hasRemaining(1)) {\n                throw this.createExtraByteError(this.pos);\n            }\n            return object;\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    *decodeMulti(buffer) {\n        if (this.entered) {\n            const instance = this.clone();\n            yield* instance.decodeMulti(buffer);\n            return;\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.setBuffer(buffer);\n            while (this.hasRemaining(1)) {\n                yield this.doDecodeSync();\n            }\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    async decodeAsync(stream) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.decodeAsync(stream);\n        }\n        try {\n            this.entered = true;\n            let decoded = false;\n            let object;\n            for await (const buffer of stream) {\n                if (decoded) {\n                    this.entered = false;\n                    throw this.createExtraByteError(this.totalPos);\n                }\n                this.appendBuffer(buffer);\n                try {\n                    object = this.doDecodeSync();\n                    decoded = true;\n                }\n                catch (e) {\n                    if (!(e instanceof RangeError)) {\n                        throw e; // rethrow\n                    }\n                    // fallthrough\n                }\n                this.totalPos += this.pos;\n            }\n            if (decoded) {\n                if (this.hasRemaining(1)) {\n                    throw this.createExtraByteError(this.totalPos);\n                }\n                return object;\n            }\n            const { headByte, pos, totalPos } = this;\n            throw new RangeError(`Insufficient data in parsing ${(0,_utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__.prettyByte)(headByte)} at ${totalPos} (${pos} in the current buffer)`);\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    decodeArrayStream(stream) {\n        return this.decodeMultiAsync(stream, true);\n    }\n    decodeStream(stream) {\n        return this.decodeMultiAsync(stream, false);\n    }\n    async *decodeMultiAsync(stream, isArray) {\n        if (this.entered) {\n            const instance = this.clone();\n            yield* instance.decodeMultiAsync(stream, isArray);\n            return;\n        }\n        try {\n            this.entered = true;\n            let isArrayHeaderRequired = isArray;\n            let arrayItemsLeft = -1;\n            for await (const buffer of stream) {\n                if (isArray && arrayItemsLeft === 0) {\n                    throw this.createExtraByteError(this.totalPos);\n                }\n                this.appendBuffer(buffer);\n                if (isArrayHeaderRequired) {\n                    arrayItemsLeft = this.readArraySize();\n                    isArrayHeaderRequired = false;\n                    this.complete();\n                }\n                try {\n                    while (true) {\n                        yield this.doDecodeSync();\n                        if (--arrayItemsLeft === 0) {\n                            break;\n                        }\n                    }\n                }\n                catch (e) {\n                    if (!(e instanceof RangeError)) {\n                        throw e; // rethrow\n                    }\n                    // fallthrough\n                }\n                this.totalPos += this.pos;\n            }\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    doDecodeSync() {\n        DECODE: while (true) {\n            const headByte = this.readHeadByte();\n            let object;\n            if (headByte >= 0xe0) {\n                // negative fixint (111x xxxx) 0xe0 - 0xff\n                object = headByte - 0x100;\n            }\n            else if (headByte < 0xc0) {\n                if (headByte < 0x80) {\n                    // positive fixint (0xxx xxxx) 0x00 - 0x7f\n                    object = headByte;\n                }\n                else if (headByte < 0x90) {\n                    // fixmap (1000 xxxx) 0x80 - 0x8f\n                    const size = headByte - 0x80;\n                    if (size !== 0) {\n                        this.pushMapState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = {};\n                    }\n                }\n                else if (headByte < 0xa0) {\n                    // fixarray (1001 xxxx) 0x90 - 0x9f\n                    const size = headByte - 0x90;\n                    if (size !== 0) {\n                        this.pushArrayState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = [];\n                    }\n                }\n                else {\n                    // fixstr (101x xxxx) 0xa0 - 0xbf\n                    const byteLength = headByte - 0xa0;\n                    object = this.decodeString(byteLength, 0);\n                }\n            }\n            else if (headByte === 0xc0) {\n                // nil\n                object = null;\n            }\n            else if (headByte === 0xc2) {\n                // false\n                object = false;\n            }\n            else if (headByte === 0xc3) {\n                // true\n                object = true;\n            }\n            else if (headByte === 0xca) {\n                // float 32\n                object = this.readF32();\n            }\n            else if (headByte === 0xcb) {\n                // float 64\n                object = this.readF64();\n            }\n            else if (headByte === 0xcc) {\n                // uint 8\n                object = this.readU8();\n            }\n            else if (headByte === 0xcd) {\n                // uint 16\n                object = this.readU16();\n            }\n            else if (headByte === 0xce) {\n                // uint 32\n                object = this.readU32();\n            }\n            else if (headByte === 0xcf) {\n                // uint 64\n                if (this.useBigInt64) {\n                    object = this.readU64AsBigInt();\n                }\n                else {\n                    object = this.readU64();\n                }\n            }\n            else if (headByte === 0xd0) {\n                // int 8\n                object = this.readI8();\n            }\n            else if (headByte === 0xd1) {\n                // int 16\n                object = this.readI16();\n            }\n            else if (headByte === 0xd2) {\n                // int 32\n                object = this.readI32();\n            }\n            else if (headByte === 0xd3) {\n                // int 64\n                if (this.useBigInt64) {\n                    object = this.readI64AsBigInt();\n                }\n                else {\n                    object = this.readI64();\n                }\n            }\n            else if (headByte === 0xd9) {\n                // str 8\n                const byteLength = this.lookU8();\n                object = this.decodeString(byteLength, 1);\n            }\n            else if (headByte === 0xda) {\n                // str 16\n                const byteLength = this.lookU16();\n                object = this.decodeString(byteLength, 2);\n            }\n            else if (headByte === 0xdb) {\n                // str 32\n                const byteLength = this.lookU32();\n                object = this.decodeString(byteLength, 4);\n            }\n            else if (headByte === 0xdc) {\n                // array 16\n                const size = this.readU16();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xdd) {\n                // array 32\n                const size = this.readU32();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xde) {\n                // map 16\n                const size = this.readU16();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xdf) {\n                // map 32\n                const size = this.readU32();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xc4) {\n                // bin 8\n                const size = this.lookU8();\n                object = this.decodeBinary(size, 1);\n            }\n            else if (headByte === 0xc5) {\n                // bin 16\n                const size = this.lookU16();\n                object = this.decodeBinary(size, 2);\n            }\n            else if (headByte === 0xc6) {\n                // bin 32\n                const size = this.lookU32();\n                object = this.decodeBinary(size, 4);\n            }\n            else if (headByte === 0xd4) {\n                // fixext 1\n                object = this.decodeExtension(1, 0);\n            }\n            else if (headByte === 0xd5) {\n                // fixext 2\n                object = this.decodeExtension(2, 0);\n            }\n            else if (headByte === 0xd6) {\n                // fixext 4\n                object = this.decodeExtension(4, 0);\n            }\n            else if (headByte === 0xd7) {\n                // fixext 8\n                object = this.decodeExtension(8, 0);\n            }\n            else if (headByte === 0xd8) {\n                // fixext 16\n                object = this.decodeExtension(16, 0);\n            }\n            else if (headByte === 0xc7) {\n                // ext 8\n                const size = this.lookU8();\n                object = this.decodeExtension(size, 1);\n            }\n            else if (headByte === 0xc8) {\n                // ext 16\n                const size = this.lookU16();\n                object = this.decodeExtension(size, 2);\n            }\n            else if (headByte === 0xc9) {\n                // ext 32\n                const size = this.lookU32();\n                object = this.decodeExtension(size, 4);\n            }\n            else {\n                throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Unrecognized type byte: ${(0,_utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__.prettyByte)(headByte)}`);\n            }\n            this.complete();\n            const stack = this.stack;\n            while (stack.length > 0) {\n                // arrays and maps\n                const state = stack.top();\n                if (state.type === STATE_ARRAY) {\n                    state.array[state.position] = object;\n                    state.position++;\n                    if (state.position === state.size) {\n                        object = state.array;\n                        stack.release(state);\n                    }\n                    else {\n                        continue DECODE;\n                    }\n                }\n                else if (state.type === STATE_MAP_KEY) {\n                    if (object === \"__proto__\") {\n                        throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(\"The key __proto__ is not allowed\");\n                    }\n                    state.key = this.mapKeyConverter(object);\n                    state.type = STATE_MAP_VALUE;\n                    continue DECODE;\n                }\n                else {\n                    // it must be `state.type === State.MAP_VALUE` here\n                    state.map[state.key] = object;\n                    state.readCount++;\n                    if (state.readCount === state.size) {\n                        object = state.map;\n                        stack.release(state);\n                    }\n                    else {\n                        state.key = null;\n                        state.type = STATE_MAP_KEY;\n                        continue DECODE;\n                    }\n                }\n            }\n            return object;\n        }\n    }\n    readHeadByte() {\n        if (this.headByte === HEAD_BYTE_REQUIRED) {\n            this.headByte = this.readU8();\n            // console.log(\"headByte\", prettyByte(this.headByte));\n        }\n        return this.headByte;\n    }\n    complete() {\n        this.headByte = HEAD_BYTE_REQUIRED;\n    }\n    readArraySize() {\n        const headByte = this.readHeadByte();\n        switch (headByte) {\n            case 0xdc:\n                return this.readU16();\n            case 0xdd:\n                return this.readU32();\n            default: {\n                if (headByte < 0xa0) {\n                    return headByte - 0x90;\n                }\n                else {\n                    throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Unrecognized array type byte: ${(0,_utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__.prettyByte)(headByte)}`);\n                }\n            }\n        }\n    }\n    pushMapState(size) {\n        if (size > this.maxMapLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n        }\n        this.stack.pushMapState(size);\n    }\n    pushArrayState(size) {\n        if (size > this.maxArrayLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n        }\n        this.stack.pushArrayState(size);\n    }\n    decodeString(byteLength, headerOffset) {\n        if (!this.rawStrings || this.stateIsMapKey()) {\n            return this.decodeUtf8String(byteLength, headerOffset);\n        }\n        return this.decodeBinary(byteLength, headerOffset);\n    }\n    /**\n     * @throws {@link RangeError}\n     */\n    decodeUtf8String(byteLength, headerOffset) {\n        if (byteLength > this.maxStrLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`);\n        }\n        if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n            throw MORE_DATA;\n        }\n        const offset = this.pos + headerOffset;\n        let object;\n        if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n            object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n        }\n        else {\n            object = (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_6__.utf8Decode)(this.bytes, offset, byteLength);\n        }\n        this.pos += headerOffset + byteLength;\n        return object;\n    }\n    stateIsMapKey() {\n        if (this.stack.length > 0) {\n            const state = this.stack.top();\n            return state.type === STATE_MAP_KEY;\n        }\n        return false;\n    }\n    /**\n     * @throws {@link RangeError}\n     */\n    decodeBinary(byteLength, headOffset) {\n        if (byteLength > this.maxBinLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n        }\n        if (!this.hasRemaining(byteLength + headOffset)) {\n            throw MORE_DATA;\n        }\n        const offset = this.pos + headOffset;\n        const object = this.bytes.subarray(offset, offset + byteLength);\n        this.pos += headOffset + byteLength;\n        return object;\n    }\n    decodeExtension(size, headOffset) {\n        if (size > this.maxExtLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n        }\n        const extType = this.view.getInt8(this.pos + headOffset);\n        const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n        return this.extensionCodec.decode(data, extType, this.context);\n    }\n    lookU8() {\n        return this.view.getUint8(this.pos);\n    }\n    lookU16() {\n        return this.view.getUint16(this.pos);\n    }\n    lookU32() {\n        return this.view.getUint32(this.pos);\n    }\n    readU8() {\n        const value = this.view.getUint8(this.pos);\n        this.pos++;\n        return value;\n    }\n    readI8() {\n        const value = this.view.getInt8(this.pos);\n        this.pos++;\n        return value;\n    }\n    readU16() {\n        const value = this.view.getUint16(this.pos);\n        this.pos += 2;\n        return value;\n    }\n    readI16() {\n        const value = this.view.getInt16(this.pos);\n        this.pos += 2;\n        return value;\n    }\n    readU32() {\n        const value = this.view.getUint32(this.pos);\n        this.pos += 4;\n        return value;\n    }\n    readI32() {\n        const value = this.view.getInt32(this.pos);\n        this.pos += 4;\n        return value;\n    }\n    readU64() {\n        const value = (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.getUint64)(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readI64() {\n        const value = (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.getInt64)(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readU64AsBigInt() {\n        const value = this.view.getBigUint64(this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readI64AsBigInt() {\n        const value = this.view.getBigInt64(this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readF32() {\n        const value = this.view.getFloat32(this.pos);\n        this.pos += 4;\n        return value;\n    }\n    readF64() {\n        const value = this.view.getFloat64(this.pos);\n        this.pos += 8;\n        return value;\n    }\n}\n//# sourceMappingURL=Decoder.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_INITIAL_BUFFER_SIZE: () => (/* binding */ DEFAULT_INITIAL_BUFFER_SIZE),\n/* harmony export */   DEFAULT_MAX_DEPTH: () => (/* binding */ DEFAULT_MAX_DEPTH),\n/* harmony export */   Encoder: () => (/* binding */ Encoder)\n/* harmony export */ });\n/* harmony import */ var _utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/utf8.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\");\n/* harmony import */ var _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ExtensionCodec.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\");\n/* harmony import */ var _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/int.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\");\n/* harmony import */ var _utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/typedArrays.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs\");\n\n\n\n\nconst DEFAULT_MAX_DEPTH = 100;\nconst DEFAULT_INITIAL_BUFFER_SIZE = 2048;\nclass Encoder {\n    constructor(options) {\n        this.entered = false;\n        this.extensionCodec = options?.extensionCodec ?? _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_0__.ExtensionCodec.defaultCodec;\n        this.context = options?.context; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n        this.useBigInt64 = options?.useBigInt64 ?? false;\n        this.maxDepth = options?.maxDepth ?? DEFAULT_MAX_DEPTH;\n        this.initialBufferSize = options?.initialBufferSize ?? DEFAULT_INITIAL_BUFFER_SIZE;\n        this.sortKeys = options?.sortKeys ?? false;\n        this.forceFloat32 = options?.forceFloat32 ?? false;\n        this.ignoreUndefined = options?.ignoreUndefined ?? false;\n        this.forceIntegerToFloat = options?.forceIntegerToFloat ?? false;\n        this.pos = 0;\n        this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n        this.bytes = new Uint8Array(this.view.buffer);\n    }\n    clone() {\n        // Because of slightly special argument `context`,\n        // type assertion is needed.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        return new Encoder({\n            extensionCodec: this.extensionCodec,\n            context: this.context,\n            useBigInt64: this.useBigInt64,\n            maxDepth: this.maxDepth,\n            initialBufferSize: this.initialBufferSize,\n            sortKeys: this.sortKeys,\n            forceFloat32: this.forceFloat32,\n            ignoreUndefined: this.ignoreUndefined,\n            forceIntegerToFloat: this.forceIntegerToFloat,\n        });\n    }\n    reinitializeState() {\n        this.pos = 0;\n    }\n    /**\n     * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n     *\n     * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n     */\n    encodeSharedRef(object) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.encodeSharedRef(object);\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.doEncode(object, 1);\n            return this.bytes.subarray(0, this.pos);\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    /**\n     * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n     */\n    encode(object) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.encode(object);\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.doEncode(object, 1);\n            return this.bytes.slice(0, this.pos);\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    doEncode(object, depth) {\n        if (depth > this.maxDepth) {\n            throw new Error(`Too deep objects in depth ${depth}`);\n        }\n        if (object == null) {\n            this.encodeNil();\n        }\n        else if (typeof object === \"boolean\") {\n            this.encodeBoolean(object);\n        }\n        else if (typeof object === \"number\") {\n            if (!this.forceIntegerToFloat) {\n                this.encodeNumber(object);\n            }\n            else {\n                this.encodeNumberAsFloat(object);\n            }\n        }\n        else if (typeof object === \"string\") {\n            this.encodeString(object);\n        }\n        else if (this.useBigInt64 && typeof object === \"bigint\") {\n            this.encodeBigInt64(object);\n        }\n        else {\n            this.encodeObject(object, depth);\n        }\n    }\n    ensureBufferSizeToWrite(sizeToWrite) {\n        const requiredSize = this.pos + sizeToWrite;\n        if (this.view.byteLength < requiredSize) {\n            this.resizeBuffer(requiredSize * 2);\n        }\n    }\n    resizeBuffer(newSize) {\n        const newBuffer = new ArrayBuffer(newSize);\n        const newBytes = new Uint8Array(newBuffer);\n        const newView = new DataView(newBuffer);\n        newBytes.set(this.bytes);\n        this.view = newView;\n        this.bytes = newBytes;\n    }\n    encodeNil() {\n        this.writeU8(0xc0);\n    }\n    encodeBoolean(object) {\n        if (object === false) {\n            this.writeU8(0xc2);\n        }\n        else {\n            this.writeU8(0xc3);\n        }\n    }\n    encodeNumber(object) {\n        if (!this.forceIntegerToFloat && Number.isSafeInteger(object)) {\n            if (object >= 0) {\n                if (object < 0x80) {\n                    // positive fixint\n                    this.writeU8(object);\n                }\n                else if (object < 0x100) {\n                    // uint 8\n                    this.writeU8(0xcc);\n                    this.writeU8(object);\n                }\n                else if (object < 0x10000) {\n                    // uint 16\n                    this.writeU8(0xcd);\n                    this.writeU16(object);\n                }\n                else if (object < 0x100000000) {\n                    // uint 32\n                    this.writeU8(0xce);\n                    this.writeU32(object);\n                }\n                else if (!this.useBigInt64) {\n                    // uint 64\n                    this.writeU8(0xcf);\n                    this.writeU64(object);\n                }\n                else {\n                    this.encodeNumberAsFloat(object);\n                }\n            }\n            else {\n                if (object >= -0x20) {\n                    // negative fixint\n                    this.writeU8(0xe0 | (object + 0x20));\n                }\n                else if (object >= -0x80) {\n                    // int 8\n                    this.writeU8(0xd0);\n                    this.writeI8(object);\n                }\n                else if (object >= -0x8000) {\n                    // int 16\n                    this.writeU8(0xd1);\n                    this.writeI16(object);\n                }\n                else if (object >= -0x80000000) {\n                    // int 32\n                    this.writeU8(0xd2);\n                    this.writeI32(object);\n                }\n                else if (!this.useBigInt64) {\n                    // int 64\n                    this.writeU8(0xd3);\n                    this.writeI64(object);\n                }\n                else {\n                    this.encodeNumberAsFloat(object);\n                }\n            }\n        }\n        else {\n            this.encodeNumberAsFloat(object);\n        }\n    }\n    encodeNumberAsFloat(object) {\n        if (this.forceFloat32) {\n            // float 32\n            this.writeU8(0xca);\n            this.writeF32(object);\n        }\n        else {\n            // float 64\n            this.writeU8(0xcb);\n            this.writeF64(object);\n        }\n    }\n    encodeBigInt64(object) {\n        if (object >= BigInt(0)) {\n            // uint 64\n            this.writeU8(0xcf);\n            this.writeBigUint64(object);\n        }\n        else {\n            // int 64\n            this.writeU8(0xd3);\n            this.writeBigInt64(object);\n        }\n    }\n    writeStringHeader(byteLength) {\n        if (byteLength < 32) {\n            // fixstr\n            this.writeU8(0xa0 + byteLength);\n        }\n        else if (byteLength < 0x100) {\n            // str 8\n            this.writeU8(0xd9);\n            this.writeU8(byteLength);\n        }\n        else if (byteLength < 0x10000) {\n            // str 16\n            this.writeU8(0xda);\n            this.writeU16(byteLength);\n        }\n        else if (byteLength < 0x100000000) {\n            // str 32\n            this.writeU8(0xdb);\n            this.writeU32(byteLength);\n        }\n        else {\n            throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n        }\n    }\n    encodeString(object) {\n        const maxHeaderSize = 1 + 4;\n        const byteLength = (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_1__.utf8Count)(object);\n        this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n        this.writeStringHeader(byteLength);\n        (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_1__.utf8Encode)(object, this.bytes, this.pos);\n        this.pos += byteLength;\n    }\n    encodeObject(object, depth) {\n        // try to encode objects with custom codec first of non-primitives\n        const ext = this.extensionCodec.tryToEncode(object, this.context);\n        if (ext != null) {\n            this.encodeExtension(ext);\n        }\n        else if (Array.isArray(object)) {\n            this.encodeArray(object, depth);\n        }\n        else if (ArrayBuffer.isView(object)) {\n            this.encodeBinary(object);\n        }\n        else if (typeof object === \"object\") {\n            this.encodeMap(object, depth);\n        }\n        else {\n            // symbol, function and other special object come here unless extensionCodec handles them.\n            throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n        }\n    }\n    encodeBinary(object) {\n        const size = object.byteLength;\n        if (size < 0x100) {\n            // bin 8\n            this.writeU8(0xc4);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // bin 16\n            this.writeU8(0xc5);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // bin 32\n            this.writeU8(0xc6);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large binary: ${size}`);\n        }\n        const bytes = (0,_utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_2__.ensureUint8Array)(object);\n        this.writeU8a(bytes);\n    }\n    encodeArray(object, depth) {\n        const size = object.length;\n        if (size < 16) {\n            // fixarray\n            this.writeU8(0x90 + size);\n        }\n        else if (size < 0x10000) {\n            // array 16\n            this.writeU8(0xdc);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // array 32\n            this.writeU8(0xdd);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large array: ${size}`);\n        }\n        for (const item of object) {\n            this.doEncode(item, depth + 1);\n        }\n    }\n    countWithoutUndefined(object, keys) {\n        let count = 0;\n        for (const key of keys) {\n            if (object[key] !== undefined) {\n                count++;\n            }\n        }\n        return count;\n    }\n    encodeMap(object, depth) {\n        const keys = Object.keys(object);\n        if (this.sortKeys) {\n            keys.sort();\n        }\n        const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n        if (size < 16) {\n            // fixmap\n            this.writeU8(0x80 + size);\n        }\n        else if (size < 0x10000) {\n            // map 16\n            this.writeU8(0xde);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // map 32\n            this.writeU8(0xdf);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large map object: ${size}`);\n        }\n        for (const key of keys) {\n            const value = object[key];\n            if (!(this.ignoreUndefined && value === undefined)) {\n                this.encodeString(key);\n                this.doEncode(value, depth + 1);\n            }\n        }\n    }\n    encodeExtension(ext) {\n        if (typeof ext.data === \"function\") {\n            const data = ext.data(this.pos + 6);\n            const size = data.length;\n            if (size >= 0x100000000) {\n                throw new Error(`Too large extension object: ${size}`);\n            }\n            this.writeU8(0xc9);\n            this.writeU32(size);\n            this.writeI8(ext.type);\n            this.writeU8a(data);\n            return;\n        }\n        const size = ext.data.length;\n        if (size === 1) {\n            // fixext 1\n            this.writeU8(0xd4);\n        }\n        else if (size === 2) {\n            // fixext 2\n            this.writeU8(0xd5);\n        }\n        else if (size === 4) {\n            // fixext 4\n            this.writeU8(0xd6);\n        }\n        else if (size === 8) {\n            // fixext 8\n            this.writeU8(0xd7);\n        }\n        else if (size === 16) {\n            // fixext 16\n            this.writeU8(0xd8);\n        }\n        else if (size < 0x100) {\n            // ext 8\n            this.writeU8(0xc7);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // ext 16\n            this.writeU8(0xc8);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // ext 32\n            this.writeU8(0xc9);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large extension object: ${size}`);\n        }\n        this.writeI8(ext.type);\n        this.writeU8a(ext.data);\n    }\n    writeU8(value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setUint8(this.pos, value);\n        this.pos++;\n    }\n    writeU8a(values) {\n        const size = values.length;\n        this.ensureBufferSizeToWrite(size);\n        this.bytes.set(values, this.pos);\n        this.pos += size;\n    }\n    writeI8(value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setInt8(this.pos, value);\n        this.pos++;\n    }\n    writeU16(value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setUint16(this.pos, value);\n        this.pos += 2;\n    }\n    writeI16(value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setInt16(this.pos, value);\n        this.pos += 2;\n    }\n    writeU32(value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setUint32(this.pos, value);\n        this.pos += 4;\n    }\n    writeI32(value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setInt32(this.pos, value);\n        this.pos += 4;\n    }\n    writeF32(value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setFloat32(this.pos, value);\n        this.pos += 4;\n    }\n    writeF64(value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setFloat64(this.pos, value);\n        this.pos += 8;\n    }\n    writeU64(value) {\n        this.ensureBufferSizeToWrite(8);\n        (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.setUint64)(this.view, this.pos, value);\n        this.pos += 8;\n    }\n    writeI64(value) {\n        this.ensureBufferSizeToWrite(8);\n        (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.setInt64)(this.view, this.pos, value);\n        this.pos += 8;\n    }\n    writeBigUint64(value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setBigUint64(this.pos, value);\n        this.pos += 8;\n    }\n    writeBigInt64(value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setBigInt64(this.pos, value);\n        this.pos += 8;\n    }\n}\n//# sourceMappingURL=Encoder.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9FbmNvZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXlEO0FBQ0g7QUFDQTtBQUNLO0FBQ3BEO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQSx5REFBeUQsK0RBQWM7QUFDdkUseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMscUJBQXFCLDBGQUEwRixxQkFBcUI7QUFDeks7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxNQUFNO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELFlBQVk7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsMERBQVM7QUFDcEM7QUFDQTtBQUNBLFFBQVEsMkRBQVU7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Qsd0NBQXdDO0FBQzVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsS0FBSztBQUN0RDtBQUNBLHNCQUFzQix3RUFBZ0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsS0FBSztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELEtBQUs7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtEQUErRCxLQUFLO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsS0FBSztBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEseURBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLHdEQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9AbXNncGFjay9tc2dwYWNrL2Rpc3QuZXNtL0VuY29kZXIubWpzP2VmOTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXRmOENvdW50LCB1dGY4RW5jb2RlIH0gZnJvbSBcIi4vdXRpbHMvdXRmOC5tanNcIjtcbmltcG9ydCB7IEV4dGVuc2lvbkNvZGVjIH0gZnJvbSBcIi4vRXh0ZW5zaW9uQ29kZWMubWpzXCI7XG5pbXBvcnQgeyBzZXRJbnQ2NCwgc2V0VWludDY0IH0gZnJvbSBcIi4vdXRpbHMvaW50Lm1qc1wiO1xuaW1wb3J0IHsgZW5zdXJlVWludDhBcnJheSB9IGZyb20gXCIuL3V0aWxzL3R5cGVkQXJyYXlzLm1qc1wiO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfTUFYX0RFUFRIID0gMTAwO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfSU5JVElBTF9CVUZGRVJfU0laRSA9IDIwNDg7XG5leHBvcnQgY2xhc3MgRW5jb2RlciB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICB0aGlzLmVudGVyZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5leHRlbnNpb25Db2RlYyA9IG9wdGlvbnM/LmV4dGVuc2lvbkNvZGVjID8/IEV4dGVuc2lvbkNvZGVjLmRlZmF1bHRDb2RlYztcbiAgICAgICAgdGhpcy5jb250ZXh0ID0gb3B0aW9ucz8uY29udGV4dDsgLy8gbmVlZHMgYSB0eXBlIGFzc2VydGlvbiBiZWNhdXNlIEVuY29kZXJPcHRpb25zIGhhcyBubyBjb250ZXh0IHByb3BlcnR5IHdoZW4gQ29udGV4dFR5cGUgaXMgdW5kZWZpbmVkXG4gICAgICAgIHRoaXMudXNlQmlnSW50NjQgPSBvcHRpb25zPy51c2VCaWdJbnQ2NCA/PyBmYWxzZTtcbiAgICAgICAgdGhpcy5tYXhEZXB0aCA9IG9wdGlvbnM/Lm1heERlcHRoID8/IERFRkFVTFRfTUFYX0RFUFRIO1xuICAgICAgICB0aGlzLmluaXRpYWxCdWZmZXJTaXplID0gb3B0aW9ucz8uaW5pdGlhbEJ1ZmZlclNpemUgPz8gREVGQVVMVF9JTklUSUFMX0JVRkZFUl9TSVpFO1xuICAgICAgICB0aGlzLnNvcnRLZXlzID0gb3B0aW9ucz8uc29ydEtleXMgPz8gZmFsc2U7XG4gICAgICAgIHRoaXMuZm9yY2VGbG9hdDMyID0gb3B0aW9ucz8uZm9yY2VGbG9hdDMyID8/IGZhbHNlO1xuICAgICAgICB0aGlzLmlnbm9yZVVuZGVmaW5lZCA9IG9wdGlvbnM/Lmlnbm9yZVVuZGVmaW5lZCA/PyBmYWxzZTtcbiAgICAgICAgdGhpcy5mb3JjZUludGVnZXJUb0Zsb2F0ID0gb3B0aW9ucz8uZm9yY2VJbnRlZ2VyVG9GbG9hdCA/PyBmYWxzZTtcbiAgICAgICAgdGhpcy5wb3MgPSAwO1xuICAgICAgICB0aGlzLnZpZXcgPSBuZXcgRGF0YVZpZXcobmV3IEFycmF5QnVmZmVyKHRoaXMuaW5pdGlhbEJ1ZmZlclNpemUpKTtcbiAgICAgICAgdGhpcy5ieXRlcyA9IG5ldyBVaW50OEFycmF5KHRoaXMudmlldy5idWZmZXIpO1xuICAgIH1cbiAgICBjbG9uZSgpIHtcbiAgICAgICAgLy8gQmVjYXVzZSBvZiBzbGlnaHRseSBzcGVjaWFsIGFyZ3VtZW50IGBjb250ZXh0YCxcbiAgICAgICAgLy8gdHlwZSBhc3NlcnRpb24gaXMgbmVlZGVkLlxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVuc2FmZS1hcmd1bWVudFxuICAgICAgICByZXR1cm4gbmV3IEVuY29kZXIoe1xuICAgICAgICAgICAgZXh0ZW5zaW9uQ29kZWM6IHRoaXMuZXh0ZW5zaW9uQ29kZWMsXG4gICAgICAgICAgICBjb250ZXh0OiB0aGlzLmNvbnRleHQsXG4gICAgICAgICAgICB1c2VCaWdJbnQ2NDogdGhpcy51c2VCaWdJbnQ2NCxcbiAgICAgICAgICAgIG1heERlcHRoOiB0aGlzLm1heERlcHRoLFxuICAgICAgICAgICAgaW5pdGlhbEJ1ZmZlclNpemU6IHRoaXMuaW5pdGlhbEJ1ZmZlclNpemUsXG4gICAgICAgICAgICBzb3J0S2V5czogdGhpcy5zb3J0S2V5cyxcbiAgICAgICAgICAgIGZvcmNlRmxvYXQzMjogdGhpcy5mb3JjZUZsb2F0MzIsXG4gICAgICAgICAgICBpZ25vcmVVbmRlZmluZWQ6IHRoaXMuaWdub3JlVW5kZWZpbmVkLFxuICAgICAgICAgICAgZm9yY2VJbnRlZ2VyVG9GbG9hdDogdGhpcy5mb3JjZUludGVnZXJUb0Zsb2F0LFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmVpbml0aWFsaXplU3RhdGUoKSB7XG4gICAgICAgIHRoaXMucG9zID0gMDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVGhpcyBpcyBhbG1vc3QgZXF1aXZhbGVudCB0byB7QGxpbmsgRW5jb2RlciNlbmNvZGV9LCBidXQgaXQgcmV0dXJucyBhbiByZWZlcmVuY2Ugb2YgdGhlIGVuY29kZXIncyBpbnRlcm5hbCBidWZmZXIgYW5kIHRodXMgbXVjaCBmYXN0ZXIgdGhhbiB7QGxpbmsgRW5jb2RlciNlbmNvZGV9LlxuICAgICAqXG4gICAgICogQHJldHVybnMgRW5jb2RlcyB0aGUgb2JqZWN0IGFuZCByZXR1cm5zIGEgc2hhcmVkIHJlZmVyZW5jZSB0aGUgZW5jb2RlcidzIGludGVybmFsIGJ1ZmZlci5cbiAgICAgKi9cbiAgICBlbmNvZGVTaGFyZWRSZWYob2JqZWN0KSB7XG4gICAgICAgIGlmICh0aGlzLmVudGVyZWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGluc3RhbmNlID0gdGhpcy5jbG9uZSgpO1xuICAgICAgICAgICAgcmV0dXJuIGluc3RhbmNlLmVuY29kZVNoYXJlZFJlZihvYmplY3QpO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB0aGlzLmVudGVyZWQgPSB0cnVlO1xuICAgICAgICAgICAgdGhpcy5yZWluaXRpYWxpemVTdGF0ZSgpO1xuICAgICAgICAgICAgdGhpcy5kb0VuY29kZShvYmplY3QsIDEpO1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuYnl0ZXMuc3ViYXJyYXkoMCwgdGhpcy5wb3MpO1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgdGhpcy5lbnRlcmVkID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQHJldHVybnMgRW5jb2RlcyB0aGUgb2JqZWN0IGFuZCByZXR1cm5zIGEgY29weSBvZiB0aGUgZW5jb2RlcidzIGludGVybmFsIGJ1ZmZlci5cbiAgICAgKi9cbiAgICBlbmNvZGUob2JqZWN0KSB7XG4gICAgICAgIGlmICh0aGlzLmVudGVyZWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGluc3RhbmNlID0gdGhpcy5jbG9uZSgpO1xuICAgICAgICAgICAgcmV0dXJuIGluc3RhbmNlLmVuY29kZShvYmplY3QpO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB0aGlzLmVudGVyZWQgPSB0cnVlO1xuICAgICAgICAgICAgdGhpcy5yZWluaXRpYWxpemVTdGF0ZSgpO1xuICAgICAgICAgICAgdGhpcy5kb0VuY29kZShvYmplY3QsIDEpO1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuYnl0ZXMuc2xpY2UoMCwgdGhpcy5wb3MpO1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgdGhpcy5lbnRlcmVkID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZG9FbmNvZGUob2JqZWN0LCBkZXB0aCkge1xuICAgICAgICBpZiAoZGVwdGggPiB0aGlzLm1heERlcHRoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRvbyBkZWVwIG9iamVjdHMgaW4gZGVwdGggJHtkZXB0aH1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAob2JqZWN0ID09IG51bGwpIHtcbiAgICAgICAgICAgIHRoaXMuZW5jb2RlTmlsKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIG9iamVjdCA9PT0gXCJib29sZWFuXCIpIHtcbiAgICAgICAgICAgIHRoaXMuZW5jb2RlQm9vbGVhbihvYmplY3QpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBvYmplY3QgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5mb3JjZUludGVnZXJUb0Zsb2F0KSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbmNvZGVOdW1iZXIob2JqZWN0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuZW5jb2RlTnVtYmVyQXNGbG9hdChvYmplY3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBvYmplY3QgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgIHRoaXMuZW5jb2RlU3RyaW5nKG9iamVjdCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy51c2VCaWdJbnQ2NCAmJiB0eXBlb2Ygb2JqZWN0ID09PSBcImJpZ2ludFwiKSB7XG4gICAgICAgICAgICB0aGlzLmVuY29kZUJpZ0ludDY0KG9iamVjdCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmVuY29kZU9iamVjdChvYmplY3QsIGRlcHRoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbnN1cmVCdWZmZXJTaXplVG9Xcml0ZShzaXplVG9Xcml0ZSkge1xuICAgICAgICBjb25zdCByZXF1aXJlZFNpemUgPSB0aGlzLnBvcyArIHNpemVUb1dyaXRlO1xuICAgICAgICBpZiAodGhpcy52aWV3LmJ5dGVMZW5ndGggPCByZXF1aXJlZFNpemUpIHtcbiAgICAgICAgICAgIHRoaXMucmVzaXplQnVmZmVyKHJlcXVpcmVkU2l6ZSAqIDIpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJlc2l6ZUJ1ZmZlcihuZXdTaXplKSB7XG4gICAgICAgIGNvbnN0IG5ld0J1ZmZlciA9IG5ldyBBcnJheUJ1ZmZlcihuZXdTaXplKTtcbiAgICAgICAgY29uc3QgbmV3Qnl0ZXMgPSBuZXcgVWludDhBcnJheShuZXdCdWZmZXIpO1xuICAgICAgICBjb25zdCBuZXdWaWV3ID0gbmV3IERhdGFWaWV3KG5ld0J1ZmZlcik7XG4gICAgICAgIG5ld0J5dGVzLnNldCh0aGlzLmJ5dGVzKTtcbiAgICAgICAgdGhpcy52aWV3ID0gbmV3VmlldztcbiAgICAgICAgdGhpcy5ieXRlcyA9IG5ld0J5dGVzO1xuICAgIH1cbiAgICBlbmNvZGVOaWwoKSB7XG4gICAgICAgIHRoaXMud3JpdGVVOCgweGMwKTtcbiAgICB9XG4gICAgZW5jb2RlQm9vbGVhbihvYmplY3QpIHtcbiAgICAgICAgaWYgKG9iamVjdCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGMyKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGMzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbmNvZGVOdW1iZXIob2JqZWN0KSB7XG4gICAgICAgIGlmICghdGhpcy5mb3JjZUludGVnZXJUb0Zsb2F0ICYmIE51bWJlci5pc1NhZmVJbnRlZ2VyKG9iamVjdCkpIHtcbiAgICAgICAgICAgIGlmIChvYmplY3QgPj0gMCkge1xuICAgICAgICAgICAgICAgIGlmIChvYmplY3QgPCAweDgwKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIHBvc2l0aXZlIGZpeGludFxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVTgob2JqZWN0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAob2JqZWN0IDwgMHgxMDApIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gdWludCA4XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGNjKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy53cml0ZVU4KG9iamVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKG9iamVjdCA8IDB4MTAwMDApIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gdWludCAxNlxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjZCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVMTYob2JqZWN0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAob2JqZWN0IDwgMHgxMDAwMDAwMDApIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gdWludCAzMlxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjZSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVMzIob2JqZWN0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoIXRoaXMudXNlQmlnSW50NjQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gdWludCA2NFxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjZik7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVNjQob2JqZWN0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW5jb2RlTnVtYmVyQXNGbG9hdChvYmplY3QpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGlmIChvYmplY3QgPj0gLTB4MjApIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gbmVnYXRpdmUgZml4aW50XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGUwIHwgKG9iamVjdCArIDB4MjApKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAob2JqZWN0ID49IC0weDgwKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGludCA4XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGQwKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy53cml0ZUk4KG9iamVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKG9iamVjdCA+PSAtMHg4MDAwKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGludCAxNlxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhkMSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVJMTYob2JqZWN0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAob2JqZWN0ID49IC0weDgwMDAwMDAwKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGludCAzMlxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhkMik7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVJMzIob2JqZWN0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoIXRoaXMudXNlQmlnSW50NjQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gaW50IDY0XG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGQzKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy53cml0ZUk2NChvYmplY3QpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbmNvZGVOdW1iZXJBc0Zsb2F0KG9iamVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5lbmNvZGVOdW1iZXJBc0Zsb2F0KG9iamVjdCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZW5jb2RlTnVtYmVyQXNGbG9hdChvYmplY3QpIHtcbiAgICAgICAgaWYgKHRoaXMuZm9yY2VGbG9hdDMyKSB7XG4gICAgICAgICAgICAvLyBmbG9hdCAzMlxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4Y2EpO1xuICAgICAgICAgICAgdGhpcy53cml0ZUYzMihvYmplY3QpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgLy8gZmxvYXQgNjRcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGNiKTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVGNjQob2JqZWN0KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbmNvZGVCaWdJbnQ2NChvYmplY3QpIHtcbiAgICAgICAgaWYgKG9iamVjdCA+PSBCaWdJbnQoMCkpIHtcbiAgICAgICAgICAgIC8vIHVpbnQgNjRcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGNmKTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVCaWdVaW50NjQob2JqZWN0KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIC8vIGludCA2NFxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4ZDMpO1xuICAgICAgICAgICAgdGhpcy53cml0ZUJpZ0ludDY0KG9iamVjdCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgd3JpdGVTdHJpbmdIZWFkZXIoYnl0ZUxlbmd0aCkge1xuICAgICAgICBpZiAoYnl0ZUxlbmd0aCA8IDMyKSB7XG4gICAgICAgICAgICAvLyBmaXhzdHJcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGEwICsgYnl0ZUxlbmd0aCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYnl0ZUxlbmd0aCA8IDB4MTAwKSB7XG4gICAgICAgICAgICAvLyBzdHIgOFxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4ZDkpO1xuICAgICAgICAgICAgdGhpcy53cml0ZVU4KGJ5dGVMZW5ndGgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGJ5dGVMZW5ndGggPCAweDEwMDAwKSB7XG4gICAgICAgICAgICAvLyBzdHIgMTZcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGRhKTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVMTYoYnl0ZUxlbmd0aCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYnl0ZUxlbmd0aCA8IDB4MTAwMDAwMDAwKSB7XG4gICAgICAgICAgICAvLyBzdHIgMzJcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGRiKTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVMzIoYnl0ZUxlbmd0aCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRvbyBsb25nIHN0cmluZzogJHtieXRlTGVuZ3RofSBieXRlcyBpbiBVVEYtOGApO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVuY29kZVN0cmluZyhvYmplY3QpIHtcbiAgICAgICAgY29uc3QgbWF4SGVhZGVyU2l6ZSA9IDEgKyA0O1xuICAgICAgICBjb25zdCBieXRlTGVuZ3RoID0gdXRmOENvdW50KG9iamVjdCk7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUobWF4SGVhZGVyU2l6ZSArIGJ5dGVMZW5ndGgpO1xuICAgICAgICB0aGlzLndyaXRlU3RyaW5nSGVhZGVyKGJ5dGVMZW5ndGgpO1xuICAgICAgICB1dGY4RW5jb2RlKG9iamVjdCwgdGhpcy5ieXRlcywgdGhpcy5wb3MpO1xuICAgICAgICB0aGlzLnBvcyArPSBieXRlTGVuZ3RoO1xuICAgIH1cbiAgICBlbmNvZGVPYmplY3Qob2JqZWN0LCBkZXB0aCkge1xuICAgICAgICAvLyB0cnkgdG8gZW5jb2RlIG9iamVjdHMgd2l0aCBjdXN0b20gY29kZWMgZmlyc3Qgb2Ygbm9uLXByaW1pdGl2ZXNcbiAgICAgICAgY29uc3QgZXh0ID0gdGhpcy5leHRlbnNpb25Db2RlYy50cnlUb0VuY29kZShvYmplY3QsIHRoaXMuY29udGV4dCk7XG4gICAgICAgIGlmIChleHQgIT0gbnVsbCkge1xuICAgICAgICAgICAgdGhpcy5lbmNvZGVFeHRlbnNpb24oZXh0KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KG9iamVjdCkpIHtcbiAgICAgICAgICAgIHRoaXMuZW5jb2RlQXJyYXkob2JqZWN0LCBkZXB0aCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoQXJyYXlCdWZmZXIuaXNWaWV3KG9iamVjdCkpIHtcbiAgICAgICAgICAgIHRoaXMuZW5jb2RlQmluYXJ5KG9iamVjdCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIG9iamVjdCA9PT0gXCJvYmplY3RcIikge1xuICAgICAgICAgICAgdGhpcy5lbmNvZGVNYXAob2JqZWN0LCBkZXB0aCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBzeW1ib2wsIGZ1bmN0aW9uIGFuZCBvdGhlciBzcGVjaWFsIG9iamVjdCBjb21lIGhlcmUgdW5sZXNzIGV4dGVuc2lvbkNvZGVjIGhhbmRsZXMgdGhlbS5cbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5yZWNvZ25pemVkIG9iamVjdDogJHtPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmFwcGx5KG9iamVjdCl9YCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZW5jb2RlQmluYXJ5KG9iamVjdCkge1xuICAgICAgICBjb25zdCBzaXplID0gb2JqZWN0LmJ5dGVMZW5ndGg7XG4gICAgICAgIGlmIChzaXplIDwgMHgxMDApIHtcbiAgICAgICAgICAgIC8vIGJpbiA4XG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjNCk7XG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoc2l6ZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc2l6ZSA8IDB4MTAwMDApIHtcbiAgICAgICAgICAgIC8vIGJpbiAxNlxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4YzUpO1xuICAgICAgICAgICAgdGhpcy53cml0ZVUxNihzaXplKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChzaXplIDwgMHgxMDAwMDAwMDApIHtcbiAgICAgICAgICAgIC8vIGJpbiAzMlxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4YzYpO1xuICAgICAgICAgICAgdGhpcy53cml0ZVUzMihzaXplKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVG9vIGxhcmdlIGJpbmFyeTogJHtzaXplfWApO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGJ5dGVzID0gZW5zdXJlVWludDhBcnJheShvYmplY3QpO1xuICAgICAgICB0aGlzLndyaXRlVThhKGJ5dGVzKTtcbiAgICB9XG4gICAgZW5jb2RlQXJyYXkob2JqZWN0LCBkZXB0aCkge1xuICAgICAgICBjb25zdCBzaXplID0gb2JqZWN0Lmxlbmd0aDtcbiAgICAgICAgaWYgKHNpemUgPCAxNikge1xuICAgICAgICAgICAgLy8gZml4YXJyYXlcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweDkwICsgc2l6ZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc2l6ZSA8IDB4MTAwMDApIHtcbiAgICAgICAgICAgIC8vIGFycmF5IDE2XG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhkYyk7XG4gICAgICAgICAgICB0aGlzLndyaXRlVTE2KHNpemUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHNpemUgPCAweDEwMDAwMDAwMCkge1xuICAgICAgICAgICAgLy8gYXJyYXkgMzJcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGRkKTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVMzIoc2l6ZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRvbyBsYXJnZSBhcnJheTogJHtzaXplfWApO1xuICAgICAgICB9XG4gICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBvYmplY3QpIHtcbiAgICAgICAgICAgIHRoaXMuZG9FbmNvZGUoaXRlbSwgZGVwdGggKyAxKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjb3VudFdpdGhvdXRVbmRlZmluZWQob2JqZWN0LCBrZXlzKSB7XG4gICAgICAgIGxldCBjb3VudCA9IDA7XG4gICAgICAgIGZvciAoY29uc3Qga2V5IG9mIGtleXMpIHtcbiAgICAgICAgICAgIGlmIChvYmplY3Rba2V5XSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgY291bnQrKztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY291bnQ7XG4gICAgfVxuICAgIGVuY29kZU1hcChvYmplY3QsIGRlcHRoKSB7XG4gICAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhvYmplY3QpO1xuICAgICAgICBpZiAodGhpcy5zb3J0S2V5cykge1xuICAgICAgICAgICAga2V5cy5zb3J0KCk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc2l6ZSA9IHRoaXMuaWdub3JlVW5kZWZpbmVkID8gdGhpcy5jb3VudFdpdGhvdXRVbmRlZmluZWQob2JqZWN0LCBrZXlzKSA6IGtleXMubGVuZ3RoO1xuICAgICAgICBpZiAoc2l6ZSA8IDE2KSB7XG4gICAgICAgICAgICAvLyBmaXhtYXBcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweDgwICsgc2l6ZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc2l6ZSA8IDB4MTAwMDApIHtcbiAgICAgICAgICAgIC8vIG1hcCAxNlxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4ZGUpO1xuICAgICAgICAgICAgdGhpcy53cml0ZVUxNihzaXplKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChzaXplIDwgMHgxMDAwMDAwMDApIHtcbiAgICAgICAgICAgIC8vIG1hcCAzMlxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4ZGYpO1xuICAgICAgICAgICAgdGhpcy53cml0ZVUzMihzaXplKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVG9vIGxhcmdlIG1hcCBvYmplY3Q6ICR7c2l6ZX1gKTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IGtleSBvZiBrZXlzKSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZSA9IG9iamVjdFtrZXldO1xuICAgICAgICAgICAgaWYgKCEodGhpcy5pZ25vcmVVbmRlZmluZWQgJiYgdmFsdWUgPT09IHVuZGVmaW5lZCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVuY29kZVN0cmluZyhrZXkpO1xuICAgICAgICAgICAgICAgIHRoaXMuZG9FbmNvZGUodmFsdWUsIGRlcHRoICsgMSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgZW5jb2RlRXh0ZW5zaW9uKGV4dCkge1xuICAgICAgICBpZiAodHlwZW9mIGV4dC5kYXRhID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBleHQuZGF0YSh0aGlzLnBvcyArIDYpO1xuICAgICAgICAgICAgY29uc3Qgc2l6ZSA9IGRhdGEubGVuZ3RoO1xuICAgICAgICAgICAgaWYgKHNpemUgPj0gMHgxMDAwMDAwMDApIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRvbyBsYXJnZSBleHRlbnNpb24gb2JqZWN0OiAke3NpemV9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjOSk7XG4gICAgICAgICAgICB0aGlzLndyaXRlVTMyKHNpemUpO1xuICAgICAgICAgICAgdGhpcy53cml0ZUk4KGV4dC50eXBlKTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOGEoZGF0YSk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc2l6ZSA9IGV4dC5kYXRhLmxlbmd0aDtcbiAgICAgICAgaWYgKHNpemUgPT09IDEpIHtcbiAgICAgICAgICAgIC8vIGZpeGV4dCAxXG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhkNCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc2l6ZSA9PT0gMikge1xuICAgICAgICAgICAgLy8gZml4ZXh0IDJcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGQ1KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChzaXplID09PSA0KSB7XG4gICAgICAgICAgICAvLyBmaXhleHQgNFxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4ZDYpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHNpemUgPT09IDgpIHtcbiAgICAgICAgICAgIC8vIGZpeGV4dCA4XG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhkNyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc2l6ZSA9PT0gMTYpIHtcbiAgICAgICAgICAgIC8vIGZpeGV4dCAxNlxuICAgICAgICAgICAgdGhpcy53cml0ZVU4KDB4ZDgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHNpemUgPCAweDEwMCkge1xuICAgICAgICAgICAgLy8gZXh0IDhcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOCgweGM3KTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVVOChzaXplKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChzaXplIDwgMHgxMDAwMCkge1xuICAgICAgICAgICAgLy8gZXh0IDE2XG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjOCk7XG4gICAgICAgICAgICB0aGlzLndyaXRlVTE2KHNpemUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHNpemUgPCAweDEwMDAwMDAwMCkge1xuICAgICAgICAgICAgLy8gZXh0IDMyXG4gICAgICAgICAgICB0aGlzLndyaXRlVTgoMHhjOSk7XG4gICAgICAgICAgICB0aGlzLndyaXRlVTMyKHNpemUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBUb28gbGFyZ2UgZXh0ZW5zaW9uIG9iamVjdDogJHtzaXplfWApO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMud3JpdGVJOChleHQudHlwZSk7XG4gICAgICAgIHRoaXMud3JpdGVVOGEoZXh0LmRhdGEpO1xuICAgIH1cbiAgICB3cml0ZVU4KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoMSk7XG4gICAgICAgIHRoaXMudmlldy5zZXRVaW50OCh0aGlzLnBvcywgdmFsdWUpO1xuICAgICAgICB0aGlzLnBvcysrO1xuICAgIH1cbiAgICB3cml0ZVU4YSh2YWx1ZXMpIHtcbiAgICAgICAgY29uc3Qgc2l6ZSA9IHZhbHVlcy5sZW5ndGg7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoc2l6ZSk7XG4gICAgICAgIHRoaXMuYnl0ZXMuc2V0KHZhbHVlcywgdGhpcy5wb3MpO1xuICAgICAgICB0aGlzLnBvcyArPSBzaXplO1xuICAgIH1cbiAgICB3cml0ZUk4KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoMSk7XG4gICAgICAgIHRoaXMudmlldy5zZXRJbnQ4KHRoaXMucG9zLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMucG9zKys7XG4gICAgfVxuICAgIHdyaXRlVTE2KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoMik7XG4gICAgICAgIHRoaXMudmlldy5zZXRVaW50MTYodGhpcy5wb3MsIHZhbHVlKTtcbiAgICAgICAgdGhpcy5wb3MgKz0gMjtcbiAgICB9XG4gICAgd3JpdGVJMTYodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVCdWZmZXJTaXplVG9Xcml0ZSgyKTtcbiAgICAgICAgdGhpcy52aWV3LnNldEludDE2KHRoaXMucG9zLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMucG9zICs9IDI7XG4gICAgfVxuICAgIHdyaXRlVTMyKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoNCk7XG4gICAgICAgIHRoaXMudmlldy5zZXRVaW50MzIodGhpcy5wb3MsIHZhbHVlKTtcbiAgICAgICAgdGhpcy5wb3MgKz0gNDtcbiAgICB9XG4gICAgd3JpdGVJMzIodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVCdWZmZXJTaXplVG9Xcml0ZSg0KTtcbiAgICAgICAgdGhpcy52aWV3LnNldEludDMyKHRoaXMucG9zLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMucG9zICs9IDQ7XG4gICAgfVxuICAgIHdyaXRlRjMyKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoNCk7XG4gICAgICAgIHRoaXMudmlldy5zZXRGbG9hdDMyKHRoaXMucG9zLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMucG9zICs9IDQ7XG4gICAgfVxuICAgIHdyaXRlRjY0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoOCk7XG4gICAgICAgIHRoaXMudmlldy5zZXRGbG9hdDY0KHRoaXMucG9zLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMucG9zICs9IDg7XG4gICAgfVxuICAgIHdyaXRlVTY0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoOCk7XG4gICAgICAgIHNldFVpbnQ2NCh0aGlzLnZpZXcsIHRoaXMucG9zLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMucG9zICs9IDg7XG4gICAgfVxuICAgIHdyaXRlSTY0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoOCk7XG4gICAgICAgIHNldEludDY0KHRoaXMudmlldywgdGhpcy5wb3MsIHZhbHVlKTtcbiAgICAgICAgdGhpcy5wb3MgKz0gODtcbiAgICB9XG4gICAgd3JpdGVCaWdVaW50NjQodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVCdWZmZXJTaXplVG9Xcml0ZSg4KTtcbiAgICAgICAgdGhpcy52aWV3LnNldEJpZ1VpbnQ2NCh0aGlzLnBvcywgdmFsdWUpO1xuICAgICAgICB0aGlzLnBvcyArPSA4O1xuICAgIH1cbiAgICB3cml0ZUJpZ0ludDY0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQnVmZmVyU2l6ZVRvV3JpdGUoOCk7XG4gICAgICAgIHRoaXMudmlldy5zZXRCaWdJbnQ2NCh0aGlzLnBvcywgdmFsdWUpO1xuICAgICAgICB0aGlzLnBvcyArPSA4O1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUVuY29kZXIubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtData: () => (/* binding */ ExtData)\n/* harmony export */ });\n/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nclass ExtData {\n    constructor(type, data) {\n        this.type = type;\n        this.data = data;\n    }\n}\n//# sourceMappingURL=ExtData.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9FeHREYXRhLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9AbXNncGFjay9tc2dwYWNrL2Rpc3QuZXNtL0V4dERhdGEubWpzPzBlYTYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHREYXRhIGlzIHVzZWQgdG8gaGFuZGxlIEV4dGVuc2lvbiBUeXBlcyB0aGF0IGFyZSBub3QgcmVnaXN0ZXJlZCB0byBFeHRlbnNpb25Db2RlYy5cbiAqL1xuZXhwb3J0IGNsYXNzIEV4dERhdGEge1xuICAgIGNvbnN0cnVjdG9yKHR5cGUsIGRhdGEpIHtcbiAgICAgICAgdGhpcy50eXBlID0gdHlwZTtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1FeHREYXRhLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtensionCodec: () => (/* binding */ ExtensionCodec)\n/* harmony export */ });\n/* harmony import */ var _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ExtData.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs\");\n/* harmony import */ var _timestamp_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timestamp.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs\");\n// ExtensionCodec to handle MessagePack extensions\n\n\nclass ExtensionCodec {\n    constructor() {\n        // built-in extensions\n        this.builtInEncoders = [];\n        this.builtInDecoders = [];\n        // custom extensions\n        this.encoders = [];\n        this.decoders = [];\n        this.register(_timestamp_mjs__WEBPACK_IMPORTED_MODULE_0__.timestampExtension);\n    }\n    register({ type, encode, decode, }) {\n        if (type >= 0) {\n            // custom extensions\n            this.encoders[type] = encode;\n            this.decoders[type] = decode;\n        }\n        else {\n            // built-in extensions\n            const index = -1 - type;\n            this.builtInEncoders[index] = encode;\n            this.builtInDecoders[index] = decode;\n        }\n    }\n    tryToEncode(object, context) {\n        // built-in extensions\n        for (let i = 0; i < this.builtInEncoders.length; i++) {\n            const encodeExt = this.builtInEncoders[i];\n            if (encodeExt != null) {\n                const data = encodeExt(object, context);\n                if (data != null) {\n                    const type = -1 - i;\n                    return new _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData(type, data);\n                }\n            }\n        }\n        // custom extensions\n        for (let i = 0; i < this.encoders.length; i++) {\n            const encodeExt = this.encoders[i];\n            if (encodeExt != null) {\n                const data = encodeExt(object, context);\n                if (data != null) {\n                    const type = i;\n                    return new _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData(type, data);\n                }\n            }\n        }\n        if (object instanceof _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData) {\n            // to keep ExtData as is\n            return object;\n        }\n        return null;\n    }\n    decode(data, type, context) {\n        const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n        if (decodeExt) {\n            return decodeExt(data, type, context);\n        }\n        else {\n            // decode() does not fail, returns ExtData instead.\n            return new _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData(type, data);\n        }\n    }\n}\nExtensionCodec.defaultCodec = new ExtensionCodec();\n//# sourceMappingURL=ExtensionCodec.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/decode.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/decode.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeMulti: () => (/* binding */ decodeMulti)\n/* harmony export */ });\n/* harmony import */ var _Decoder_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Decoder.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs\");\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync}, {@link decodeMultiStream}, or {@link decodeArrayStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nfunction decode(buffer, options) {\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_0__.Decoder(options);\n    return decoder.decode(buffer);\n}\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nfunction decodeMulti(buffer, options) {\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_0__.Decoder(options);\n    return decoder.decodeMulti(buffer);\n}\n//# sourceMappingURL=decode.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/decode.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/encode.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/encode.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _Encoder_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Encoder.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs\");\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nfunction encode(value, options) {\n    const encoder = new _Encoder_mjs__WEBPACK_IMPORTED_MODULE_0__.Encoder(options);\n    return encoder.encodeSharedRef(value);\n}\n//# sourceMappingURL=encode.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9lbmNvZGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1Asd0JBQXdCLGlEQUFPO0FBQy9CO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vZW5jb2RlLm1qcz9mYzk4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEVuY29kZXIgfSBmcm9tIFwiLi9FbmNvZGVyLm1qc1wiO1xuLyoqXG4gKiBJdCBlbmNvZGVzIGB2YWx1ZWAgaW4gdGhlIE1lc3NhZ2VQYWNrIGZvcm1hdCBhbmRcbiAqIHJldHVybnMgYSBieXRlIGJ1ZmZlci5cbiAqXG4gKiBUaGUgcmV0dXJuZWQgYnVmZmVyIGlzIGEgc2xpY2Ugb2YgYSBsYXJnZXIgYEFycmF5QnVmZmVyYCwgc28geW91IGhhdmUgdG8gdXNlIGl0cyBgI2J5dGVPZmZzZXRgIGFuZCBgI2J5dGVMZW5ndGhgIGluIG9yZGVyIHRvIGNvbnZlcnQgaXQgdG8gYW5vdGhlciB0eXBlZCBhcnJheXMgaW5jbHVkaW5nIE5vZGVKUyBgQnVmZmVyYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuY29kZSh2YWx1ZSwgb3B0aW9ucykge1xuICAgIGNvbnN0IGVuY29kZXIgPSBuZXcgRW5jb2RlcihvcHRpb25zKTtcbiAgICByZXR1cm4gZW5jb2Rlci5lbmNvZGVTaGFyZWRSZWYodmFsdWUpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW5jb2RlLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/encode.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EXT_TIMESTAMP: () => (/* binding */ EXT_TIMESTAMP),\n/* harmony export */   decodeTimestampExtension: () => (/* binding */ decodeTimestampExtension),\n/* harmony export */   decodeTimestampToTimeSpec: () => (/* binding */ decodeTimestampToTimeSpec),\n/* harmony export */   encodeDateToTimeSpec: () => (/* binding */ encodeDateToTimeSpec),\n/* harmony export */   encodeTimeSpecToTimestamp: () => (/* binding */ encodeTimeSpecToTimestamp),\n/* harmony export */   encodeTimestampExtension: () => (/* binding */ encodeTimestampExtension),\n/* harmony export */   timestampExtension: () => (/* binding */ timestampExtension)\n/* harmony export */ });\n/* harmony import */ var _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DecodeError.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\");\n/* harmony import */ var _utils_int_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/int.mjs */ \"(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\");\n// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\n\n\nconst EXT_TIMESTAMP = -1;\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\nfunction encodeTimeSpecToTimestamp({ sec, nsec }) {\n    if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n        // Here sec >= 0 && nsec >= 0\n        if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n            // timestamp 32 = { sec32 (unsigned) }\n            const rv = new Uint8Array(4);\n            const view = new DataView(rv.buffer);\n            view.setUint32(0, sec);\n            return rv;\n        }\n        else {\n            // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n            const secHigh = sec / 0x100000000;\n            const secLow = sec & 0xffffffff;\n            const rv = new Uint8Array(8);\n            const view = new DataView(rv.buffer);\n            // nsec30 | secHigh2\n            view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n            // secLow32\n            view.setUint32(4, secLow);\n            return rv;\n        }\n    }\n    else {\n        // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n        const rv = new Uint8Array(12);\n        const view = new DataView(rv.buffer);\n        view.setUint32(0, nsec);\n        (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_0__.setInt64)(view, 4, sec);\n        return rv;\n    }\n}\nfunction encodeDateToTimeSpec(date) {\n    const msec = date.getTime();\n    const sec = Math.floor(msec / 1e3);\n    const nsec = (msec - sec * 1e3) * 1e6;\n    // Normalizes { sec, nsec } to ensure nsec is unsigned.\n    const nsecInSec = Math.floor(nsec / 1e9);\n    return {\n        sec: sec + nsecInSec,\n        nsec: nsec - nsecInSec * 1e9,\n    };\n}\nfunction encodeTimestampExtension(object) {\n    if (object instanceof Date) {\n        const timeSpec = encodeDateToTimeSpec(object);\n        return encodeTimeSpecToTimestamp(timeSpec);\n    }\n    else {\n        return null;\n    }\n}\nfunction decodeTimestampToTimeSpec(data) {\n    const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n    // data may be 32, 64, or 96 bits\n    switch (data.byteLength) {\n        case 4: {\n            // timestamp 32 = { sec32 }\n            const sec = view.getUint32(0);\n            const nsec = 0;\n            return { sec, nsec };\n        }\n        case 8: {\n            // timestamp 64 = { nsec30, sec34 }\n            const nsec30AndSecHigh2 = view.getUint32(0);\n            const secLow32 = view.getUint32(4);\n            const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n            const nsec = nsec30AndSecHigh2 >>> 2;\n            return { sec, nsec };\n        }\n        case 12: {\n            // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n            const sec = (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_0__.getInt64)(view, 4);\n            const nsec = view.getUint32(0);\n            return { sec, nsec };\n        }\n        default:\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_1__.DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n    }\n}\nfunction decodeTimestampExtension(data) {\n    const timeSpec = decodeTimestampToTimeSpec(data);\n    return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\nconst timestampExtension = {\n    type: EXT_TIMESTAMP,\n    encode: encodeTimestampExtension,\n    decode: decodeTimestampExtension,\n};\n//# sourceMappingURL=timestamp.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UINT32_MAX: () => (/* binding */ UINT32_MAX),\n/* harmony export */   getInt64: () => (/* binding */ getInt64),\n/* harmony export */   getUint64: () => (/* binding */ getUint64),\n/* harmony export */   setInt64: () => (/* binding */ setInt64),\n/* harmony export */   setUint64: () => (/* binding */ setUint64)\n/* harmony export */ });\n// Integer Utility\nconst UINT32_MAX = 4294967295;\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\nfunction setUint64(view, offset, value) {\n    const high = value / 4294967296;\n    const low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nfunction setInt64(view, offset, value) {\n    const high = Math.floor(value / 4294967296);\n    const low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nfunction getInt64(view, offset) {\n    const high = view.getInt32(offset);\n    const low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\nfunction getUint64(view, offset) {\n    const high = view.getUint32(offset);\n    const low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\n//# sourceMappingURL=int.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9pbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vdXRpbHMvaW50Lm1qcz9jN2M3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEludGVnZXIgVXRpbGl0eVxuZXhwb3J0IGNvbnN0IFVJTlQzMl9NQVggPSA0Mjk0OTY3Mjk1O1xuLy8gRGF0YVZpZXcgZXh0ZW5zaW9uIHRvIGhhbmRsZSBpbnQ2NCAvIHVpbnQ2NCxcbi8vIHdoZXJlIHRoZSBhY3R1YWwgcmFuZ2UgaXMgNTMtYml0cyBpbnRlZ2VyIChhLmsuYS4gc2FmZSBpbnRlZ2VyKVxuZXhwb3J0IGZ1bmN0aW9uIHNldFVpbnQ2NCh2aWV3LCBvZmZzZXQsIHZhbHVlKSB7XG4gICAgY29uc3QgaGlnaCA9IHZhbHVlIC8gNDI5NDk2NzI5NjtcbiAgICBjb25zdCBsb3cgPSB2YWx1ZTsgLy8gaGlnaCBiaXRzIGFyZSB0cnVuY2F0ZWQgYnkgRGF0YVZpZXdcbiAgICB2aWV3LnNldFVpbnQzMihvZmZzZXQsIGhpZ2gpO1xuICAgIHZpZXcuc2V0VWludDMyKG9mZnNldCArIDQsIGxvdyk7XG59XG5leHBvcnQgZnVuY3Rpb24gc2V0SW50NjQodmlldywgb2Zmc2V0LCB2YWx1ZSkge1xuICAgIGNvbnN0IGhpZ2ggPSBNYXRoLmZsb29yKHZhbHVlIC8gNDI5NDk2NzI5Nik7XG4gICAgY29uc3QgbG93ID0gdmFsdWU7IC8vIGhpZ2ggYml0cyBhcmUgdHJ1bmNhdGVkIGJ5IERhdGFWaWV3XG4gICAgdmlldy5zZXRVaW50MzIob2Zmc2V0LCBoaWdoKTtcbiAgICB2aWV3LnNldFVpbnQzMihvZmZzZXQgKyA0LCBsb3cpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldEludDY0KHZpZXcsIG9mZnNldCkge1xuICAgIGNvbnN0IGhpZ2ggPSB2aWV3LmdldEludDMyKG9mZnNldCk7XG4gICAgY29uc3QgbG93ID0gdmlldy5nZXRVaW50MzIob2Zmc2V0ICsgNCk7XG4gICAgcmV0dXJuIGhpZ2ggKiA0Mjk0OTY3Mjk2ICsgbG93O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldFVpbnQ2NCh2aWV3LCBvZmZzZXQpIHtcbiAgICBjb25zdCBoaWdoID0gdmlldy5nZXRVaW50MzIob2Zmc2V0KTtcbiAgICBjb25zdCBsb3cgPSB2aWV3LmdldFVpbnQzMihvZmZzZXQgKyA0KTtcbiAgICByZXR1cm4gaGlnaCAqIDQyOTQ5NjcyOTYgKyBsb3c7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnQubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prettyByte: () => (/* binding */ prettyByte)\n/* harmony export */ });\nfunction prettyByte(byte) {\n    return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n//# sourceMappingURL=prettyByte.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9wcmV0dHlCeXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxjQUFjLG9CQUFvQixJQUFJLDZDQUE2QztBQUNuRjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9wcmV0dHlCeXRlLm1qcz81NGY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBwcmV0dHlCeXRlKGJ5dGUpIHtcbiAgICByZXR1cm4gYCR7Ynl0ZSA8IDAgPyBcIi1cIiA6IFwiXCJ9MHgke01hdGguYWJzKGJ5dGUpLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCBcIjBcIil9YDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByZXR0eUJ5dGUubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureUint8Array: () => (/* binding */ ensureUint8Array)\n/* harmony export */ });\nfunction isArrayBufferLike(buffer) {\n    return (buffer instanceof ArrayBuffer || (typeof SharedArrayBuffer !== \"undefined\" && buffer instanceof SharedArrayBuffer));\n}\nfunction ensureUint8Array(buffer) {\n    if (buffer instanceof Uint8Array) {\n        return buffer;\n    }\n    else if (ArrayBuffer.isView(buffer)) {\n        return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    }\n    else if (isArrayBufferLike(buffer)) {\n        return new Uint8Array(buffer);\n    }\n    else {\n        // ArrayLike<number>\n        return Uint8Array.from(buffer);\n    }\n}\n//# sourceMappingURL=typedArrays.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy90eXBlZEFycmF5cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy90eXBlZEFycmF5cy5tanM/MTkyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc0FycmF5QnVmZmVyTGlrZShidWZmZXIpIHtcbiAgICByZXR1cm4gKGJ1ZmZlciBpbnN0YW5jZW9mIEFycmF5QnVmZmVyIHx8ICh0eXBlb2YgU2hhcmVkQXJyYXlCdWZmZXIgIT09IFwidW5kZWZpbmVkXCIgJiYgYnVmZmVyIGluc3RhbmNlb2YgU2hhcmVkQXJyYXlCdWZmZXIpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBlbnN1cmVVaW50OEFycmF5KGJ1ZmZlcikge1xuICAgIGlmIChidWZmZXIgaW5zdGFuY2VvZiBVaW50OEFycmF5KSB7XG4gICAgICAgIHJldHVybiBidWZmZXI7XG4gICAgfVxuICAgIGVsc2UgaWYgKEFycmF5QnVmZmVyLmlzVmlldyhidWZmZXIpKSB7XG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShidWZmZXIuYnVmZmVyLCBidWZmZXIuYnl0ZU9mZnNldCwgYnVmZmVyLmJ5dGVMZW5ndGgpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc0FycmF5QnVmZmVyTGlrZShidWZmZXIpKSB7XG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShidWZmZXIpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgLy8gQXJyYXlMaWtlPG51bWJlcj5cbiAgICAgICAgcmV0dXJuIFVpbnQ4QXJyYXkuZnJvbShidWZmZXIpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVkQXJyYXlzLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   utf8Count: () => (/* binding */ utf8Count),\n/* harmony export */   utf8Decode: () => (/* binding */ utf8Decode),\n/* harmony export */   utf8DecodeJs: () => (/* binding */ utf8DecodeJs),\n/* harmony export */   utf8DecodeTD: () => (/* binding */ utf8DecodeTD),\n/* harmony export */   utf8Encode: () => (/* binding */ utf8Encode),\n/* harmony export */   utf8EncodeJs: () => (/* binding */ utf8EncodeJs),\n/* harmony export */   utf8EncodeTE: () => (/* binding */ utf8EncodeTE)\n/* harmony export */ });\nfunction utf8Count(str) {\n    const strLength = str.length;\n    let byteLength = 0;\n    let pos = 0;\n    while (pos < strLength) {\n        let value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            byteLength++;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            byteLength += 2;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    const extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                byteLength += 3;\n            }\n            else {\n                // 4-byte\n                byteLength += 4;\n            }\n        }\n    }\n    return byteLength;\n}\nfunction utf8EncodeJs(str, output, outputOffset) {\n    const strLength = str.length;\n    let offset = outputOffset;\n    let pos = 0;\n    while (pos < strLength) {\n        let value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            output[offset++] = value;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    const extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n            else {\n                // 4-byte\n                output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n                output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n        }\n        output[offset++] = (value & 0x3f) | 0x80;\n    }\n}\n// TextEncoder and TextDecoder are standardized in whatwg encoding:\n// https://encoding.spec.whatwg.org/\n// and available in all the modern browsers:\n// https://caniuse.com/textencoder\n// They are available in Node.js since v12 LTS as well:\n// https://nodejs.org/api/globals.html#textencoder\nconst sharedTextEncoder = new TextEncoder();\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/encode-string.ts` for details.\nconst TEXT_ENCODER_THRESHOLD = 50;\nfunction utf8EncodeTE(str, output, outputOffset) {\n    sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\nfunction utf8Encode(str, output, outputOffset) {\n    if (str.length > TEXT_ENCODER_THRESHOLD) {\n        utf8EncodeTE(str, output, outputOffset);\n    }\n    else {\n        utf8EncodeJs(str, output, outputOffset);\n    }\n}\nconst CHUNK_SIZE = 4096;\nfunction utf8DecodeJs(bytes, inputOffset, byteLength) {\n    let offset = inputOffset;\n    const end = offset + byteLength;\n    const units = [];\n    let result = \"\";\n    while (offset < end) {\n        const byte1 = bytes[offset++];\n        if ((byte1 & 0x80) === 0) {\n            // 1 byte\n            units.push(byte1);\n        }\n        else if ((byte1 & 0xe0) === 0xc0) {\n            // 2 bytes\n            const byte2 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 6) | byte2);\n        }\n        else if ((byte1 & 0xf0) === 0xe0) {\n            // 3 bytes\n            const byte2 = bytes[offset++] & 0x3f;\n            const byte3 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n        }\n        else if ((byte1 & 0xf8) === 0xf0) {\n            // 4 bytes\n            const byte2 = bytes[offset++] & 0x3f;\n            const byte3 = bytes[offset++] & 0x3f;\n            const byte4 = bytes[offset++] & 0x3f;\n            let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n            if (unit > 0xffff) {\n                unit -= 0x10000;\n                units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n                unit = 0xdc00 | (unit & 0x3ff);\n            }\n            units.push(unit);\n        }\n        else {\n            units.push(byte1);\n        }\n        if (units.length >= CHUNK_SIZE) {\n            result += String.fromCharCode(...units);\n            units.length = 0;\n        }\n    }\n    if (units.length > 0) {\n        result += String.fromCharCode(...units);\n    }\n    return result;\n}\nconst sharedTextDecoder = new TextDecoder();\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/decode-string.ts` for details.\nconst TEXT_DECODER_THRESHOLD = 200;\nfunction utf8DecodeTD(bytes, inputOffset, byteLength) {\n    const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n    return sharedTextDecoder.decode(stringBytes);\n}\nfunction utf8Decode(bytes, inputOffset, byteLength) {\n    if (byteLength > TEXT_DECODER_THRESHOLD) {\n        return utf8DecodeTD(bytes, inputOffset, byteLength);\n    }\n    else {\n        return utf8DecodeJs(bytes, inputOffset, byteLength);\n    }\n}\n//# sourceMappingURL=utf8.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\n");

/***/ })

};
;