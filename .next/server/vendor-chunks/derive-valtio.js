"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/derive-valtio";
exports.ids = ["vendor-chunks/derive-valtio"];
exports.modules = {

/***/ "(ssr)/./node_modules/derive-valtio/dist/index.modern.js":
/*!*********************************************************!*\
  !*** ./node_modules/derive-valtio/dist/index.modern.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   derive: () => (/* binding */ f),\n/* harmony export */   underive: () => (/* binding */ u),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/./node_modules/valtio/esm/vanilla.mjs\");\nconst o=new WeakMap,r=new WeakMap,s=(e,t)=>{const n=o.get(e);n&&(n[0].forEach(t=>{const{d:n}=t;e!==n&&s(n)}),++n[2],t&&n[3].add(t))},l=e=>{const t=o.get(e);t&&(--t[2],t[2]||(t[3].forEach(e=>e()),t[3].clear()),t[0].forEach(t=>{const{d:n}=t;e!==n&&l(n)}))},c=e=>{const{s:n,d:c}=e;let a=r.get(c);a||(a=[new Set],r.set(e.d,a)),a[0].add(e);let d=o.get(n);if(!d){const e=new Set,r=(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(n,t=>{e.forEach(e=>{const{d:o,c:r,n:c,i:a}=e;n===o&&t.every(e=>1===e[1].length&&a.includes(e[1][0]))||e.p||(s(n,r),c?l(n):e.p=Promise.resolve().then(()=>{delete e.p,l(n)}))})},!0);d=[e,r,0,new Set],o.set(n,d)}d[0].add(e)},a=e=>{const{s:t,d:n}=e,s=r.get(n);null==s||s[0].delete(e),0===(null==s?void 0:s[0].size)&&r.delete(n);const l=o.get(t);if(l){const[n,r]=l;n.delete(e),n.size||(r(),o.delete(t))}},d=e=>{const t=r.get(e);return t?Array.from(t[0]):[]},i={add:c,remove:a,list:d};function f(t,r){const s=(null==r?void 0:r.proxy)||(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({}),l=!(null==r||!r.sync),d=Object.keys(t);return d.forEach(e=>{if(Object.getOwnPropertyDescriptor(s,e))throw new Error(\"object property already defined\");const r=t[e];let i=null;const f=()=>{if(i){if(Array.from(i).map(([e])=>((e,t)=>{const n=o.get(e);return!(null==n||!n[2]||(n[3].add(t),0))})(e,f)).some(e=>e))return;if(Array.from(i).every(([e,t])=>(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.getVersion)(e)===t.v))return}const t=new Map,u=r(e=>(t.set(e,{v:(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.getVersion)(e)}),e)),p=()=>{var n;t.forEach((t,n)=>{var o;const r=null==(o=i)||null==(o=o.get(n))?void 0:o.s;if(r)t.s=r;else{const o={s:n,d:s,k:e,c:f,n:l,i:d};c(o),t.s=o}}),null==(n=i)||n.forEach((e,n)=>{!t.has(n)&&e.s&&a(e.s)}),i=t};u instanceof Promise?u.finally(p):p(),s[e]=u};f()}),s}function u(e,t){const n=null!=t&&t.delete?new Set:null;d(e).forEach(e=>{const{k:o}=e;null!=t&&t.keys&&!t.keys.includes(o)||(a(e),n&&n.add(o))}),n&&n.forEach(t=>{delete e[t]})}\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/derive-valtio/dist/index.modern.js\n");

/***/ })

};
;