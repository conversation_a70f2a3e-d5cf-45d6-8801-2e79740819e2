"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino-std-serializers";
exports.ids = ["vendor-chunks/pino-std-serializers"];
exports.modules = {

/***/ "(ssr)/./node_modules/pino-std-serializers/index.js":
/*!****************************************************!*\
  !*** ./node_modules/pino-std-serializers/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst errSerializer = __webpack_require__(/*! ./lib/err */ \"(ssr)/./node_modules/pino-std-serializers/lib/err.js\")\nconst reqSerializers = __webpack_require__(/*! ./lib/req */ \"(ssr)/./node_modules/pino-std-serializers/lib/req.js\")\nconst resSerializers = __webpack_require__(/*! ./lib/res */ \"(ssr)/./node_modules/pino-std-serializers/lib/res.js\")\n\nmodule.exports = {\n  err: errSerializer,\n  mapHttpRequest: reqSerializers.mapHttpRequest,\n  mapHttpResponse: resSerializers.mapHttpResponse,\n  req: reqSerializers.reqSerializer,\n  res: resSerializers.resSerializer,\n\n  wrapErrorSerializer: function wrapErrorSerializer (customSerializer) {\n    if (customSerializer === errSerializer) return customSerializer\n    return function wrapErrSerializer (err) {\n      return customSerializer(errSerializer(err))\n    }\n  },\n\n  wrapRequestSerializer: function wrapRequestSerializer (customSerializer) {\n    if (customSerializer === reqSerializers.reqSerializer) return customSerializer\n    return function wrappedReqSerializer (req) {\n      return customSerializer(reqSerializers.reqSerializer(req))\n    }\n  },\n\n  wrapResponseSerializer: function wrapResponseSerializer (customSerializer) {\n    if (customSerializer === resSerializers.resSerializer) return customSerializer\n    return function wrappedResSerializer (res) {\n      return customSerializer(resSerializers.resSerializer(res))\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino-std-serializers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino-std-serializers/lib/err.js":
/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/err.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = errSerializer\n\nconst { toString } = Object.prototype\nconst seen = Symbol('circular-ref-tag')\nconst rawSymbol = Symbol('pino-raw-err-ref')\nconst pinoErrProto = Object.create({}, {\n  type: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  message: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  stack: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoErrProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction errSerializer (err) {\n  if (!(err instanceof Error)) {\n    return err\n  }\n\n  err[seen] = undefined // tag to prevent re-looking at this\n  const _err = Object.create(pinoErrProto)\n  _err.type = toString.call(err.constructor) === '[object Function]'\n    ? err.constructor.name\n    : err.name\n  _err.message = err.message\n  _err.stack = err.stack\n  for (const key in err) {\n    if (_err[key] === undefined) {\n      const val = err[key]\n      if (val instanceof Error) {\n        /* eslint-disable no-prototype-builtins */\n        if (!val.hasOwnProperty(seen)) {\n          _err[key] = errSerializer(val)\n        }\n      } else {\n        _err[key] = val\n      }\n    }\n  }\n\n  delete err[seen] // clean up tag in case err is serialized again later\n  _err.raw = err\n  return _err\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino-std-serializers/lib/err.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino-std-serializers/lib/req.js":
/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/req.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  mapHttpRequest,\n  reqSerializer\n}\n\nconst rawSymbol = Symbol('pino-raw-req-ref')\nconst pinoReqProto = Object.create({}, {\n  id: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  method: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  url: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  query: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  params: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  headers: {\n    enumerable: true,\n    writable: true,\n    value: {}\n  },\n  remoteAddress: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  remotePort: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoReqProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction reqSerializer (req) {\n  // req.info is for hapi compat.\n  const connection = req.info || req.socket\n  const _req = Object.create(pinoReqProto)\n  _req.id = (typeof req.id === 'function' ? req.id() : (req.id || (req.info ? req.info.id : undefined)))\n  _req.method = req.method\n  // req.originalUrl is for expressjs compat.\n  if (req.originalUrl) {\n    _req.url = req.originalUrl\n    _req.query = req.query\n    _req.params = req.params\n  } else {\n    // req.url.path is  for hapi compat.\n    _req.url = req.path || (req.url ? (req.url.path || req.url) : undefined)\n  }\n  _req.headers = req.headers\n  _req.remoteAddress = connection && connection.remoteAddress\n  _req.remotePort = connection && connection.remotePort\n  // req.raw is  for hapi compat/equivalence\n  _req.raw = req.raw || req\n  return _req\n}\n\nfunction mapHttpRequest (req) {\n  return {\n    req: reqSerializer(req)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino-std-serializers/lib/req.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino-std-serializers/lib/res.js":
/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/res.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  mapHttpResponse,\n  resSerializer\n}\n\nconst rawSymbol = Symbol('pino-raw-res-ref')\nconst pinoResProto = Object.create({}, {\n  statusCode: {\n    enumerable: true,\n    writable: true,\n    value: 0\n  },\n  headers: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoResProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction resSerializer (res) {\n  const _res = Object.create(pinoResProto)\n  _res.statusCode = res.statusCode\n  _res.headers = res.getHeaders ? res.getHeaders() : res._headers\n  _res.raw = res\n  return _res\n}\n\nfunction mapHttpResponse (res) {\n  return {\n    res: resSerializer(res)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby1zdGQtc2VyaWFsaXplcnMvbGliL3Jlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9waW5vLXN0ZC1zZXJpYWxpemVycy9saWIvcmVzLmpzPzVmZTgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBtYXBIdHRwUmVzcG9uc2UsXG4gIHJlc1NlcmlhbGl6ZXJcbn1cblxuY29uc3QgcmF3U3ltYm9sID0gU3ltYm9sKCdwaW5vLXJhdy1yZXMtcmVmJylcbmNvbnN0IHBpbm9SZXNQcm90byA9IE9iamVjdC5jcmVhdGUoe30sIHtcbiAgc3RhdHVzQ29kZToge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgd3JpdGFibGU6IHRydWUsXG4gICAgdmFsdWU6IDBcbiAgfSxcbiAgaGVhZGVyczoge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgd3JpdGFibGU6IHRydWUsXG4gICAgdmFsdWU6ICcnXG4gIH0sXG4gIHJhdzoge1xuICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgIGdldDogZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIHRoaXNbcmF3U3ltYm9sXVxuICAgIH0sXG4gICAgc2V0OiBmdW5jdGlvbiAodmFsKSB7XG4gICAgICB0aGlzW3Jhd1N5bWJvbF0gPSB2YWxcbiAgICB9XG4gIH1cbn0pXG5PYmplY3QuZGVmaW5lUHJvcGVydHkocGlub1Jlc1Byb3RvLCByYXdTeW1ib2wsIHtcbiAgd3JpdGFibGU6IHRydWUsXG4gIHZhbHVlOiB7fVxufSlcblxuZnVuY3Rpb24gcmVzU2VyaWFsaXplciAocmVzKSB7XG4gIGNvbnN0IF9yZXMgPSBPYmplY3QuY3JlYXRlKHBpbm9SZXNQcm90bylcbiAgX3Jlcy5zdGF0dXNDb2RlID0gcmVzLnN0YXR1c0NvZGVcbiAgX3Jlcy5oZWFkZXJzID0gcmVzLmdldEhlYWRlcnMgPyByZXMuZ2V0SGVhZGVycygpIDogcmVzLl9oZWFkZXJzXG4gIF9yZXMucmF3ID0gcmVzXG4gIHJldHVybiBfcmVzXG59XG5cbmZ1bmN0aW9uIG1hcEh0dHBSZXNwb25zZSAocmVzKSB7XG4gIHJldHVybiB7XG4gICAgcmVzOiByZXNTZXJpYWxpemVyKHJlcylcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino-std-serializers/lib/res.js\n");

/***/ })

};
;