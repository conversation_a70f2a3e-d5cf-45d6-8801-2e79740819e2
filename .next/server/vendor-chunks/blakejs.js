/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/blakejs";
exports.ids = ["vendor-chunks/blakejs"];
exports.modules = {

/***/ "(ssr)/./node_modules/blakejs/blake2b.js":
/*!*****************************************!*\
  !*** ./node_modules/blakejs/blake2b.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Blake2B in pure Javascript\n// Adapted from the reference implementation in RFC7693\n// Ported to Javascript by DC - https://github.com/dcposch\n\nconst util = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/blakejs/util.js\")\n\n// 64-bit unsigned addition\n// Sets v[a,a+1] += v[b,b+1]\n// v should be a Uint32Array\nfunction ADD64AA (v, a, b) {\n  const o0 = v[a] + v[b]\n  let o1 = v[a + 1] + v[b + 1]\n  if (o0 >= 0x100000000) {\n    o1++\n  }\n  v[a] = o0\n  v[a + 1] = o1\n}\n\n// 64-bit unsigned addition\n// Sets v[a,a+1] += b\n// b0 is the low 32 bits of b, b1 represents the high 32 bits\nfunction ADD64AC (v, a, b0, b1) {\n  let o0 = v[a] + b0\n  if (b0 < 0) {\n    o0 += 0x100000000\n  }\n  let o1 = v[a + 1] + b1\n  if (o0 >= 0x100000000) {\n    o1++\n  }\n  v[a] = o0\n  v[a + 1] = o1\n}\n\n// Little-endian byte access\nfunction B2B_GET32 (arr, i) {\n  return arr[i] ^ (arr[i + 1] << 8) ^ (arr[i + 2] << 16) ^ (arr[i + 3] << 24)\n}\n\n// G Mixing function\n// The ROTRs are inlined for speed\nfunction B2B_G (a, b, c, d, ix, iy) {\n  const x0 = m[ix]\n  const x1 = m[ix + 1]\n  const y0 = m[iy]\n  const y1 = m[iy + 1]\n\n  ADD64AA(v, a, b) // v[a,a+1] += v[b,b+1] ... in JS we must store a uint64 as two uint32s\n  ADD64AC(v, a, x0, x1) // v[a, a+1] += x ... x0 is the low 32 bits of x, x1 is the high 32 bits\n\n  // v[d,d+1] = (v[d,d+1] xor v[a,a+1]) rotated to the right by 32 bits\n  let xor0 = v[d] ^ v[a]\n  let xor1 = v[d + 1] ^ v[a + 1]\n  v[d] = xor1\n  v[d + 1] = xor0\n\n  ADD64AA(v, c, d)\n\n  // v[b,b+1] = (v[b,b+1] xor v[c,c+1]) rotated right by 24 bits\n  xor0 = v[b] ^ v[c]\n  xor1 = v[b + 1] ^ v[c + 1]\n  v[b] = (xor0 >>> 24) ^ (xor1 << 8)\n  v[b + 1] = (xor1 >>> 24) ^ (xor0 << 8)\n\n  ADD64AA(v, a, b)\n  ADD64AC(v, a, y0, y1)\n\n  // v[d,d+1] = (v[d,d+1] xor v[a,a+1]) rotated right by 16 bits\n  xor0 = v[d] ^ v[a]\n  xor1 = v[d + 1] ^ v[a + 1]\n  v[d] = (xor0 >>> 16) ^ (xor1 << 16)\n  v[d + 1] = (xor1 >>> 16) ^ (xor0 << 16)\n\n  ADD64AA(v, c, d)\n\n  // v[b,b+1] = (v[b,b+1] xor v[c,c+1]) rotated right by 63 bits\n  xor0 = v[b] ^ v[c]\n  xor1 = v[b + 1] ^ v[c + 1]\n  v[b] = (xor1 >>> 31) ^ (xor0 << 1)\n  v[b + 1] = (xor0 >>> 31) ^ (xor1 << 1)\n}\n\n// Initialization Vector\nconst BLAKE2B_IV32 = new Uint32Array([\n  0xf3bcc908, 0x6a09e667, 0x84caa73b, 0xbb67ae85, 0xfe94f82b, 0x3c6ef372,\n  0x5f1d36f1, 0xa54ff53a, 0xade682d1, 0x510e527f, 0x2b3e6c1f, 0x9b05688c,\n  0xfb41bd6b, 0x1f83d9ab, 0x137e2179, 0x5be0cd19\n])\n\nconst SIGMA8 = [\n  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 14, 10, 4, 8, 9, 15, 13,\n  6, 1, 12, 0, 2, 11, 7, 5, 3, 11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1,\n  9, 4, 7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8, 9, 0, 5, 7, 2, 4,\n  10, 15, 14, 1, 11, 12, 6, 8, 3, 13, 2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5,\n  15, 14, 1, 9, 12, 5, 1, 15, 14, 13, 4, 10, 0, 7, 6, 3, 9, 2, 8, 11, 13, 11, 7,\n  14, 12, 1, 3, 9, 5, 0, 15, 4, 8, 6, 2, 10, 6, 15, 14, 9, 11, 3, 0, 8, 12, 2,\n  13, 7, 1, 4, 10, 5, 10, 2, 8, 4, 7, 6, 1, 5, 15, 11, 9, 14, 3, 12, 13, 0, 0,\n  1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 14, 10, 4, 8, 9, 15, 13, 6,\n  1, 12, 0, 2, 11, 7, 5, 3\n]\n\n// These are offsets into a uint64 buffer.\n// Multiply them all by 2 to make them offsets into a uint32 buffer,\n// because this is Javascript and we don't have uint64s\nconst SIGMA82 = new Uint8Array(\n  SIGMA8.map(function (x) {\n    return x * 2\n  })\n)\n\n// Compression function. 'last' flag indicates last block.\n// Note we're representing 16 uint64s as 32 uint32s\nconst v = new Uint32Array(32)\nconst m = new Uint32Array(32)\nfunction blake2bCompress (ctx, last) {\n  let i = 0\n\n  // init work variables\n  for (i = 0; i < 16; i++) {\n    v[i] = ctx.h[i]\n    v[i + 16] = BLAKE2B_IV32[i]\n  }\n\n  // low 64 bits of offset\n  v[24] = v[24] ^ ctx.t\n  v[25] = v[25] ^ (ctx.t / 0x100000000)\n  // high 64 bits not supported, offset may not be higher than 2**53-1\n\n  // last block flag set ?\n  if (last) {\n    v[28] = ~v[28]\n    v[29] = ~v[29]\n  }\n\n  // get little-endian words\n  for (i = 0; i < 32; i++) {\n    m[i] = B2B_GET32(ctx.b, 4 * i)\n  }\n\n  // twelve rounds of mixing\n  // uncomment the DebugPrint calls to log the computation\n  // and match the RFC sample documentation\n  // util.debugPrint('          m[16]', m, 64)\n  for (i = 0; i < 12; i++) {\n    // util.debugPrint('   (i=' + (i < 10 ? ' ' : '') + i + ') v[16]', v, 64)\n    B2B_G(0, 8, 16, 24, SIGMA82[i * 16 + 0], SIGMA82[i * 16 + 1])\n    B2B_G(2, 10, 18, 26, SIGMA82[i * 16 + 2], SIGMA82[i * 16 + 3])\n    B2B_G(4, 12, 20, 28, SIGMA82[i * 16 + 4], SIGMA82[i * 16 + 5])\n    B2B_G(6, 14, 22, 30, SIGMA82[i * 16 + 6], SIGMA82[i * 16 + 7])\n    B2B_G(0, 10, 20, 30, SIGMA82[i * 16 + 8], SIGMA82[i * 16 + 9])\n    B2B_G(2, 12, 22, 24, SIGMA82[i * 16 + 10], SIGMA82[i * 16 + 11])\n    B2B_G(4, 14, 16, 26, SIGMA82[i * 16 + 12], SIGMA82[i * 16 + 13])\n    B2B_G(6, 8, 18, 28, SIGMA82[i * 16 + 14], SIGMA82[i * 16 + 15])\n  }\n  // util.debugPrint('   (i=12) v[16]', v, 64)\n\n  for (i = 0; i < 16; i++) {\n    ctx.h[i] = ctx.h[i] ^ v[i] ^ v[i + 16]\n  }\n  // util.debugPrint('h[8]', ctx.h, 64)\n}\n\n// reusable parameterBlock\nconst parameterBlock = new Uint8Array([\n  0,\n  0,\n  0,\n  0, //  0: outlen, keylen, fanout, depth\n  0,\n  0,\n  0,\n  0, //  4: leaf length, sequential mode\n  0,\n  0,\n  0,\n  0, //  8: node offset\n  0,\n  0,\n  0,\n  0, // 12: node offset\n  0,\n  0,\n  0,\n  0, // 16: node depth, inner length, rfu\n  0,\n  0,\n  0,\n  0, // 20: rfu\n  0,\n  0,\n  0,\n  0, // 24: rfu\n  0,\n  0,\n  0,\n  0, // 28: rfu\n  0,\n  0,\n  0,\n  0, // 32: salt\n  0,\n  0,\n  0,\n  0, // 36: salt\n  0,\n  0,\n  0,\n  0, // 40: salt\n  0,\n  0,\n  0,\n  0, // 44: salt\n  0,\n  0,\n  0,\n  0, // 48: personal\n  0,\n  0,\n  0,\n  0, // 52: personal\n  0,\n  0,\n  0,\n  0, // 56: personal\n  0,\n  0,\n  0,\n  0 // 60: personal\n])\n\n// Creates a BLAKE2b hashing context\n// Requires an output length between 1 and 64 bytes\n// Takes an optional Uint8Array key\n// Takes an optinal Uint8Array salt\n// Takes an optinal Uint8Array personal\nfunction blake2bInit (outlen, key, salt, personal) {\n  if (outlen === 0 || outlen > 64) {\n    throw new Error('Illegal output length, expected 0 < length <= 64')\n  }\n  if (key && key.length > 64) {\n    throw new Error('Illegal key, expected Uint8Array with 0 < length <= 64')\n  }\n  if (salt && salt.length !== 16) {\n    throw new Error('Illegal salt, expected Uint8Array with length is 16')\n  }\n  if (personal && personal.length !== 16) {\n    throw new Error('Illegal personal, expected Uint8Array with length is 16')\n  }\n\n  // state, 'param block'\n  const ctx = {\n    b: new Uint8Array(128),\n    h: new Uint32Array(16),\n    t: 0, // input count\n    c: 0, // pointer within buffer\n    outlen: outlen // output length in bytes\n  }\n\n  // initialize parameterBlock before usage\n  parameterBlock.fill(0)\n  parameterBlock[0] = outlen\n  if (key) parameterBlock[1] = key.length\n  parameterBlock[2] = 1 // fanout\n  parameterBlock[3] = 1 // depth\n  if (salt) parameterBlock.set(salt, 32)\n  if (personal) parameterBlock.set(personal, 48)\n\n  // initialize hash state\n  for (let i = 0; i < 16; i++) {\n    ctx.h[i] = BLAKE2B_IV32[i] ^ B2B_GET32(parameterBlock, i * 4)\n  }\n\n  // key the hash, if applicable\n  if (key) {\n    blake2bUpdate(ctx, key)\n    // at the end\n    ctx.c = 128\n  }\n\n  return ctx\n}\n\n// Updates a BLAKE2b streaming hash\n// Requires hash context and Uint8Array (byte array)\nfunction blake2bUpdate (ctx, input) {\n  for (let i = 0; i < input.length; i++) {\n    if (ctx.c === 128) {\n      // buffer full ?\n      ctx.t += ctx.c // add counters\n      blake2bCompress(ctx, false) // compress (not last)\n      ctx.c = 0 // counter to zero\n    }\n    ctx.b[ctx.c++] = input[i]\n  }\n}\n\n// Completes a BLAKE2b streaming hash\n// Returns a Uint8Array containing the message digest\nfunction blake2bFinal (ctx) {\n  ctx.t += ctx.c // mark last block offset\n\n  while (ctx.c < 128) {\n    // fill up with zeros\n    ctx.b[ctx.c++] = 0\n  }\n  blake2bCompress(ctx, true) // final block flag = 1\n\n  // little endian convert and store\n  const out = new Uint8Array(ctx.outlen)\n  for (let i = 0; i < ctx.outlen; i++) {\n    out[i] = ctx.h[i >> 2] >> (8 * (i & 3))\n  }\n  return out\n}\n\n// Computes the BLAKE2B hash of a string or byte array, and returns a Uint8Array\n//\n// Returns a n-byte Uint8Array\n//\n// Parameters:\n// - input - the input bytes, as a string, Buffer or Uint8Array\n// - key - optional key Uint8Array, up to 64 bytes\n// - outlen - optional output length in bytes, default 64\n// - salt - optional salt bytes, string, Buffer or Uint8Array\n// - personal - optional personal bytes, string, Buffer or Uint8Array\nfunction blake2b (input, key, outlen, salt, personal) {\n  // preprocess inputs\n  outlen = outlen || 64\n  input = util.normalizeInput(input)\n  if (salt) {\n    salt = util.normalizeInput(salt)\n  }\n  if (personal) {\n    personal = util.normalizeInput(personal)\n  }\n\n  // do the math\n  const ctx = blake2bInit(outlen, key, salt, personal)\n  blake2bUpdate(ctx, input)\n  return blake2bFinal(ctx)\n}\n\n// Computes the BLAKE2B hash of a string or byte array\n//\n// Returns an n-byte hash in hex, all lowercase\n//\n// Parameters:\n// - input - the input bytes, as a string, Buffer, or Uint8Array\n// - key - optional key Uint8Array, up to 64 bytes\n// - outlen - optional output length in bytes, default 64\n// - salt - optional salt bytes, string, Buffer or Uint8Array\n// - personal - optional personal bytes, string, Buffer or Uint8Array\nfunction blake2bHex (input, key, outlen, salt, personal) {\n  const output = blake2b(input, key, outlen, salt, personal)\n  return util.toHex(output)\n}\n\nmodule.exports = {\n  blake2b: blake2b,\n  blake2bHex: blake2bHex,\n  blake2bInit: blake2bInit,\n  blake2bUpdate: blake2bUpdate,\n  blake2bFinal: blake2bFinal\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/blakejs/blake2b.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/blakejs/blake2s.js":
/*!*****************************************!*\
  !*** ./node_modules/blakejs/blake2s.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// BLAKE2s hash function in pure Javascript\n// Adapted from the reference implementation in RFC7693\n// Ported to Javascript by DC - https://github.com/dcposch\n\nconst util = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/blakejs/util.js\")\n\n// Little-endian byte access.\n// Expects a Uint8Array and an index\n// Returns the little-endian uint32 at v[i..i+3]\nfunction B2S_GET32 (v, i) {\n  return v[i] ^ (v[i + 1] << 8) ^ (v[i + 2] << 16) ^ (v[i + 3] << 24)\n}\n\n// Mixing function G.\nfunction B2S_G (a, b, c, d, x, y) {\n  v[a] = v[a] + v[b] + x\n  v[d] = ROTR32(v[d] ^ v[a], 16)\n  v[c] = v[c] + v[d]\n  v[b] = ROTR32(v[b] ^ v[c], 12)\n  v[a] = v[a] + v[b] + y\n  v[d] = ROTR32(v[d] ^ v[a], 8)\n  v[c] = v[c] + v[d]\n  v[b] = ROTR32(v[b] ^ v[c], 7)\n}\n\n// 32-bit right rotation\n// x should be a uint32\n// y must be between 1 and 31, inclusive\nfunction ROTR32 (x, y) {\n  return (x >>> y) ^ (x << (32 - y))\n}\n\n// Initialization Vector.\nconst BLAKE2S_IV = new Uint32Array([\n  0x6a09e667,\n  0xbb67ae85,\n  0x3c6ef372,\n  0xa54ff53a,\n  0x510e527f,\n  0x9b05688c,\n  0x1f83d9ab,\n  0x5be0cd19\n])\n\nconst SIGMA = new Uint8Array([\n  0,\n  1,\n  2,\n  3,\n  4,\n  5,\n  6,\n  7,\n  8,\n  9,\n  10,\n  11,\n  12,\n  13,\n  14,\n  15,\n  14,\n  10,\n  4,\n  8,\n  9,\n  15,\n  13,\n  6,\n  1,\n  12,\n  0,\n  2,\n  11,\n  7,\n  5,\n  3,\n  11,\n  8,\n  12,\n  0,\n  5,\n  2,\n  15,\n  13,\n  10,\n  14,\n  3,\n  6,\n  7,\n  1,\n  9,\n  4,\n  7,\n  9,\n  3,\n  1,\n  13,\n  12,\n  11,\n  14,\n  2,\n  6,\n  5,\n  10,\n  4,\n  0,\n  15,\n  8,\n  9,\n  0,\n  5,\n  7,\n  2,\n  4,\n  10,\n  15,\n  14,\n  1,\n  11,\n  12,\n  6,\n  8,\n  3,\n  13,\n  2,\n  12,\n  6,\n  10,\n  0,\n  11,\n  8,\n  3,\n  4,\n  13,\n  7,\n  5,\n  15,\n  14,\n  1,\n  9,\n  12,\n  5,\n  1,\n  15,\n  14,\n  13,\n  4,\n  10,\n  0,\n  7,\n  6,\n  3,\n  9,\n  2,\n  8,\n  11,\n  13,\n  11,\n  7,\n  14,\n  12,\n  1,\n  3,\n  9,\n  5,\n  0,\n  15,\n  4,\n  8,\n  6,\n  2,\n  10,\n  6,\n  15,\n  14,\n  9,\n  11,\n  3,\n  0,\n  8,\n  12,\n  2,\n  13,\n  7,\n  1,\n  4,\n  10,\n  5,\n  10,\n  2,\n  8,\n  4,\n  7,\n  6,\n  1,\n  5,\n  15,\n  11,\n  9,\n  14,\n  3,\n  12,\n  13,\n  0\n])\n\n// Compression function. \"last\" flag indicates last block\nconst v = new Uint32Array(16)\nconst m = new Uint32Array(16)\nfunction blake2sCompress (ctx, last) {\n  let i = 0\n  for (i = 0; i < 8; i++) {\n    // init work variables\n    v[i] = ctx.h[i]\n    v[i + 8] = BLAKE2S_IV[i]\n  }\n\n  v[12] ^= ctx.t // low 32 bits of offset\n  v[13] ^= ctx.t / 0x100000000 // high 32 bits\n  if (last) {\n    // last block flag set ?\n    v[14] = ~v[14]\n  }\n\n  for (i = 0; i < 16; i++) {\n    // get little-endian words\n    m[i] = B2S_GET32(ctx.b, 4 * i)\n  }\n\n  // ten rounds of mixing\n  // uncomment the DebugPrint calls to log the computation\n  // and match the RFC sample documentation\n  // util.debugPrint('          m[16]', m, 32)\n  for (i = 0; i < 10; i++) {\n    // util.debugPrint('   (i=' + i + ')  v[16]', v, 32)\n    B2S_G(0, 4, 8, 12, m[SIGMA[i * 16 + 0]], m[SIGMA[i * 16 + 1]])\n    B2S_G(1, 5, 9, 13, m[SIGMA[i * 16 + 2]], m[SIGMA[i * 16 + 3]])\n    B2S_G(2, 6, 10, 14, m[SIGMA[i * 16 + 4]], m[SIGMA[i * 16 + 5]])\n    B2S_G(3, 7, 11, 15, m[SIGMA[i * 16 + 6]], m[SIGMA[i * 16 + 7]])\n    B2S_G(0, 5, 10, 15, m[SIGMA[i * 16 + 8]], m[SIGMA[i * 16 + 9]])\n    B2S_G(1, 6, 11, 12, m[SIGMA[i * 16 + 10]], m[SIGMA[i * 16 + 11]])\n    B2S_G(2, 7, 8, 13, m[SIGMA[i * 16 + 12]], m[SIGMA[i * 16 + 13]])\n    B2S_G(3, 4, 9, 14, m[SIGMA[i * 16 + 14]], m[SIGMA[i * 16 + 15]])\n  }\n  // util.debugPrint('   (i=10) v[16]', v, 32)\n\n  for (i = 0; i < 8; i++) {\n    ctx.h[i] ^= v[i] ^ v[i + 8]\n  }\n  // util.debugPrint('h[8]', ctx.h, 32)\n}\n\n// Creates a BLAKE2s hashing context\n// Requires an output length between 1 and 32 bytes\n// Takes an optional Uint8Array key\nfunction blake2sInit (outlen, key) {\n  if (!(outlen > 0 && outlen <= 32)) {\n    throw new Error('Incorrect output length, should be in [1, 32]')\n  }\n  const keylen = key ? key.length : 0\n  if (key && !(keylen > 0 && keylen <= 32)) {\n    throw new Error('Incorrect key length, should be in [1, 32]')\n  }\n\n  const ctx = {\n    h: new Uint32Array(BLAKE2S_IV), // hash state\n    b: new Uint8Array(64), // input block\n    c: 0, // pointer within block\n    t: 0, // input count\n    outlen: outlen // output length in bytes\n  }\n  ctx.h[0] ^= 0x01010000 ^ (keylen << 8) ^ outlen\n\n  if (keylen > 0) {\n    blake2sUpdate(ctx, key)\n    ctx.c = 64 // at the end\n  }\n\n  return ctx\n}\n\n// Updates a BLAKE2s streaming hash\n// Requires hash context and Uint8Array (byte array)\nfunction blake2sUpdate (ctx, input) {\n  for (let i = 0; i < input.length; i++) {\n    if (ctx.c === 64) {\n      // buffer full ?\n      ctx.t += ctx.c // add counters\n      blake2sCompress(ctx, false) // compress (not last)\n      ctx.c = 0 // counter to zero\n    }\n    ctx.b[ctx.c++] = input[i]\n  }\n}\n\n// Completes a BLAKE2s streaming hash\n// Returns a Uint8Array containing the message digest\nfunction blake2sFinal (ctx) {\n  ctx.t += ctx.c // mark last block offset\n  while (ctx.c < 64) {\n    // fill up with zeros\n    ctx.b[ctx.c++] = 0\n  }\n  blake2sCompress(ctx, true) // final block flag = 1\n\n  // little endian convert and store\n  const out = new Uint8Array(ctx.outlen)\n  for (let i = 0; i < ctx.outlen; i++) {\n    out[i] = (ctx.h[i >> 2] >> (8 * (i & 3))) & 0xff\n  }\n  return out\n}\n\n// Computes the BLAKE2S hash of a string or byte array, and returns a Uint8Array\n//\n// Returns a n-byte Uint8Array\n//\n// Parameters:\n// - input - the input bytes, as a string, Buffer, or Uint8Array\n// - key - optional key Uint8Array, up to 32 bytes\n// - outlen - optional output length in bytes, default 64\nfunction blake2s (input, key, outlen) {\n  // preprocess inputs\n  outlen = outlen || 32\n  input = util.normalizeInput(input)\n\n  // do the math\n  const ctx = blake2sInit(outlen, key)\n  blake2sUpdate(ctx, input)\n  return blake2sFinal(ctx)\n}\n\n// Computes the BLAKE2S hash of a string or byte array\n//\n// Returns an n-byte hash in hex, all lowercase\n//\n// Parameters:\n// - input - the input bytes, as a string, Buffer, or Uint8Array\n// - key - optional key Uint8Array, up to 32 bytes\n// - outlen - optional output length in bytes, default 64\nfunction blake2sHex (input, key, outlen) {\n  const output = blake2s(input, key, outlen)\n  return util.toHex(output)\n}\n\nmodule.exports = {\n  blake2s: blake2s,\n  blake2sHex: blake2sHex,\n  blake2sInit: blake2sInit,\n  blake2sUpdate: blake2sUpdate,\n  blake2sFinal: blake2sFinal\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/blakejs/blake2s.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/blakejs/index.js":
/*!***************************************!*\
  !*** ./node_modules/blakejs/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const b2b = __webpack_require__(/*! ./blake2b */ \"(ssr)/./node_modules/blakejs/blake2b.js\")\nconst b2s = __webpack_require__(/*! ./blake2s */ \"(ssr)/./node_modules/blakejs/blake2s.js\")\n\nmodule.exports = {\n  blake2b: b2b.blake2b,\n  blake2bHex: b2b.blake2bHex,\n  blake2bInit: b2b.blake2bInit,\n  blake2bUpdate: b2b.blake2bUpdate,\n  blake2bFinal: b2b.blake2bFinal,\n  blake2s: b2s.blake2s,\n  blake2sHex: b2s.blake2sHex,\n  blake2sInit: b2s.blake2sInit,\n  blake2sUpdate: b2s.blake2sUpdate,\n  blake2sFinal: b2s.blake2sFinal\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmxha2Vqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxZQUFZLG1CQUFPLENBQUMsMERBQVc7QUFDL0IsWUFBWSxtQkFBTyxDQUFDLDBEQUFXOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9ibGFrZWpzL2luZGV4LmpzPzhhY2YiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYjJiID0gcmVxdWlyZSgnLi9ibGFrZTJiJylcbmNvbnN0IGIycyA9IHJlcXVpcmUoJy4vYmxha2UycycpXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBibGFrZTJiOiBiMmIuYmxha2UyYixcbiAgYmxha2UyYkhleDogYjJiLmJsYWtlMmJIZXgsXG4gIGJsYWtlMmJJbml0OiBiMmIuYmxha2UyYkluaXQsXG4gIGJsYWtlMmJVcGRhdGU6IGIyYi5ibGFrZTJiVXBkYXRlLFxuICBibGFrZTJiRmluYWw6IGIyYi5ibGFrZTJiRmluYWwsXG4gIGJsYWtlMnM6IGIycy5ibGFrZTJzLFxuICBibGFrZTJzSGV4OiBiMnMuYmxha2Uyc0hleCxcbiAgYmxha2Uyc0luaXQ6IGIycy5ibGFrZTJzSW5pdCxcbiAgYmxha2Uyc1VwZGF0ZTogYjJzLmJsYWtlMnNVcGRhdGUsXG4gIGJsYWtlMnNGaW5hbDogYjJzLmJsYWtlMnNGaW5hbFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/blakejs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/blakejs/util.js":
/*!**************************************!*\
  !*** ./node_modules/blakejs/util.js ***!
  \**************************************/
/***/ ((module) => {

eval("const ERROR_MSG_INPUT = 'Input must be an string, Buffer or Uint8Array'\n\n// For convenience, let people hash a string, not just a Uint8Array\nfunction normalizeInput (input) {\n  let ret\n  if (input instanceof Uint8Array) {\n    ret = input\n  } else if (typeof input === 'string') {\n    const encoder = new TextEncoder()\n    ret = encoder.encode(input)\n  } else {\n    throw new Error(ERROR_MSG_INPUT)\n  }\n  return ret\n}\n\n// Converts a Uint8Array to a hexadecimal string\n// For example, toHex([255, 0, 255]) returns \"ff00ff\"\nfunction toHex (bytes) {\n  return Array.prototype.map\n    .call(bytes, function (n) {\n      return (n < 16 ? '0' : '') + n.toString(16)\n    })\n    .join('')\n}\n\n// Converts any value in [0...2^32-1] to an 8-character hex string\nfunction uint32ToHex (val) {\n  return (0x100000000 + val).toString(16).substring(1)\n}\n\n// For debugging: prints out hash state in the same format as the RFC\n// sample computation exactly, so that you can diff\nfunction debugPrint (label, arr, size) {\n  let msg = '\\n' + label + ' = '\n  for (let i = 0; i < arr.length; i += 2) {\n    if (size === 32) {\n      msg += uint32ToHex(arr[i]).toUpperCase()\n      msg += ' '\n      msg += uint32ToHex(arr[i + 1]).toUpperCase()\n    } else if (size === 64) {\n      msg += uint32ToHex(arr[i + 1]).toUpperCase()\n      msg += uint32ToHex(arr[i]).toUpperCase()\n    } else throw new Error('Invalid size ' + size)\n    if (i % 6 === 4) {\n      msg += '\\n' + new Array(label.length + 4).join(' ')\n    } else if (i < arr.length - 2) {\n      msg += ' '\n    }\n  }\n  console.log(msg)\n}\n\n// For performance testing: generates N bytes of input, hashes M times\n// Measures and prints MB/second hash performance each time\nfunction testSpeed (hashFn, N, M) {\n  let startMs = new Date().getTime()\n\n  const input = new Uint8Array(N)\n  for (let i = 0; i < N; i++) {\n    input[i] = i % 256\n  }\n  const genMs = new Date().getTime()\n  console.log('Generated random input in ' + (genMs - startMs) + 'ms')\n  startMs = genMs\n\n  for (let i = 0; i < M; i++) {\n    const hashHex = hashFn(input)\n    const hashMs = new Date().getTime()\n    const ms = hashMs - startMs\n    startMs = hashMs\n    console.log('Hashed in ' + ms + 'ms: ' + hashHex.substring(0, 20) + '...')\n    console.log(\n      Math.round((N / (1 << 20) / (ms / 1000)) * 100) / 100 + ' MB PER SECOND'\n    )\n  }\n}\n\nmodule.exports = {\n  normalizeInput: normalizeInput,\n  toHex: toHex,\n  debugPrint: debugPrint,\n  testSpeed: testSpeed\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/blakejs/util.js\n");

/***/ })

};
;