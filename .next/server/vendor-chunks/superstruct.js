"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superstruct";
exports.ids = ["vendor-chunks/superstruct"];
exports.modules = {

/***/ "(ssr)/./node_modules/superstruct/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/superstruct/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Struct: () => (/* binding */ Struct),\n/* harmony export */   StructError: () => (/* binding */ StructError),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   bigint: () => (/* binding */ bigint),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   defaulted: () => (/* binding */ defaulted),\n/* harmony export */   define: () => (/* binding */ define),\n/* harmony export */   deprecated: () => (/* binding */ deprecated),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   enums: () => (/* binding */ enums),\n/* harmony export */   func: () => (/* binding */ func),\n/* harmony export */   instance: () => (/* binding */ instance),\n/* harmony export */   integer: () => (/* binding */ integer),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   literal: () => (/* binding */ literal),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   mask: () => (/* binding */ mask),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   nonempty: () => (/* binding */ nonempty),\n/* harmony export */   nullable: () => (/* binding */ nullable),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   omit: () => (/* binding */ omit),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   partial: () => (/* binding */ partial),\n/* harmony export */   pattern: () => (/* binding */ pattern),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   record: () => (/* binding */ record),\n/* harmony export */   refine: () => (/* binding */ refine),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   struct: () => (/* binding */ struct),\n/* harmony export */   trimmed: () => (/* binding */ trimmed),\n/* harmony export */   tuple: () => (/* binding */ tuple),\n/* harmony export */   type: () => (/* binding */ type),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unknown: () => (/* binding */ unknown),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\nclass StructError extends TypeError {\n    constructor(failure, failures) {\n        let cached;\n        const { message, explanation, ...rest } = failure;\n        const { path } = failure;\n        const msg = path.length === 0 ? message : `At path: ${path.join('.')} -- ${message}`;\n        super(explanation ?? msg);\n        if (explanation != null)\n            this.cause = msg;\n        Object.assign(this, rest);\n        this.name = this.constructor.name;\n        this.failures = () => {\n            return (cached ?? (cached = [failure, ...failures()]));\n        };\n    }\n}\n\n/**\n * Check if a value is an iterator.\n */\nfunction isIterable(x) {\n    return isObject(x) && typeof x[Symbol.iterator] === 'function';\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isObject(x) {\n    return typeof x === 'object' && x != null;\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isPlainObject(x) {\n    if (Object.prototype.toString.call(x) !== '[object Object]') {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(x);\n    return prototype === null || prototype === Object.prototype;\n}\n/**\n * Return a value as a printable string.\n */\nfunction print(value) {\n    if (typeof value === 'symbol') {\n        return value.toString();\n    }\n    return typeof value === 'string' ? JSON.stringify(value) : `${value}`;\n}\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\nfunction shiftIterator(input) {\n    const { done, value } = input.next();\n    return done ? undefined : value;\n}\n/**\n * Convert a single validation result to a failure.\n */\nfunction toFailure(result, context, struct, value) {\n    if (result === true) {\n        return;\n    }\n    else if (result === false) {\n        result = {};\n    }\n    else if (typeof result === 'string') {\n        result = { message: result };\n    }\n    const { path, branch } = context;\n    const { type } = struct;\n    const { refinement, message = `Expected a value of type \\`${type}\\`${refinement ? ` with refinement \\`${refinement}\\`` : ''}, but received: \\`${print(value)}\\``, } = result;\n    return {\n        value,\n        type,\n        refinement,\n        key: path[path.length - 1],\n        path,\n        branch,\n        ...result,\n        message,\n    };\n}\n/**\n * Convert a validation result to an iterable of failures.\n */\nfunction* toFailures(result, context, struct, value) {\n    if (!isIterable(result)) {\n        result = [result];\n    }\n    for (const r of result) {\n        const failure = toFailure(r, context, struct, value);\n        if (failure) {\n            yield failure;\n        }\n    }\n}\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\nfunction* run(value, struct, options = {}) {\n    const { path = [], branch = [value], coerce = false, mask = false } = options;\n    const ctx = { path, branch };\n    if (coerce) {\n        value = struct.coercer(value, ctx);\n        if (mask &&\n            struct.type !== 'type' &&\n            isObject(struct.schema) &&\n            isObject(value) &&\n            !Array.isArray(value)) {\n            for (const key in value) {\n                if (struct.schema[key] === undefined) {\n                    delete value[key];\n                }\n            }\n        }\n    }\n    let status = 'valid';\n    for (const failure of struct.validator(value, ctx)) {\n        failure.explanation = options.message;\n        status = 'not_valid';\n        yield [failure, undefined];\n    }\n    for (let [k, v, s] of struct.entries(value, ctx)) {\n        const ts = run(v, s, {\n            path: k === undefined ? path : [...path, k],\n            branch: k === undefined ? branch : [...branch, v],\n            coerce,\n            mask,\n            message: options.message,\n        });\n        for (const t of ts) {\n            if (t[0]) {\n                status = t[0].refinement != null ? 'not_refined' : 'not_valid';\n                yield [t[0], undefined];\n            }\n            else if (coerce) {\n                v = t[1];\n                if (k === undefined) {\n                    value = v;\n                }\n                else if (value instanceof Map) {\n                    value.set(k, v);\n                }\n                else if (value instanceof Set) {\n                    value.add(v);\n                }\n                else if (isObject(value)) {\n                    if (v !== undefined || k in value)\n                        value[k] = v;\n                }\n            }\n        }\n    }\n    if (status !== 'not_valid') {\n        for (const failure of struct.refiner(value, ctx)) {\n            failure.explanation = options.message;\n            status = 'not_refined';\n            yield [failure, undefined];\n        }\n    }\n    if (status === 'valid') {\n        yield [undefined, value];\n    }\n}\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\nclass Struct {\n    constructor(props) {\n        const { type, schema, validator, refiner, coercer = (value) => value, entries = function* () { }, } = props;\n        this.type = type;\n        this.schema = schema;\n        this.entries = entries;\n        this.coercer = coercer;\n        if (validator) {\n            this.validator = (value, context) => {\n                const result = validator(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.validator = () => [];\n        }\n        if (refiner) {\n            this.refiner = (value, context) => {\n                const result = refiner(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.refiner = () => [];\n        }\n    }\n    /**\n     * Assert that a value passes the struct's validation, throwing if it doesn't.\n     */\n    assert(value, message) {\n        return assert(value, this, message);\n    }\n    /**\n     * Create a value with the struct's coercion logic, then validate it.\n     */\n    create(value, message) {\n        return create(value, this, message);\n    }\n    /**\n     * Check if a value passes the struct's validation.\n     */\n    is(value) {\n        return is(value, this);\n    }\n    /**\n     * Mask a value, coercing and validating it, but returning only the subset of\n     * properties defined by the struct's schema.\n     */\n    mask(value, message) {\n        return mask(value, this, message);\n    }\n    /**\n     * Validate a value with the struct's validation logic, returning a tuple\n     * representing the result.\n     *\n     * You may optionally pass `true` for the `withCoercion` argument to coerce\n     * the value before attempting to validate it. If you do, the result will\n     * contain the coerced result when successful.\n     */\n    validate(value, options = {}) {\n        return validate(value, this, options);\n    }\n}\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\nfunction assert(value, struct, message) {\n    const result = validate(value, struct, { message });\n    if (result[0]) {\n        throw result[0];\n    }\n}\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\nfunction create(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\nfunction mask(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, mask: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Check if a value passes a struct.\n */\nfunction is(value, struct) {\n    const result = validate(value, struct);\n    return !result[0];\n}\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\nfunction validate(value, struct, options = {}) {\n    const tuples = run(value, struct, options);\n    const tuple = shiftIterator(tuples);\n    if (tuple[0]) {\n        const error = new StructError(tuple[0], function* () {\n            for (const t of tuples) {\n                if (t[0]) {\n                    yield t[0];\n                }\n            }\n        });\n        return [error, undefined];\n    }\n    else {\n        const v = tuple[1];\n        return [undefined, v];\n    }\n}\n\nfunction assign(...Structs) {\n    const isType = Structs[0].type === 'type';\n    const schemas = Structs.map((s) => s.schema);\n    const schema = Object.assign({}, ...schemas);\n    return isType ? type(schema) : object(schema);\n}\n/**\n * Define a new struct type with a custom validation function.\n */\nfunction define(name, validator) {\n    return new Struct({ type: name, schema: null, validator });\n}\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\nfunction deprecated(struct, log) {\n    return new Struct({\n        ...struct,\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n        validator(value, ctx) {\n            if (value === undefined) {\n                return true;\n            }\n            else {\n                log(value, ctx);\n                return struct.validator(value, ctx);\n            }\n        },\n    });\n}\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\nfunction dynamic(fn) {\n    return new Struct({\n        type: 'dynamic',\n        schema: null,\n        *entries(value, ctx) {\n            const struct = fn(value, ctx);\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\nfunction lazy(fn) {\n    let struct;\n    return new Struct({\n        type: 'lazy',\n        schema: null,\n        *entries(value, ctx) {\n            struct ?? (struct = fn());\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\nfunction omit(struct, keys) {\n    const { schema } = struct;\n    const subschema = { ...schema };\n    for (const key of keys) {\n        delete subschema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\nfunction partial(struct) {\n    const isStruct = struct instanceof Struct;\n    const schema = isStruct ? { ...struct.schema } : { ...struct };\n    for (const key in schema) {\n        schema[key] = optional(schema[key]);\n    }\n    if (isStruct && struct.type === 'type') {\n        return type(schema);\n    }\n    return object(schema);\n}\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\nfunction pick(struct, keys) {\n    const { schema } = struct;\n    const subschema = {};\n    for (const key of keys) {\n        subschema[key] = schema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\nfunction struct(name, validator) {\n    console.warn('superstruct@0.11 - The `struct` helper has been renamed to `define`.');\n    return define(name, validator);\n}\n\n/**\n * Ensure that any value passes validation.\n */\nfunction any() {\n    return define('any', () => true);\n}\nfunction array(Element) {\n    return new Struct({\n        type: 'array',\n        schema: Element,\n        *entries(value) {\n            if (Element && Array.isArray(value)) {\n                for (const [i, v] of value.entries()) {\n                    yield [i, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return Array.isArray(value) ? value.slice() : value;\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array value, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a bigint.\n */\nfunction bigint() {\n    return define('bigint', (value) => {\n        return typeof value === 'bigint';\n    });\n}\n/**\n * Ensure that a value is a boolean.\n */\nfunction boolean() {\n    return define('boolean', (value) => {\n        return typeof value === 'boolean';\n    });\n}\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\nfunction date() {\n    return define('date', (value) => {\n        return ((value instanceof Date && !isNaN(value.getTime())) ||\n            `Expected a valid \\`Date\\` object, but received: ${print(value)}`);\n    });\n}\nfunction enums(values) {\n    const schema = {};\n    const description = values.map((v) => print(v)).join();\n    for (const key of values) {\n        schema[key] = key;\n    }\n    return new Struct({\n        type: 'enums',\n        schema,\n        validator(value) {\n            return (values.includes(value) ||\n                `Expected one of \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a function.\n */\nfunction func() {\n    return define('func', (value) => {\n        return (typeof value === 'function' ||\n            `Expected a function, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an instance of a specific class.\n */\nfunction instance(Class) {\n    return define('instance', (value) => {\n        return (value instanceof Class ||\n            `Expected a \\`${Class.name}\\` instance, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an integer.\n */\nfunction integer() {\n    return define('integer', (value) => {\n        return ((typeof value === 'number' && !isNaN(value) && Number.isInteger(value)) ||\n            `Expected an integer, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value matches all of a set of types.\n */\nfunction intersection(Structs) {\n    return new Struct({\n        type: 'intersection',\n        schema: null,\n        *entries(value, ctx) {\n            for (const S of Structs) {\n                yield* S.entries(value, ctx);\n            }\n        },\n        *validator(value, ctx) {\n            for (const S of Structs) {\n                yield* S.validator(value, ctx);\n            }\n        },\n        *refiner(value, ctx) {\n            for (const S of Structs) {\n                yield* S.refiner(value, ctx);\n            }\n        },\n    });\n}\nfunction literal(constant) {\n    const description = print(constant);\n    const t = typeof constant;\n    return new Struct({\n        type: 'literal',\n        schema: t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n        validator(value) {\n            return (value === constant ||\n                `Expected the literal \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\nfunction map(Key, Value) {\n    return new Struct({\n        type: 'map',\n        schema: null,\n        *entries(value) {\n            if (Key && Value && value instanceof Map) {\n                for (const [k, v] of value.entries()) {\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Map ? new Map(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Map ||\n                `Expected a \\`Map\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that no value ever passes validation.\n */\nfunction never() {\n    return define('never', () => false);\n}\n/**\n * Augment an existing struct to allow `null` values.\n */\nfunction nullable(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === null || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === null || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is a number.\n */\nfunction number() {\n    return define('number', (value) => {\n        return ((typeof value === 'number' && !isNaN(value)) ||\n            `Expected a number, but received: ${print(value)}`);\n    });\n}\nfunction object(schema) {\n    const knowns = schema ? Object.keys(schema) : [];\n    const Never = never();\n    return new Struct({\n        type: 'object',\n        schema: schema ? schema : null,\n        *entries(value) {\n            if (schema && isObject(value)) {\n                const unknowns = new Set(Object.keys(value));\n                for (const key of knowns) {\n                    unknowns.delete(key);\n                    yield [key, value[key], schema[key]];\n                }\n                for (const key of unknowns) {\n                    yield [key, value[key], Never];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Augment a struct to allow `undefined` values.\n */\nfunction optional(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === undefined || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\nfunction record(Key, Value) {\n    return new Struct({\n        type: 'record',\n        schema: null,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k in value) {\n                    const v = value[k];\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\nfunction regexp() {\n    return define('regexp', (value) => {\n        return value instanceof RegExp;\n    });\n}\nfunction set(Element) {\n    return new Struct({\n        type: 'set',\n        schema: null,\n        *entries(value) {\n            if (Element && value instanceof Set) {\n                for (const v of value) {\n                    yield [v, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Set ? new Set(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Set ||\n                `Expected a \\`Set\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a string.\n */\nfunction string() {\n    return define('string', (value) => {\n        return (typeof value === 'string' ||\n            `Expected a string, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\nfunction tuple(Structs) {\n    const Never = never();\n    return new Struct({\n        type: 'tuple',\n        schema: null,\n        *entries(value) {\n            if (Array.isArray(value)) {\n                const length = Math.max(Structs.length, value.length);\n                for (let i = 0; i < length; i++) {\n                    yield [i, value[i], Structs[i] || Never];\n                }\n            }\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\nfunction type(schema) {\n    const keys = Object.keys(schema);\n    return new Struct({\n        type: 'type',\n        schema,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k of keys) {\n                    yield [k, value[k], schema[k]];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Ensure that a value matches one of a set of types.\n */\nfunction union(Structs) {\n    const description = Structs.map((s) => s.type).join(' | ');\n    return new Struct({\n        type: 'union',\n        schema: null,\n        coercer(value) {\n            for (const S of Structs) {\n                const [error, coerced] = S.validate(value, { coerce: true });\n                if (!error) {\n                    return coerced;\n                }\n            }\n            return value;\n        },\n        validator(value, ctx) {\n            const failures = [];\n            for (const S of Structs) {\n                const [...tuples] = run(value, S, ctx);\n                const [first] = tuples;\n                if (!first[0]) {\n                    return [];\n                }\n                else {\n                    for (const [failure] of tuples) {\n                        if (failure) {\n                            failures.push(failure);\n                        }\n                    }\n                }\n            }\n            return [\n                `Expected the value to satisfy a union of \\`${description}\\`, but received: ${print(value)}`,\n                ...failures,\n            ];\n        },\n    });\n}\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\nfunction unknown() {\n    return define('unknown', () => true);\n}\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction coerce(struct, condition, coercer) {\n    return new Struct({\n        ...struct,\n        coercer: (value, ctx) => {\n            return is(value, condition)\n                ? struct.coercer(coercer(value, ctx), ctx)\n                : struct.coercer(value, ctx);\n        },\n    });\n}\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction defaulted(struct, fallback, options = {}) {\n    return coerce(struct, unknown(), (x) => {\n        const f = typeof fallback === 'function' ? fallback() : fallback;\n        if (x === undefined) {\n            return f;\n        }\n        if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n            const ret = { ...x };\n            let changed = false;\n            for (const key in f) {\n                if (ret[key] === undefined) {\n                    ret[key] = f[key];\n                    changed = true;\n                }\n            }\n            if (changed) {\n                return ret;\n            }\n        }\n        return x;\n    });\n}\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction trimmed(struct) {\n    return coerce(struct, string(), (x) => x.trim());\n}\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\nfunction empty(struct) {\n    return refine(struct, 'empty', (value) => {\n        const size = getSize(value);\n        return (size === 0 ||\n            `Expected an empty ${struct.type} but received one with a size of \\`${size}\\``);\n    });\n}\nfunction getSize(value) {\n    if (value instanceof Map || value instanceof Set) {\n        return value.size;\n    }\n    else {\n        return value.length;\n    }\n}\n/**\n * Ensure that a number or date is below a threshold.\n */\nfunction max(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'max', (value) => {\n        return exclusive\n            ? value < threshold\n            : value <= threshold ||\n                `Expected a ${struct.type} less than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a number or date is above a threshold.\n */\nfunction min(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'min', (value) => {\n        return exclusive\n            ? value > threshold\n            : value >= threshold ||\n                `Expected a ${struct.type} greater than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a string, array, map or set is not empty.\n */\nfunction nonempty(struct) {\n    return refine(struct, 'nonempty', (value) => {\n        const size = getSize(value);\n        return (size > 0 || `Expected a nonempty ${struct.type} but received an empty one`);\n    });\n}\n/**\n * Ensure that a string matches a regular expression.\n */\nfunction pattern(struct, regexp) {\n    return refine(struct, 'pattern', (value) => {\n        return (regexp.test(value) ||\n            `Expected a ${struct.type} matching \\`/${regexp.source}/\\` but received \"${value}\"`);\n    });\n}\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\nfunction size(struct, min, max = min) {\n    const expected = `Expected a ${struct.type}`;\n    const of = min === max ? `of \\`${min}\\`` : `between \\`${min}\\` and \\`${max}\\``;\n    return refine(struct, 'size', (value) => {\n        if (typeof value === 'number' || value instanceof Date) {\n            return ((min <= value && value <= max) ||\n                `${expected} ${of} but received \\`${value}\\``);\n        }\n        else if (value instanceof Map || value instanceof Set) {\n            const { size } = value;\n            return ((min <= size && size <= max) ||\n                `${expected} with a size ${of} but received one with a size of \\`${size}\\``);\n        }\n        else {\n            const { length } = value;\n            return ((min <= length && length <= max) ||\n                `${expected} with a length ${of} but received one with a length of \\`${length}\\``);\n        }\n    });\n}\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\nfunction refine(struct, name, refiner) {\n    return new Struct({\n        ...struct,\n        *refiner(value, ctx) {\n            yield* struct.refiner(value, ctx);\n            const result = refiner(value, ctx);\n            const failures = toFailures(result, ctx, struct, value);\n            for (const failure of failures) {\n                yield { ...failure, refinement: name };\n            }\n        },\n    });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superstruct/dist/index.mjs\n");

/***/ })

};
;