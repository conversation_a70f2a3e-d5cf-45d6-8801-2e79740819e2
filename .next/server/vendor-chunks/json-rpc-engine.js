"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-rpc-engine";
exports.ids = ["vendor-chunks/json-rpc-engine"];
exports.modules = {

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js":
/*!************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/JsonRpcEngine.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.JsonRpcEngine = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/index.js\"));\nconst eth_rpc_errors_1 = __webpack_require__(/*! eth-rpc-errors */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/index.js\");\n/**\n * A JSON-RPC request and response processor.\n * Give it a stack of middleware, pass it requests, and get back responses.\n */\nclass JsonRpcEngine extends safe_event_emitter_1.default {\n    constructor() {\n        super();\n        this._middleware = [];\n    }\n    /**\n     * Add a middleware function to the engine's middleware stack.\n     *\n     * @param middleware - The middleware function to add.\n     */\n    push(middleware) {\n        this._middleware.push(middleware);\n    }\n    handle(req, cb) {\n        if (cb && typeof cb !== 'function') {\n            throw new Error('\"callback\" must be a function if provided.');\n        }\n        if (Array.isArray(req)) {\n            if (cb) {\n                return this._handleBatch(req, cb);\n            }\n            return this._handleBatch(req);\n        }\n        if (cb) {\n            return this._handle(req, cb);\n        }\n        return this._promiseHandle(req);\n    }\n    /**\n     * Returns this engine as a middleware function that can be pushed to other\n     * engines.\n     *\n     * @returns This engine as a middleware function.\n     */\n    asMiddleware() {\n        return async (req, res, next, end) => {\n            try {\n                const [middlewareError, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n                if (isComplete) {\n                    await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    return end(middlewareError);\n                }\n                return next(async (handlerCallback) => {\n                    try {\n                        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    }\n                    catch (error) {\n                        return handlerCallback(error);\n                    }\n                    return handlerCallback();\n                });\n            }\n            catch (error) {\n                return end(error);\n            }\n        };\n    }\n    async _handleBatch(reqs, cb) {\n        // The order here is important\n        try {\n            // 2. Wait for all requests to finish, or throw on some kind of fatal\n            // error\n            const responses = await Promise.all(\n            // 1. Begin executing each request in the order received\n            reqs.map(this._promiseHandle.bind(this)));\n            // 3. Return batch response\n            if (cb) {\n                return cb(null, responses);\n            }\n            return responses;\n        }\n        catch (error) {\n            if (cb) {\n                return cb(error);\n            }\n            throw error;\n        }\n    }\n    /**\n     * A promise-wrapped _handle.\n     */\n    _promiseHandle(req) {\n        return new Promise((resolve) => {\n            this._handle(req, (_err, res) => {\n                // There will always be a response, and it will always have any error\n                // that is caught and propagated.\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Ensures that the request object is valid, processes it, and passes any\n     * error and the response object to the given callback.\n     *\n     * Does not reject.\n     */\n    async _handle(callerReq, cb) {\n        if (!callerReq ||\n            Array.isArray(callerReq) ||\n            typeof callerReq !== 'object') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Requests must be plain objects. Received: ${typeof callerReq}`, { request: callerReq });\n            return cb(error, { id: undefined, jsonrpc: '2.0', error });\n        }\n        if (typeof callerReq.method !== 'string') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Must specify a string method. Received: ${typeof callerReq.method}`, { request: callerReq });\n            return cb(error, { id: callerReq.id, jsonrpc: '2.0', error });\n        }\n        const req = Object.assign({}, callerReq);\n        const res = {\n            id: req.id,\n            jsonrpc: req.jsonrpc,\n        };\n        let error = null;\n        try {\n            await this._processRequest(req, res);\n        }\n        catch (_error) {\n            // A request handler error, a re-thrown middleware error, or something\n            // unexpected.\n            error = _error;\n        }\n        if (error) {\n            // Ensure no result is present on an errored response\n            delete res.result;\n            if (!res.error) {\n                res.error = eth_rpc_errors_1.serializeError(error);\n            }\n        }\n        return cb(error, res);\n    }\n    /**\n     * For the given request and response, runs all middleware and their return\n     * handlers, if any, and ensures that internal request processing semantics\n     * are satisfied.\n     */\n    async _processRequest(req, res) {\n        const [error, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n        // Throw if \"end\" was not called, or if the response has neither a result\n        // nor an error.\n        JsonRpcEngine._checkForCompletion(req, res, isComplete);\n        // The return handlers should run even if an error was encountered during\n        // middleware processing.\n        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n        // Now we re-throw the middleware processing error, if any, to catch it\n        // further up the call chain.\n        if (error) {\n            throw error;\n        }\n    }\n    /**\n     * Serially executes the given stack of middleware.\n     *\n     * @returns An array of any error encountered during middleware execution,\n     * a boolean indicating whether the request was completed, and an array of\n     * middleware-defined return handlers.\n     */\n    static async _runAllMiddleware(req, res, middlewareStack) {\n        const returnHandlers = [];\n        let error = null;\n        let isComplete = false;\n        // Go down stack of middleware, call and collect optional returnHandlers\n        for (const middleware of middlewareStack) {\n            [error, isComplete] = await JsonRpcEngine._runMiddleware(req, res, middleware, returnHandlers);\n            if (isComplete) {\n                break;\n            }\n        }\n        return [error, isComplete, returnHandlers.reverse()];\n    }\n    /**\n     * Runs an individual middleware.\n     *\n     * @returns An array of any error encountered during middleware exection,\n     * and a boolean indicating whether the request should end.\n     */\n    static _runMiddleware(req, res, middleware, returnHandlers) {\n        return new Promise((resolve) => {\n            const end = (err) => {\n                const error = err || res.error;\n                if (error) {\n                    res.error = eth_rpc_errors_1.serializeError(error);\n                }\n                // True indicates that the request should end\n                resolve([error, true]);\n            };\n            const next = (returnHandler) => {\n                if (res.error) {\n                    end(res.error);\n                }\n                else {\n                    if (returnHandler) {\n                        if (typeof returnHandler !== 'function') {\n                            end(new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: \"next\" return handlers must be functions. ` +\n                                `Received \"${typeof returnHandler}\" for request:\\n${jsonify(req)}`, { request: req }));\n                        }\n                        returnHandlers.push(returnHandler);\n                    }\n                    // False indicates that the request should not end\n                    resolve([null, false]);\n                }\n            };\n            try {\n                middleware(req, res, next, end);\n            }\n            catch (error) {\n                end(error);\n            }\n        });\n    }\n    /**\n     * Serially executes array of return handlers. The request and response are\n     * assumed to be in their scope.\n     */\n    static async _runReturnHandlers(handlers) {\n        for (const handler of handlers) {\n            await new Promise((resolve, reject) => {\n                handler((err) => (err ? reject(err) : resolve()));\n            });\n        }\n    }\n    /**\n     * Throws an error if the response has neither a result nor an error, or if\n     * the \"isComplete\" flag is falsy.\n     */\n    static _checkForCompletion(req, res, isComplete) {\n        if (!('result' in res) && !('error' in res)) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Response has no error or result for request:\\n${jsonify(req)}`, { request: req });\n        }\n        if (!isComplete) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Nothing ended request:\\n${jsonify(req)}`, { request: req });\n        }\n    }\n}\nexports.JsonRpcEngine = JsonRpcEngine;\nfunction jsonify(request) {\n    return JSON.stringify(request, null, 2);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js":
/*!********************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createAsyncMiddleware = void 0;\n/**\n * JsonRpcEngine only accepts callback-based middleware directly.\n * createAsyncMiddleware exists to enable consumers to pass in async middleware\n * functions.\n *\n * Async middleware have no \"end\" function. Instead, they \"end\" if they return\n * without calling \"next\". Rather than passing in explicit return handlers,\n * async middleware can simply await \"next\", and perform operations on the\n * response object when execution resumes.\n *\n * To accomplish this, createAsyncMiddleware passes the async middleware a\n * wrapped \"next\" function. That function calls the internal JsonRpcEngine\n * \"next\" function with a return handler that resolves a promise when called.\n *\n * The return handler will always be called. Its resolution of the promise\n * enables the control flow described above.\n */\nfunction createAsyncMiddleware(asyncMiddleware) {\n    return async (req, res, next, end) => {\n        // nextPromise is the key to the implementation\n        // it is resolved by the return handler passed to the\n        // \"next\" function\n        let resolveNextPromise;\n        const nextPromise = new Promise((resolve) => {\n            resolveNextPromise = resolve;\n        });\n        let returnHandlerCallback = null;\n        let nextWasCalled = false;\n        // This will be called by the consumer's async middleware.\n        const asyncNext = async () => {\n            nextWasCalled = true;\n            // We pass a return handler to next(). When it is called by the engine,\n            // the consumer's async middleware will resume executing.\n            // eslint-disable-next-line node/callback-return\n            next((runReturnHandlersCallback) => {\n                // This callback comes from JsonRpcEngine._runReturnHandlers\n                returnHandlerCallback = runReturnHandlersCallback;\n                resolveNextPromise();\n            });\n            await nextPromise;\n        };\n        try {\n            await asyncMiddleware(req, res, asyncNext);\n            if (nextWasCalled) {\n                await nextPromise; // we must wait until the return handler is called\n                returnHandlerCallback(null);\n            }\n            else {\n                end(null);\n            }\n        }\n        catch (error) {\n            if (returnHandlerCallback) {\n                returnHandlerCallback(error);\n            }\n            else {\n                end(error);\n            }\n        }\n    };\n}\nexports.createAsyncMiddleware = createAsyncMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlQXN5bmNNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZUFzeW5jTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFnQkE7Ozs7Ozs7Ozs7Ozs7Ozs7R0FnQkc7QUFDSCxTQUFnQixxQkFBcUIsQ0FDbkMsZUFBNkM7SUFFN0MsT0FBTyxLQUFLLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLEVBQUU7UUFDbkMsK0NBQStDO1FBQy9DLHFEQUFxRDtRQUNyRCxrQkFBa0I7UUFDbEIsSUFBSSxrQkFBOEIsQ0FBQztRQUNuQyxNQUFNLFdBQVcsR0FBRyxJQUFJLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxFQUFFO1lBQzFDLGtCQUFrQixHQUFHLE9BQU8sQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILElBQUkscUJBQXFCLEdBQVksSUFBSSxDQUFDO1FBQzFDLElBQUksYUFBYSxHQUFHLEtBQUssQ0FBQztRQUUxQiwwREFBMEQ7UUFDMUQsTUFBTSxTQUFTLEdBQUcsS0FBSyxJQUFJLEVBQUU7WUFDM0IsYUFBYSxHQUFHLElBQUksQ0FBQztZQUVyQix1RUFBdUU7WUFDdkUseURBQXlEO1lBQ3pELGdEQUFnRDtZQUNoRCxJQUFJLENBQUMsQ0FBQyx5QkFBeUIsRUFBRSxFQUFFO2dCQUNqQyw0REFBNEQ7Z0JBQzVELHFCQUFxQixHQUFHLHlCQUF5QixDQUFDO2dCQUNsRCxrQkFBa0IsRUFBRSxDQUFDO1lBQ3ZCLENBQUMsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxXQUFXLENBQUM7UUFDcEIsQ0FBQyxDQUFDO1FBRUYsSUFBSTtZQUNGLE1BQU0sZUFBZSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFFM0MsSUFBSSxhQUFhLEVBQUU7Z0JBQ2pCLE1BQU0sV0FBVyxDQUFDLENBQUMsa0RBQWtEO2dCQUNwRSxxQkFBK0MsQ0FBQyxJQUFJLENBQUMsQ0FBQzthQUN4RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDWDtTQUNGO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDZCxJQUFJLHFCQUFxQixFQUFFO2dCQUN4QixxQkFBK0MsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN6RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7YUFDWjtTQUNGO0lBQ0gsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQS9DRCxzREErQ0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js":
/*!***********************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createScaffoldMiddleware = void 0;\nfunction createScaffoldMiddleware(handlers) {\n    return (req, res, next, end) => {\n        const handler = handlers[req.method];\n        // if no handler, return\n        if (handler === undefined) {\n            return next();\n        }\n        // if handler is fn, call as middleware\n        if (typeof handler === 'function') {\n            return handler(req, res, next, end);\n        }\n        // if handler is some other value, use as result\n        res.result = handler;\n        return end();\n    };\n}\nexports.createScaffoldMiddleware = createScaffoldMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlU2NhZmZvbGRNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZVNjYWZmb2xkTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFJQSxTQUFnQix3QkFBd0IsQ0FBQyxRQUV4QztJQUNDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsRUFBRTtRQUM3QixNQUFNLE9BQU8sR0FBRyxRQUFRLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ3JDLHdCQUF3QjtRQUN4QixJQUFJLE9BQU8sS0FBSyxTQUFTLEVBQUU7WUFDekIsT0FBTyxJQUFJLEVBQUUsQ0FBQztTQUNmO1FBQ0QsdUNBQXVDO1FBQ3ZDLElBQUksT0FBTyxPQUFPLEtBQUssVUFBVSxFQUFFO1lBQ2pDLE9BQU8sT0FBTyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQ3JDO1FBQ0QsZ0RBQWdEO1FBQy9DLEdBQStCLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQztRQUNsRCxPQUFPLEdBQUcsRUFBRSxDQUFDO0lBQ2YsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQWpCRCw0REFpQkMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js":
/*!**********************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/getUniqueId.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getUniqueId = void 0;\n// uint32 (two's complement) max\n// more conservative than Number.MAX_SAFE_INTEGER\nconst MAX = 4294967295;\nlet idCounter = Math.floor(Math.random() * MAX);\nfunction getUniqueId() {\n    idCounter = (idCounter + 1) % MAX;\n    return idCounter;\n}\nexports.getUniqueId = getUniqueId;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ2V0VW5pcXVlSWQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvZ2V0VW5pcXVlSWQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsZ0NBQWdDO0FBQ2hDLGlEQUFpRDtBQUNqRCxNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7QUFDdkIsSUFBSSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUM7QUFFaEQsU0FBZ0IsV0FBVztJQUN6QixTQUFTLEdBQUcsQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ2xDLE9BQU8sU0FBUyxDQUFDO0FBQ25CLENBQUM7QUFIRCxrQ0FHQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvZ2V0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkIsMkNBQTJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvZ2V0VW5pcXVlSWQuanM/ZmQwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZ2V0VW5pcXVlSWQgPSB2b2lkIDA7XG4vLyB1aW50MzIgKHR3bydzIGNvbXBsZW1lbnQpIG1heFxuLy8gbW9yZSBjb25zZXJ2YXRpdmUgdGhhbiBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUlxuY29uc3QgTUFYID0gNDI5NDk2NzI5NTtcbmxldCBpZENvdW50ZXIgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBNQVgpO1xuZnVuY3Rpb24gZ2V0VW5pcXVlSWQoKSB7XG4gICAgaWRDb3VudGVyID0gKGlkQ291bnRlciArIDEpICUgTUFYO1xuICAgIHJldHVybiBpZENvdW50ZXI7XG59XG5leHBvcnRzLmdldFVuaXF1ZUlkID0gZ2V0VW5pcXVlSWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kYXRhOmFwcGxpY2F0aW9uL2pzb247YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0ptYVd4bElqb2laMlYwVlc1cGNYVmxTV1F1YW5NaUxDSnpiM1Z5WTJWU2IyOTBJam9pSWl3aWMyOTFjbU5sY3lJNld5SXVMaTl6Y21NdloyVjBWVzVwY1hWbFNXUXVkSE1pWFN3aWJtRnRaWE1pT2x0ZExDSnRZWEJ3YVc1bmN5STZJanM3TzBGQlFVRXNaME5CUVdkRE8wRkJRMmhETEdsRVFVRnBSRHRCUVVOcVJDeE5RVUZOTEVkQlFVY3NSMEZCUnl4VlFVRlZMRU5CUVVNN1FVRkRka0lzU1VGQlNTeFRRVUZUTEVkQlFVY3NTVUZCU1N4RFFVRkRMRXRCUVVzc1EwRkJReXhKUVVGSkxFTkJRVU1zVFVGQlRTeEZRVUZGTEVkQlFVY3NSMEZCUnl4RFFVRkRMRU5CUVVNN1FVRkZhRVFzVTBGQlowSXNWMEZCVnp0SlFVTjZRaXhUUVVGVExFZEJRVWNzUTBGQlF5eFRRVUZUTEVkQlFVY3NRMEZCUXl4RFFVRkRMRWRCUVVjc1IwRkJSeXhEUVVGRE8wbEJRMnhETEU5QlFVOHNVMEZCVXl4RFFVRkRPMEZCUTI1Q0xFTkJRVU03UVVGSVJDeHJRMEZIUXlKOSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js":
/*!****************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/idRemapMiddleware.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createIdRemapMiddleware = void 0;\nconst getUniqueId_1 = __webpack_require__(/*! ./getUniqueId */ \"(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\");\nfunction createIdRemapMiddleware() {\n    return (req, res, next, _end) => {\n        const originalId = req.id;\n        const newId = getUniqueId_1.getUniqueId();\n        req.id = newId;\n        res.id = newId;\n        next((done) => {\n            req.id = originalId;\n            res.id = originalId;\n            done();\n        });\n    };\n}\nexports.createIdRemapMiddleware = createIdRemapMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaWRSZW1hcE1pZGRsZXdhcmUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaWRSZW1hcE1pZGRsZXdhcmUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsK0NBQTRDO0FBRzVDLFNBQWdCLHVCQUF1QjtJQUNyQyxPQUFPLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLEVBQUU7UUFDOUIsTUFBTSxVQUFVLEdBQUcsR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUMxQixNQUFNLEtBQUssR0FBRyx5QkFBVyxFQUFFLENBQUM7UUFDNUIsR0FBRyxDQUFDLEVBQUUsR0FBRyxLQUFLLENBQUM7UUFDZixHQUFHLENBQUMsRUFBRSxHQUFHLEtBQUssQ0FBQztRQUNmLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ1osR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsSUFBSSxFQUFFLENBQUM7UUFDVCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQztBQUNKLENBQUM7QUFaRCwwREFZQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./idRemapMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createAsyncMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createScaffoldMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./getUniqueId */ \"(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\"), exports);\n__exportStar(__webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\"), exports);\n__exportStar(__webpack_require__(/*! ./mergeMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js\"), exports);\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsc0RBQW9DO0FBQ3BDLDBEQUF3QztBQUN4Qyw2REFBMkM7QUFDM0MsZ0RBQThCO0FBQzlCLGtEQUFnQztBQUNoQyxvREFBa0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0EsbUNBQW1DLG9DQUFvQyxnQkFBZ0I7QUFDdkYsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsbUJBQU8sQ0FBQywyRkFBcUI7QUFDMUMsYUFBYSxtQkFBTyxDQUFDLG1HQUF5QjtBQUM5QyxhQUFhLG1CQUFPLENBQUMseUdBQTRCO0FBQ2pELGFBQWEsbUJBQU8sQ0FBQywrRUFBZTtBQUNwQyxhQUFhLG1CQUFPLENBQUMsbUZBQWlCO0FBQ3RDLGFBQWEsbUJBQU8sQ0FBQyx1RkFBbUI7QUFDeEMsMkNBQTJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvaW5kZXguanM/MWYzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2lkUmVtYXBNaWRkbGV3YXJlXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9jcmVhdGVBc3luY01pZGRsZXdhcmVcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2NyZWF0ZVNjYWZmb2xkTWlkZGxld2FyZVwiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZ2V0VW5pcXVlSWRcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL0pzb25ScGNFbmdpbmVcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL21lcmdlTWlkZGxld2FyZVwiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kYXRhOmFwcGxpY2F0aW9uL2pzb247YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0ptYVd4bElqb2lhVzVrWlhndWFuTWlMQ0p6YjNWeVkyVlNiMjkwSWpvaUlpd2ljMjkxY21ObGN5STZXeUl1TGk5emNtTXZhVzVrWlhndWRITWlYU3dpYm1GdFpYTWlPbHRkTENKdFlYQndhVzVuY3lJNklqczdPenM3T3pzN096czdPMEZCUVVFc2MwUkJRVzlETzBGQlEzQkRMREJFUVVGM1F6dEJRVU40UXl3MlJFRkJNa003UVVGRE0wTXNaMFJCUVRoQ08wRkJRemxDTEd0RVFVRm5RenRCUVVOb1F5eHZSRUZCYTBNaWZRPT0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js":
/*!**************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/mergeMiddleware.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.mergeMiddleware = void 0;\nconst JsonRpcEngine_1 = __webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\");\nfunction mergeMiddleware(middlewareStack) {\n    const engine = new JsonRpcEngine_1.JsonRpcEngine();\n    middlewareStack.forEach((middleware) => engine.push(middleware));\n    return engine.asMiddleware();\n}\nexports.mergeMiddleware = mergeMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWVyZ2VNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL21lcmdlTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSxtREFBbUU7QUFFbkUsU0FBZ0IsZUFBZSxDQUFDLGVBQXNEO0lBQ3BGLE1BQU0sTUFBTSxHQUFHLElBQUksNkJBQWEsRUFBRSxDQUFDO0lBQ25DLGVBQWUsQ0FBQyxPQUFPLENBQUMsQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztJQUNqRSxPQUFPLE1BQU0sQ0FBQyxZQUFZLEVBQUUsQ0FBQztBQUMvQixDQUFDO0FBSkQsMENBSUMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvbWVyZ2VNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2Qix3QkFBd0IsbUJBQU8sQ0FBQyxtRkFBaUI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QiwyQ0FBMkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9qc29uLXJwYy1lbmdpbmUvZGlzdC9tZXJnZU1pZGRsZXdhcmUuanM/ODBmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubWVyZ2VNaWRkbGV3YXJlID0gdm9pZCAwO1xuY29uc3QgSnNvblJwY0VuZ2luZV8xID0gcmVxdWlyZShcIi4vSnNvblJwY0VuZ2luZVwiKTtcbmZ1bmN0aW9uIG1lcmdlTWlkZGxld2FyZShtaWRkbGV3YXJlU3RhY2spIHtcbiAgICBjb25zdCBlbmdpbmUgPSBuZXcgSnNvblJwY0VuZ2luZV8xLkpzb25ScGNFbmdpbmUoKTtcbiAgICBtaWRkbGV3YXJlU3RhY2suZm9yRWFjaCgobWlkZGxld2FyZSkgPT4gZW5naW5lLnB1c2gobWlkZGxld2FyZSkpO1xuICAgIHJldHVybiBlbmdpbmUuYXNNaWRkbGV3YXJlKCk7XG59XG5leHBvcnRzLm1lcmdlTWlkZGxld2FyZSA9IG1lcmdlTWlkZGxld2FyZTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRhdGE6YXBwbGljYXRpb24vanNvbjtiYXNlNjQsZXlKMlpYSnphVzl1SWpvekxDSm1hV3hsSWpvaWJXVnlaMlZOYVdSa2JHVjNZWEpsTG1weklpd2ljMjkxY21ObFVtOXZkQ0k2SWlJc0luTnZkWEpqWlhNaU9sc2lMaTR2YzNKakwyMWxjbWRsVFdsa1pHeGxkMkZ5WlM1MGN5SmRMQ0p1WVcxbGN5STZXMTBzSW0xaGNIQnBibWR6SWpvaU96czdRVUZCUVN4dFJFRkJiVVU3UVVGRmJrVXNVMEZCWjBJc1pVRkJaU3hEUVVGRExHVkJRWE5FTzBsQlEzQkdMRTFCUVUwc1RVRkJUU3hIUVVGSExFbEJRVWtzTmtKQlFXRXNSVUZCUlN4RFFVRkRPMGxCUTI1RExHVkJRV1VzUTBGQlF5eFBRVUZQTEVOQlFVTXNRMEZCUXl4VlFVRlZMRVZCUVVVc1JVRkJSU3hEUVVGRExFMUJRVTBzUTBGQlF5eEpRVUZKTEVOQlFVTXNWVUZCVlN4RFFVRkRMRU5CUVVNc1EwRkJRenRKUVVOcVJTeFBRVUZQTEUxQlFVMHNRMEZCUXl4WlFVRlpMRVZCUVVVc1EwRkJRenRCUVVNdlFpeERRVUZETzBGQlNrUXNNRU5CU1VNaWZRPT0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/classes.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/classes.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EthereumProviderError = exports.EthereumRpcError = void 0;\nconst fast_safe_stringify_1 = __webpack_require__(/*! fast-safe-stringify */ \"(ssr)/./node_modules/fast-safe-stringify/index.js\");\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors\n * per EIP-1474.\n * Permits any integer error code.\n */\nclass EthereumRpcError extends Error {\n    constructor(code, message, data) {\n        if (!Number.isInteger(code)) {\n            throw new Error('\"code\" must be an integer.');\n        }\n        if (!message || typeof message !== 'string') {\n            throw new Error('\"message\" must be a nonempty string.');\n        }\n        super(message);\n        this.code = code;\n        if (data !== undefined) {\n            this.data = data;\n        }\n    }\n    /**\n     * Returns a plain object with all public class properties.\n     */\n    serialize() {\n        const serialized = {\n            code: this.code,\n            message: this.message,\n        };\n        if (this.data !== undefined) {\n            serialized.data = this.data;\n        }\n        if (this.stack) {\n            serialized.stack = this.stack;\n        }\n        return serialized;\n    }\n    /**\n     * Return a string representation of the serialized error, omitting\n     * any circular references.\n     */\n    toString() {\n        return fast_safe_stringify_1.default(this.serialize(), stringifyReplacer, 2);\n    }\n}\nexports.EthereumRpcError = EthereumRpcError;\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n * Permits integer error codes in the [ 1000 <= 4999 ] range.\n */\nclass EthereumProviderError extends EthereumRpcError {\n    /**\n     * Create an Ethereum Provider JSON-RPC error.\n     * `code` must be an integer in the 1000 <= 4999 range.\n     */\n    constructor(code, message, data) {\n        if (!isValidEthProviderCode(code)) {\n            throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');\n        }\n        super(code, message, data);\n    }\n}\nexports.EthereumProviderError = EthereumProviderError;\n// Internal\nfunction isValidEthProviderCode(code) {\n    return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\nfunction stringifyReplacer(_, value) {\n    if (value === '[Circular]') {\n        return undefined;\n    }\n    return value;\n}\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL25vZGVfbW9kdWxlcy9ldGgtcnBjLWVycm9ycy9kaXN0L2NsYXNzZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsNkJBQTZCLEdBQUcsd0JBQXdCO0FBQ3hELDhCQUE4QixtQkFBTyxDQUFDLDhFQUFxQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL2pzb24tcnBjLWVuZ2luZS9ub2RlX21vZHVsZXMvZXRoLXJwYy1lcnJvcnMvZGlzdC9jbGFzc2VzLmpzP2ZiNTIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkV0aGVyZXVtUHJvdmlkZXJFcnJvciA9IGV4cG9ydHMuRXRoZXJldW1ScGNFcnJvciA9IHZvaWQgMDtcbmNvbnN0IGZhc3Rfc2FmZV9zdHJpbmdpZnlfMSA9IHJlcXVpcmUoXCJmYXN0LXNhZmUtc3RyaW5naWZ5XCIpO1xuLyoqXG4gKiBFcnJvciBzdWJjbGFzcyBpbXBsZW1lbnRpbmcgSlNPTiBSUEMgMi4wIGVycm9ycyBhbmQgRXRoZXJldW0gUlBDIGVycm9yc1xuICogcGVyIEVJUC0xNDc0LlxuICogUGVybWl0cyBhbnkgaW50ZWdlciBlcnJvciBjb2RlLlxuICovXG5jbGFzcyBFdGhlcmV1bVJwY0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKGNvZGUsIG1lc3NhZ2UsIGRhdGEpIHtcbiAgICAgICAgaWYgKCFOdW1iZXIuaXNJbnRlZ2VyKGNvZGUpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1wiY29kZVwiIG11c3QgYmUgYW4gaW50ZWdlci4nKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIW1lc3NhZ2UgfHwgdHlwZW9mIG1lc3NhZ2UgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1wibWVzc2FnZVwiIG11c3QgYmUgYSBub25lbXB0eSBzdHJpbmcuJyk7XG4gICAgICAgIH1cbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMuY29kZSA9IGNvZGU7XG4gICAgICAgIGlmIChkYXRhICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuZGF0YSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogUmV0dXJucyBhIHBsYWluIG9iamVjdCB3aXRoIGFsbCBwdWJsaWMgY2xhc3MgcHJvcGVydGllcy5cbiAgICAgKi9cbiAgICBzZXJpYWxpemUoKSB7XG4gICAgICAgIGNvbnN0IHNlcmlhbGl6ZWQgPSB7XG4gICAgICAgICAgICBjb2RlOiB0aGlzLmNvZGUsXG4gICAgICAgICAgICBtZXNzYWdlOiB0aGlzLm1lc3NhZ2UsXG4gICAgICAgIH07XG4gICAgICAgIGlmICh0aGlzLmRhdGEgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgc2VyaWFsaXplZC5kYXRhID0gdGhpcy5kYXRhO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLnN0YWNrKSB7XG4gICAgICAgICAgICBzZXJpYWxpemVkLnN0YWNrID0gdGhpcy5zdGFjaztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc2VyaWFsaXplZDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmV0dXJuIGEgc3RyaW5nIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBzZXJpYWxpemVkIGVycm9yLCBvbWl0dGluZ1xuICAgICAqIGFueSBjaXJjdWxhciByZWZlcmVuY2VzLlxuICAgICAqL1xuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICByZXR1cm4gZmFzdF9zYWZlX3N0cmluZ2lmeV8xLmRlZmF1bHQodGhpcy5zZXJpYWxpemUoKSwgc3RyaW5naWZ5UmVwbGFjZXIsIDIpO1xuICAgIH1cbn1cbmV4cG9ydHMuRXRoZXJldW1ScGNFcnJvciA9IEV0aGVyZXVtUnBjRXJyb3I7XG4vKipcbiAqIEVycm9yIHN1YmNsYXNzIGltcGxlbWVudGluZyBFdGhlcmV1bSBQcm92aWRlciBlcnJvcnMgcGVyIEVJUC0xMTkzLlxuICogUGVybWl0cyBpbnRlZ2VyIGVycm9yIGNvZGVzIGluIHRoZSBbIDEwMDAgPD0gNDk5OSBdIHJhbmdlLlxuICovXG5jbGFzcyBFdGhlcmV1bVByb3ZpZGVyRXJyb3IgZXh0ZW5kcyBFdGhlcmV1bVJwY0Vycm9yIHtcbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYW4gRXRoZXJldW0gUHJvdmlkZXIgSlNPTi1SUEMgZXJyb3IuXG4gICAgICogYGNvZGVgIG11c3QgYmUgYW4gaW50ZWdlciBpbiB0aGUgMTAwMCA8PSA0OTk5IHJhbmdlLlxuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKGNvZGUsIG1lc3NhZ2UsIGRhdGEpIHtcbiAgICAgICAgaWYgKCFpc1ZhbGlkRXRoUHJvdmlkZXJDb2RlKGNvZGUpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1wiY29kZVwiIG11c3QgYmUgYW4gaW50ZWdlciBzdWNoIHRoYXQ6IDEwMDAgPD0gY29kZSA8PSA0OTk5Jyk7XG4gICAgICAgIH1cbiAgICAgICAgc3VwZXIoY29kZSwgbWVzc2FnZSwgZGF0YSk7XG4gICAgfVxufVxuZXhwb3J0cy5FdGhlcmV1bVByb3ZpZGVyRXJyb3IgPSBFdGhlcmV1bVByb3ZpZGVyRXJyb3I7XG4vLyBJbnRlcm5hbFxuZnVuY3Rpb24gaXNWYWxpZEV0aFByb3ZpZGVyQ29kZShjb2RlKSB7XG4gICAgcmV0dXJuIE51bWJlci5pc0ludGVnZXIoY29kZSkgJiYgY29kZSA+PSAxMDAwICYmIGNvZGUgPD0gNDk5OTtcbn1cbmZ1bmN0aW9uIHN0cmluZ2lmeVJlcGxhY2VyKF8sIHZhbHVlKSB7XG4gICAgaWYgKHZhbHVlID09PSAnW0NpcmN1bGFyXScpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2Jhc2U2NCxleUoyWlhKemFXOXVJam96TENKbWFXeGxJam9pWTJ4aGMzTmxjeTVxY3lJc0luTnZkWEpqWlZKdmIzUWlPaUlpTENKemIzVnlZMlZ6SWpwYklpNHVMM055WXk5amJHRnpjMlZ6TG5SeklsMHNJbTVoYldWeklqcGJYU3dpYldGd2NHbHVaM01pT2lJN096dEJRVUZCTERaRVFVRm5SRHRCUVZOb1JEczdPenRIUVVsSE8wRkJRMGdzVFVGQllTeG5Ra0ZCYjBJc1UwRkJVU3hMUVVGTE8wbEJUVFZETEZsQlFWa3NTVUZCV1N4RlFVRkZMRTlCUVdVc1JVRkJSU3hKUVVGUk8xRkJSV3BFTEVsQlFVa3NRMEZCUXl4TlFVRk5MRU5CUVVNc1UwRkJVeXhEUVVGRExFbEJRVWtzUTBGQlF5eEZRVUZGTzFsQlF6TkNMRTFCUVUwc1NVRkJTU3hMUVVGTExFTkJRMklzTkVKQlFUUkNMRU5CUXpkQ0xFTkJRVU03VTBGRFNEdFJRVU5FTEVsQlFVa3NRMEZCUXl4UFFVRlBMRWxCUVVrc1QwRkJUeXhQUVVGUExFdEJRVXNzVVVGQlVTeEZRVUZGTzFsQlF6TkRMRTFCUVUwc1NVRkJTU3hMUVVGTExFTkJRMklzYzBOQlFYTkRMRU5CUTNaRExFTkJRVU03VTBGRFNEdFJRVVZFTEV0QlFVc3NRMEZCUXl4UFFVRlBMRU5CUVVNc1EwRkJRenRSUVVObUxFbEJRVWtzUTBGQlF5eEpRVUZKTEVkQlFVY3NTVUZCU1N4RFFVRkRPMUZCUTJwQ0xFbEJRVWtzU1VGQlNTeExRVUZMTEZOQlFWTXNSVUZCUlR0WlFVTjBRaXhKUVVGSkxFTkJRVU1zU1VGQlNTeEhRVUZITEVsQlFVa3NRMEZCUXp0VFFVTnNRanRKUVVOSUxFTkJRVU03U1VGRlJEczdUMEZGUnp0SlFVTklMRk5CUVZNN1VVRkRVQ3hOUVVGTkxGVkJRVlVzUjBGQkswSTdXVUZETjBNc1NVRkJTU3hGUVVGRkxFbEJRVWtzUTBGQlF5eEpRVUZKTzFsQlEyWXNUMEZCVHl4RlFVRkZMRWxCUVVrc1EwRkJReXhQUVVGUE8xTkJRM1JDTEVOQlFVTTdVVUZEUml4SlFVRkpMRWxCUVVrc1EwRkJReXhKUVVGSkxFdEJRVXNzVTBGQlV5eEZRVUZGTzFsQlF6TkNMRlZCUVZVc1EwRkJReXhKUVVGSkxFZEJRVWNzU1VGQlNTeERRVUZETEVsQlFVa3NRMEZCUXp0VFFVTTNRanRSUVVORUxFbEJRVWtzU1VGQlNTeERRVUZETEV0QlFVc3NSVUZCUlR0WlFVTmtMRlZCUVZVc1EwRkJReXhMUVVGTExFZEJRVWNzU1VGQlNTeERRVUZETEV0QlFVc3NRMEZCUXp0VFFVTXZRanRSUVVORUxFOUJRVThzVlVGQlZTeERRVUZETzBsQlEzQkNMRU5CUVVNN1NVRkZSRHM3TzA5QlIwYzdTVUZEU0N4UlFVRlJPMUZCUTA0c1QwRkJUeXcyUWtGQllTeERRVU5zUWl4SlFVRkpMRU5CUVVNc1UwRkJVeXhGUVVGRkxFVkJRMmhDTEdsQ1FVRnBRaXhGUVVOcVFpeERRVUZETEVOQlEwWXNRMEZCUXp0SlFVTktMRU5CUVVNN1EwRkRSanRCUVhSRVJDdzBRMEZ6UkVNN1FVRkZSRHM3TzBkQlIwYzdRVUZEU0N4TlFVRmhMSEZDUVVGNVFpeFRRVUZSTEdkQ1FVRnRRanRKUVVVdlJEczdPMDlCUjBjN1NVRkRTQ3haUVVGWkxFbEJRVmtzUlVGQlJTeFBRVUZsTEVWQlFVVXNTVUZCVVR0UlFVVnFSQ3hKUVVGSkxFTkJRVU1zYzBKQlFYTkNMRU5CUVVNc1NVRkJTU3hEUVVGRExFVkJRVVU3V1VGRGFrTXNUVUZCVFN4SlFVRkpMRXRCUVVzc1EwRkRZaXd5UkVGQk1rUXNRMEZETlVRc1EwRkJRenRUUVVOSU8xRkJSVVFzUzBGQlN5eERRVUZETEVsQlFVa3NSVUZCUlN4UFFVRlBMRVZCUVVVc1NVRkJTU3hEUVVGRExFTkJRVU03U1VGRE4wSXNRMEZCUXp0RFFVTkdPMEZCYUVKRUxITkVRV2RDUXp0QlFVVkVMRmRCUVZjN1FVRkZXQ3hUUVVGVExITkNRVUZ6UWl4RFFVRkRMRWxCUVZrN1NVRkRNVU1zVDBGQlR5eE5RVUZOTEVOQlFVTXNVMEZCVXl4RFFVRkRMRWxCUVVrc1EwRkJReXhKUVVGSkxFbEJRVWtzU1VGQlNTeEpRVUZKTEVsQlFVa3NTVUZCU1N4SlFVRkpMRWxCUVVrc1EwRkJRenRCUVVOb1JTeERRVUZETzBGQlJVUXNVMEZCVXl4cFFrRkJhVUlzUTBGQlF5eERRVUZWTEVWQlFVVXNTMEZCWXp0SlFVTnVSQ3hKUVVGSkxFdEJRVXNzUzBGQlN5eFpRVUZaTEVWQlFVVTdVVUZETVVJc1QwRkJUeXhUUVVGVExFTkJRVU03UzBGRGJFSTdTVUZEUkN4UFFVRlBMRXRCUVVzc1EwRkJRenRCUVVObUxFTkJRVU1pZlE9PSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/classes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/error-constants.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/error-constants.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.errorValues = exports.errorCodes = void 0;\nexports.errorCodes = {\n    rpc: {\n        invalidInput: -32000,\n        resourceNotFound: -32001,\n        resourceUnavailable: -32002,\n        transactionRejected: -32003,\n        methodNotSupported: -32004,\n        limitExceeded: -32005,\n        parse: -32700,\n        invalidRequest: -32600,\n        methodNotFound: -32601,\n        invalidParams: -32602,\n        internal: -32603,\n    },\n    provider: {\n        userRejectedRequest: 4001,\n        unauthorized: 4100,\n        unsupportedMethod: 4200,\n        disconnected: 4900,\n        chainDisconnected: 4901,\n    },\n};\nexports.errorValues = {\n    '-32700': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    },\n    '-32600': {\n        standard: 'JSON RPC 2.0',\n        message: 'The JSON sent is not a valid Request object.',\n    },\n    '-32601': {\n        standard: 'JSON RPC 2.0',\n        message: 'The method does not exist / is not available.',\n    },\n    '-32602': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid method parameter(s).',\n    },\n    '-32603': {\n        standard: 'JSON RPC 2.0',\n        message: 'Internal JSON-RPC error.',\n    },\n    '-32000': {\n        standard: 'EIP-1474',\n        message: 'Invalid input.',\n    },\n    '-32001': {\n        standard: 'EIP-1474',\n        message: 'Resource not found.',\n    },\n    '-32002': {\n        standard: 'EIP-1474',\n        message: 'Resource unavailable.',\n    },\n    '-32003': {\n        standard: 'EIP-1474',\n        message: 'Transaction rejected.',\n    },\n    '-32004': {\n        standard: 'EIP-1474',\n        message: 'Method not supported.',\n    },\n    '-32005': {\n        standard: 'EIP-1474',\n        message: 'Request limit exceeded.',\n    },\n    '4001': {\n        standard: 'EIP-1193',\n        message: 'User rejected the request.',\n    },\n    '4100': {\n        standard: 'EIP-1193',\n        message: 'The requested account and/or method has not been authorized by the user.',\n    },\n    '4200': {\n        standard: 'EIP-1193',\n        message: 'The requested method is not supported by this Ethereum provider.',\n    },\n    '4900': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from all chains.',\n    },\n    '4901': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from the specified chain.',\n    },\n};\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/error-constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/errors.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/errors.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ethErrors = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/classes.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/utils.js\");\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/error-constants.js\");\nexports.ethErrors = {\n    rpc: {\n        /**\n         * Get a JSON RPC 2.0 Parse (-32700) error.\n         */\n        parse: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.parse, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Request (-32600) error.\n         */\n        invalidRequest: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidRequest, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Params (-32602) error.\n         */\n        invalidParams: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidParams, arg),\n        /**\n         * Get a JSON RPC 2.0 Method Not Found (-32601) error.\n         */\n        methodNotFound: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotFound, arg),\n        /**\n         * Get a JSON RPC 2.0 Internal (-32603) error.\n         */\n        internal: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.internal, arg),\n        /**\n         * Get a JSON RPC 2.0 Server error.\n         * Permits integer error codes in the [ -32099 <= -32005 ] range.\n         * Codes -32000 through -32004 are reserved by EIP-1474.\n         */\n        server: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum RPC Server errors must provide single object argument.');\n            }\n            const { code } = opts;\n            if (!Number.isInteger(code) || code > -32005 || code < -32099) {\n                throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');\n            }\n            return getEthJsonRpcError(code, opts);\n        },\n        /**\n         * Get an Ethereum JSON RPC Invalid Input (-32000) error.\n         */\n        invalidInput: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidInput, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Not Found (-32001) error.\n         */\n        resourceNotFound: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceNotFound, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Unavailable (-32002) error.\n         */\n        resourceUnavailable: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceUnavailable, arg),\n        /**\n         * Get an Ethereum JSON RPC Transaction Rejected (-32003) error.\n         */\n        transactionRejected: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.transactionRejected, arg),\n        /**\n         * Get an Ethereum JSON RPC Method Not Supported (-32004) error.\n         */\n        methodNotSupported: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotSupported, arg),\n        /**\n         * Get an Ethereum JSON RPC Limit Exceeded (-32005) error.\n         */\n        limitExceeded: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.limitExceeded, arg),\n    },\n    provider: {\n        /**\n         * Get an Ethereum Provider User Rejected Request (4001) error.\n         */\n        userRejectedRequest: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.userRejectedRequest, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unauthorized (4100) error.\n         */\n        unauthorized: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.unauthorized, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unsupported Method (4200) error.\n         */\n        unsupportedMethod: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.unsupportedMethod, arg);\n        },\n        /**\n         * Get an Ethereum Provider Not Connected (4900) error.\n         */\n        disconnected: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.disconnected, arg);\n        },\n        /**\n         * Get an Ethereum Provider Chain Not Connected (4901) error.\n         */\n        chainDisconnected: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.chainDisconnected, arg);\n        },\n        /**\n         * Get a custom Ethereum Provider error.\n         */\n        custom: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum Provider custom errors must provide single object argument.');\n            }\n            const { code, message, data } = opts;\n            if (!message || typeof message !== 'string') {\n                throw new Error('\"message\" must be a nonempty string');\n            }\n            return new classes_1.EthereumProviderError(code, message, data);\n        },\n    },\n};\n// Internal\nfunction getEthJsonRpcError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumRpcError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction getEthProviderError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumProviderError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction parseOpts(arg) {\n    if (arg) {\n        if (typeof arg === 'string') {\n            return [arg];\n        }\n        else if (typeof arg === 'object' && !Array.isArray(arg)) {\n            const { message, data } = arg;\n            if (message && typeof message !== 'string') {\n                throw new Error('Must specify string message.');\n            }\n            return [message || undefined, data];\n        }\n    }\n    return [];\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/index.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getMessageFromCode = exports.serializeError = exports.EthereumProviderError = exports.EthereumRpcError = exports.ethErrors = exports.errorCodes = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/classes.js\");\nObject.defineProperty(exports, \"EthereumRpcError\", ({ enumerable: true, get: function () { return classes_1.EthereumRpcError; } }));\nObject.defineProperty(exports, \"EthereumProviderError\", ({ enumerable: true, get: function () { return classes_1.EthereumProviderError; } }));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/utils.js\");\nObject.defineProperty(exports, \"serializeError\", ({ enumerable: true, get: function () { return utils_1.serializeError; } }));\nObject.defineProperty(exports, \"getMessageFromCode\", ({ enumerable: true, get: function () { return utils_1.getMessageFromCode; } }));\nconst errors_1 = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/errors.js\");\nObject.defineProperty(exports, \"ethErrors\", ({ enumerable: true, get: function () { return errors_1.ethErrors; } }));\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/error-constants.js\");\nObject.defineProperty(exports, \"errorCodes\", ({ enumerable: true, get: function () { return error_constants_1.errorCodes; } }));\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsdUNBQW9FO0FBVWxFLGlHQVZPLDBCQUFnQixPQVVQO0FBQ2hCLHNHQVh5QiwrQkFBcUIsT0FXekI7QUFWdkIsbUNBRWlCO0FBU2YsK0ZBVkEsc0JBQWMsT0FVQTtBQUNkLG1HQVhnQiwwQkFBa0IsT0FXaEI7QUFUcEIscUNBQXFDO0FBS25DLDBGQUxPLGtCQUFTLE9BS1A7QUFKWCx1REFBK0M7QUFHN0MsMkZBSE8sNEJBQVUsT0FHUCJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.serializeError = exports.isValidCode = exports.getMessageFromCode = exports.JSON_RPC_SERVER_ERROR_MESSAGE = void 0;\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/error-constants.js\");\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/classes.js\");\nconst FALLBACK_ERROR_CODE = error_constants_1.errorCodes.rpc.internal;\nconst FALLBACK_MESSAGE = 'Unspecified error message. This is a bug, please report it.';\nconst FALLBACK_ERROR = {\n    code: FALLBACK_ERROR_CODE,\n    message: getMessageFromCode(FALLBACK_ERROR_CODE),\n};\nexports.JSON_RPC_SERVER_ERROR_MESSAGE = 'Unspecified server error.';\n/**\n * Gets the message for a given code, or a fallback message if the code has\n * no corresponding message.\n */\nfunction getMessageFromCode(code, fallbackMessage = FALLBACK_MESSAGE) {\n    if (Number.isInteger(code)) {\n        const codeString = code.toString();\n        if (hasKey(error_constants_1.errorValues, codeString)) {\n            return error_constants_1.errorValues[codeString].message;\n        }\n        if (isJsonRpcServerError(code)) {\n            return exports.JSON_RPC_SERVER_ERROR_MESSAGE;\n        }\n    }\n    return fallbackMessage;\n}\nexports.getMessageFromCode = getMessageFromCode;\n/**\n * Returns whether the given code is valid.\n * A code is only valid if it has a message.\n */\nfunction isValidCode(code) {\n    if (!Number.isInteger(code)) {\n        return false;\n    }\n    const codeString = code.toString();\n    if (error_constants_1.errorValues[codeString]) {\n        return true;\n    }\n    if (isJsonRpcServerError(code)) {\n        return true;\n    }\n    return false;\n}\nexports.isValidCode = isValidCode;\n/**\n * Serializes the given error to an Ethereum JSON RPC-compatible error object.\n * Merely copies the given error's values if it is already compatible.\n * If the given error is not fully compatible, it will be preserved on the\n * returned object's data.originalError property.\n */\nfunction serializeError(error, { fallbackError = FALLBACK_ERROR, shouldIncludeStack = false, } = {}) {\n    var _a, _b;\n    if (!fallbackError ||\n        !Number.isInteger(fallbackError.code) ||\n        typeof fallbackError.message !== 'string') {\n        throw new Error('Must provide fallback error with integer number code and string message.');\n    }\n    if (error instanceof classes_1.EthereumRpcError) {\n        return error.serialize();\n    }\n    const serialized = {};\n    if (error &&\n        typeof error === 'object' &&\n        !Array.isArray(error) &&\n        hasKey(error, 'code') &&\n        isValidCode(error.code)) {\n        const _error = error;\n        serialized.code = _error.code;\n        if (_error.message && typeof _error.message === 'string') {\n            serialized.message = _error.message;\n            if (hasKey(_error, 'data')) {\n                serialized.data = _error.data;\n            }\n        }\n        else {\n            serialized.message = getMessageFromCode(serialized.code);\n            serialized.data = { originalError: assignOriginalError(error) };\n        }\n    }\n    else {\n        serialized.code = fallbackError.code;\n        const message = (_a = error) === null || _a === void 0 ? void 0 : _a.message;\n        serialized.message = (message && typeof message === 'string'\n            ? message\n            : fallbackError.message);\n        serialized.data = { originalError: assignOriginalError(error) };\n    }\n    const stack = (_b = error) === null || _b === void 0 ? void 0 : _b.stack;\n    if (shouldIncludeStack && error && stack && typeof stack === 'string') {\n        serialized.stack = stack;\n    }\n    return serialized;\n}\nexports.serializeError = serializeError;\n// Internal\nfunction isJsonRpcServerError(code) {\n    return code >= -32099 && code <= -32000;\n}\nfunction assignOriginalError(error) {\n    if (error && typeof error === 'object' && !Array.isArray(error)) {\n        return Object.assign({}, error);\n    }\n    return error;\n}\nfunction hasKey(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/eth-rpc-errors/dist/utils.js\n");

/***/ })

};
;