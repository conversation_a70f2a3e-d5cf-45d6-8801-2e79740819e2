"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/wagmi";
exports.ids = ["vendor-chunks/wagmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/wagmi/dist/esm/context.js":
/*!************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/context.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WagmiContext: () => (/* binding */ WagmiContext),\n/* harmony export */   WagmiProvider: () => (/* binding */ WagmiProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _hydrate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hydrate.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hydrate.js\");\n/* __next_internal_client_entry_do_not_use__ WagmiContext,WagmiProvider auto */ \n\nconst WagmiContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction WagmiProvider(parameters) {\n    const { children, config } = parameters;\n    const props = {\n        value: config\n    };\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_hydrate_js__WEBPACK_IMPORTED_MODULE_1__.Hydrate, parameters, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WagmiContext.Provider, props, children));\n} //# sourceMappingURL=context.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O2dGQUdvRDtBQUNkO0FBRS9CLE1BQU1HLDZCQUFlSCxvREFBQUEsQ0FFMUJJLFdBQVU7QUFRTixTQUFVQyxjQUNkQyxVQUF1RDtJQUV2RCxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsTUFBTSxFQUFFLEdBQUdGO0lBRTdCLE1BQU1HLFFBQVE7UUFBRUMsT0FBT0Y7SUFBTTtJQUM3QixxQkFBT1Asb0RBQUFBLENBQ0xDLGdEQUFBQSxFQUNBSSwwQkFDQUwsb0RBQUFBLENBQWNFLGFBQWFRLFFBQVEsRUFBRUYsT0FBT0Y7QUFFaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uLi8uLi9zcmMvY29udGV4dC50cz9iZjZmIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJjcmVhdGVFbGVtZW50IiwiSHlkcmF0ZSIsIldhZ21pQ29udGV4dCIsInVuZGVmaW5lZCIsIldhZ21pUHJvdmlkZXIiLCJwYXJhbWV0ZXJzIiwiY2hpbGRyZW4iLCJjb25maWciLCJwcm9wcyIsInZhbHVlIiwiUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/errors/base.js":
/*!****************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/errors/base.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/./node_modules/wagmi/dist/esm/utils/getVersion.js\");\n\n\nclass BaseError extends _wagmi_core__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiError'\n        });\n    }\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/react';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_1__.getVersion)();\n    }\n}\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vZXJyb3JzL2Jhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFEO0FBQ0Q7QUFDN0Msd0JBQXdCLGtEQUFTO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGdFQUFVO0FBQ3pCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2Vycm9ycy9iYXNlLmpzPzRjYjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIGFzIENvcmVFcnJvciB9IGZyb20gJ0B3YWdtaS9jb3JlJztcbmltcG9ydCB7IGdldFZlcnNpb24gfSBmcm9tICcuLi91dGlscy9nZXRWZXJzaW9uLmpzJztcbmV4cG9ydCBjbGFzcyBCYXNlRXJyb3IgZXh0ZW5kcyBDb3JlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnV2FnbWlFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGdldCBkb2NzQmFzZVVybCgpIHtcbiAgICAgICAgcmV0dXJuICdodHRwczovL3dhZ21pLnNoL3JlYWN0JztcbiAgICB9XG4gICAgZ2V0IHZlcnNpb24oKSB7XG4gICAgICAgIHJldHVybiBnZXRWZXJzaW9uKCk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YmFzZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/errors/context.js":
/*!*******************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/errors/context.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WagmiProviderNotFoundError: () => (/* binding */ WagmiProviderNotFoundError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/wagmi/dist/esm/errors/base.js\");\n\nclass WagmiProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('`useConfig` must be used within `WagmiProvider`.', {\n            docsPath: '/api/WagmiProvider',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiProviderNotFoundError'\n        });\n    }\n}\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vZXJyb3JzL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0M7QUFDL0IseUNBQXlDLCtDQUFTO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2Vycm9ycy9jb250ZXh0LmpzPzY0ZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBXYWdtaVByb3ZpZGVyTm90Rm91bmRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdgdXNlQ29uZmlnYCBtdXN0IGJlIHVzZWQgd2l0aGluIGBXYWdtaVByb3ZpZGVyYC4nLCB7XG4gICAgICAgICAgICBkb2NzUGF0aDogJy9hcGkvV2FnbWlQcm92aWRlcicsXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnV2FnbWlQcm92aWRlck5vdEZvdW5kRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/errors/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useChainId.js":
/*!*********************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useChainId.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChainId: () => (/* binding */ useChainId)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getChainId.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useChainId auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useChainId */ function useChainId(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)((onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchChainId)(config, {\n            onChange\n        }), ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getChainId)(config), ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getChainId)(config));\n} //# sourceMappingURL=useChainId.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlQ2hhaW5JZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztnRUFRb0I7QUFDd0I7QUFHRjtBQVExQyxtREFDTSxTQUFVSSxXQUNkQyxhQUEyQyxFQUFFO0lBRTdDLE1BQU1DLFNBQVNILHdEQUFBQSxDQUFVRTtJQUV6QixPQUFPSCwyREFBQUEsQ0FDTCxDQUFDSyxXQUFhTix5REFBQUEsQ0FBYUssUUFBUTtZQUFFQztRQUFRLElBQzdDLElBQU1QLHVEQUFBQSxDQUFXTSxTQUNqQixJQUFNTix1REFBQUEsQ0FBV007QUFFckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uLi8uLi8uLi9zcmMvaG9va3MvdXNlQ2hhaW5JZC50cz84YWNjIl0sIm5hbWVzIjpbImdldENoYWluSWQiLCJ3YXRjaENoYWluSWQiLCJ1c2VTeW5jRXh0ZXJuYWxTdG9yZSIsInVzZUNvbmZpZyIsInVzZUNoYWluSWQiLCJwYXJhbWV0ZXJzIiwiY29uZmlnIiwib25DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useChainId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js":
/*!********************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useConfig.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConfig: () => (/* binding */ useConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context.js */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _errors_context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors/context.js */ \"(ssr)/./node_modules/wagmi/dist/esm/errors/context.js\");\n/* __next_internal_client_entry_do_not_use__ useConfig auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useConfig */ function useConfig(parameters = {}) {\n    const config = parameters.config ?? (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.WagmiContext);\n    if (!config) throw new _errors_context_js__WEBPACK_IMPORTED_MODULE_2__.WagmiProviderNotFoundError();\n    return config;\n} //# sourceMappingURL=useConfig.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlQ29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7K0RBR2tDO0FBRVU7QUFDcUI7QUFRakUsa0RBQ00sU0FBVUcsVUFDZEMsYUFBMEMsRUFBRTtJQUU1QyxNQUFNQyxTQUFTRCxXQUFXQyxNQUFNLElBQUlMLGlEQUFBQSxDQUFXQyxxREFBQUE7SUFDL0MsSUFBSSxDQUFDSSxRQUFRLE1BQU0sSUFBSUgsMEVBQUFBO0lBQ3ZCLE9BQU9HO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uLi8uLi8uLi9zcmMvaG9va3MvdXNlQ29uZmlnLnRzP2VhNmUiXSwibmFtZXMiOlsidXNlQ29udGV4dCIsIldhZ21pQ29udGV4dCIsIldhZ21pUHJvdmlkZXJOb3RGb3VuZEVycm9yIiwidXNlQ29uZmlnIiwicGFyYW1ldGVycyIsImNvbmZpZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useConnections.js":
/*!*************************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useConnections.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnections: () => (/* binding */ useConnections)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useConnections auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useConnections */ function useConnections(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)((onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchConnections)(config, {\n            onChange\n        }), ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getConnections)(config), ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getConnections)(config));\n} //# sourceMappingURL=useConnections.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlQ29ubmVjdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7b0VBTW9CO0FBQ3dCO0FBR0Y7QUFNMUMsdURBQ00sU0FBVUksZUFDZEMsYUFBdUMsRUFBRTtJQUV6QyxNQUFNQyxTQUFTSCx3REFBQUEsQ0FBVUU7SUFFekIsT0FBT0gsMkRBQUFBLENBQ0wsQ0FBQ0ssV0FBYU4sNkRBQUFBLENBQWlCSyxRQUFRO1lBQUVDO1FBQVEsSUFDakQsSUFBTVAsMkRBQUFBLENBQWVNLFNBQ3JCLElBQU1OLDJEQUFBQSxDQUFlTTtBQUV6QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4uLy4uLy4uL3NyYy9ob29rcy91c2VDb25uZWN0aW9ucy50cz8yMDY4Il0sIm5hbWVzIjpbImdldENvbm5lY3Rpb25zIiwid2F0Y2hDb25uZWN0aW9ucyIsInVzZVN5bmNFeHRlcm5hbFN0b3JlIiwidXNlQ29uZmlnIiwidXNlQ29ubmVjdGlvbnMiLCJwYXJhbWV0ZXJzIiwiY29uZmlnIiwib25DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useConnections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useDisconnect.js":
/*!************************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useDisconnect.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDisconnect: () => (/* binding */ useDisconnect)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/disconnect.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* harmony import */ var _useConnections_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useConnections.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConnections.js\");\n/* __next_internal_client_entry_do_not_use__ useDisconnect auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useDisconnect */ function useDisconnect(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__.disconnectMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    return {\n        ...result,\n        connectors: (0,_useConnections_js__WEBPACK_IMPORTED_MODULE_3__.useConnections)({\n            config\n        }).map((connection)=>connection.connector),\n        disconnect: mutate,\n        disconnectAsync: mutateAsync\n    };\n} //# sourceMappingURL=useDisconnect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlRGlzY29ubmVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OzttRUFFbUQ7QUFTekI7QUFPZ0I7QUFDVTtBQTRCcEQsc0RBQ00sU0FBVUksY0FDZEMsYUFBK0MsRUFBRTtJQUVqRCxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHRDtJQUVyQixNQUFNRSxTQUFTTCx3REFBQUEsQ0FBVUc7SUFFekIsTUFBTUcsa0JBQWtCUCw0RUFBQUEsQ0FBMEJNO0lBQ2xELE1BQU0sRUFBRUUsTUFBTSxFQUFFQyxXQUFXLEVBQUUsR0FBR0MsUUFBUSxHQUFHWCxrRUFBQUEsQ0FBWTtRQUNyRCxHQUFHTSxRQUFRO1FBQ1gsR0FBR0UsZUFBZTs7SUFHcEIsT0FBTztRQUNMLEdBQUdHLE1BQU07UUFDVEMsWUFBWVQsa0VBQUFBLENBQWU7WUFBRUk7UUFBTSxHQUFJTSxHQUFHLENBQ3hDLENBQUNDLGFBQWVBLFdBQVdDLFNBQVM7UUFFdENDLFlBQVlQO1FBQ1pRLGlCQUFpQlA7O0FBRXJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi4vLi4vLi4vc3JjL2hvb2tzL3VzZURpc2Nvbm5lY3QudHM/MGQxYyJdLCJuYW1lcyI6WyJ1c2VNdXRhdGlvbiIsImRpc2Nvbm5lY3RNdXRhdGlvbk9wdGlvbnMiLCJ1c2VDb25maWciLCJ1c2VDb25uZWN0aW9ucyIsInVzZURpc2Nvbm5lY3QiLCJwYXJhbWV0ZXJzIiwibXV0YXRpb24iLCJjb25maWciLCJtdXRhdGlvbk9wdGlvbnMiLCJtdXRhdGUiLCJtdXRhdGVBc3luYyIsInJlc3VsdCIsImNvbm5lY3RvcnMiLCJtYXAiLCJjb25uZWN0aW9uIiwiY29ubmVjdG9yIiwiZGlzY29ubmVjdCIsImRpc2Nvbm5lY3RBc3luYyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useDisconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useReadContract.js":
/*!**************************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useReadContract.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useReadContract: () => (/* binding */ useReadContract)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/readContract.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/./node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useReadContract auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useReadContract */ function useReadContract(parameters = {}) {\n    const { abi, address, functionName, query = {} } = parameters;\n    // @ts-ignore\n    const code = parameters.code;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.readContractQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean((address || code) && abi && functionName && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled,\n        structuralSharing: query.structuralSharing ?? _wagmi_core_query__WEBPACK_IMPORTED_MODULE_4__.structuralSharing\n    });\n} //# sourceMappingURL=useReadContract.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlUmVhZENvbnRyYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztxRUFlMEI7QUFJMkM7QUFDekI7QUFDRjtBQXdDMUMsd0RBQ00sU0FBVUssZ0JBT2RDLGFBTUksRUFBUztJQUViLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxPQUFPLEVBQUVDLFlBQVksRUFBRUMsUUFBUSxFQUFFLEVBQUUsR0FBR0o7SUFDbkQsYUFBYTtJQUNiLE1BQU1LLE9BQU9MLFdBQVdLLElBQXVCO0lBRS9DLE1BQU1DLFNBQVNSLHdEQUFBQSxDQUFVRTtJQUN6QixNQUFNTyxVQUFVViwwREFBQUEsQ0FBVztRQUFFUztJQUFNO0lBRW5DLE1BQU1FLFVBQVVkLDJFQUFBQSxDQUNkWSxRQUNBO1FBQUUsR0FBSU4sVUFBa0I7UUFBRU8sU0FBU1AsV0FBV08sT0FBTyxJQUFJQTtJQUFPO0lBRWxFLE1BQU1FLFVBQVVDLFFBQ2QsQ0FBQ1IsV0FBV0csSUFBQSxLQUFTSixPQUFPRSxnQkFBaUJDLENBQUFBLE1BQU1LLE9BQU8sSUFBSTtJQUdoRSxPQUFPYix5REFBQUEsQ0FBUztRQUNkLEdBQUdRLEtBQUs7UUFDUixHQUFHSSxPQUFPO1FBQ1ZDO1FBQ0FkLG1CQUFtQlMsTUFBTVQsaUJBQWlCLElBQUlBLGdFQUFBQTs7QUFFbEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uLi8uLi8uLi9zcmMvaG9va3MvdXNlUmVhZENvbnRyYWN0LnRzPzc0YjMiXSwibmFtZXMiOlsicmVhZENvbnRyYWN0UXVlcnlPcHRpb25zIiwic3RydWN0dXJhbFNoYXJpbmciLCJ1c2VRdWVyeSIsInVzZUNoYWluSWQiLCJ1c2VDb25maWciLCJ1c2VSZWFkQ29udHJhY3QiLCJwYXJhbWV0ZXJzIiwiYWJpIiwiYWRkcmVzcyIsImZ1bmN0aW9uTmFtZSIsInF1ZXJ5IiwiY29kZSIsImNvbmZpZyIsImNoYWluSWQiLCJvcHRpb25zIiwiZW5hYmxlZCIsIkJvb2xlYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useReadContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js":
/*!***************************************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWaitForTransactionReceipt: () => (/* binding */ useWaitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/./node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useWaitForTransactionReceipt auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useWaitForTransactionReceipt */ function useWaitForTransactionReceipt(parameters = {}) {\n    const { hash, query = {} } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.waitForTransactionReceiptQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean(hash && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled\n    });\n} //# sourceMappingURL=useWaitForTransactionReceipt.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlV2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztrRkFjMEI7QUFHMkM7QUFDekI7QUFDRjtBQXlCMUMscUVBQ00sU0FBVUksNkJBTWRDLGFBSUksRUFBRTtJQUVOLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxRQUFRLEVBQUUsRUFBRSxHQUFHRjtJQUU3QixNQUFNRyxTQUFTTCx3REFBQUEsQ0FBVUU7SUFDekIsTUFBTUksVUFBVVAsMERBQUFBLENBQVc7UUFBRU07SUFBTTtJQUVuQyxNQUFNRSxVQUFVVix3RkFBQUEsQ0FBc0NRLFFBQVE7UUFDNUQsR0FBR0gsVUFBVTtRQUNiSSxTQUFTSixXQUFXSSxPQUFPLElBQUlBOztJQUVqQyxNQUFNRSxVQUFVQyxRQUFRTixRQUFTQyxDQUFBQSxNQUFNSSxPQUFPLElBQUk7SUFFbEQsT0FBT1YseURBQUFBLENBQVM7UUFDZCxHQUFJTSxLQUFhO1FBQ2pCLEdBQUdHLE9BQU87UUFDVkM7O0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uLi8uLi8uLi9zcmMvaG9va3MvdXNlV2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdC50cz9iN2NlIl0sIm5hbWVzIjpbIndhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHRRdWVyeU9wdGlvbnMiLCJ1c2VRdWVyeSIsInVzZUNoYWluSWQiLCJ1c2VDb25maWciLCJ1c2VXYWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0IiwicGFyYW1ldGVycyIsImhhc2giLCJxdWVyeSIsImNvbmZpZyIsImNoYWluSWQiLCJvcHRpb25zIiwiZW5hYmxlZCIsIkJvb2xlYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hooks/useWriteContract.js":
/*!***************************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hooks/useWriteContract.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWriteContract: () => (/* binding */ useWriteContract)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/writeContract.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/./node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useWriteContract auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useWriteContract */ function useWriteContract(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__.writeContractMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    return {\n        ...result,\n        writeContract: mutate,\n        writeContractAsync: mutateAsync\n    };\n} //# sourceMappingURL=useWriteContract.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaG9va3MvdXNlV3JpdGVDb250cmFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O3NFQUVtRDtBQVl6QjtBQVFnQjtBQXlDMUMseURBQ00sU0FBVUcsaUJBSWRDLGFBQTBELEVBQUU7SUFFNUQsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0Q7SUFFckIsTUFBTUUsU0FBU0osd0RBQUFBLENBQVVFO0lBRXpCLE1BQU1HLGtCQUFrQk4sK0VBQUFBLENBQTZCSztJQUNyRCxNQUFNLEVBQUVFLE1BQU0sRUFBRUMsV0FBVyxFQUFFLEdBQUdDLFFBQVEsR0FBR1Ysa0VBQUFBLENBQVk7UUFDckQsR0FBR0ssUUFBUTtRQUNYLEdBQUdFLGVBQWU7O0lBSXBCLE9BQU87UUFDTCxHQUFHRyxNQUFNO1FBQ1RDLGVBQWVIO1FBQ2ZJLG9CQUFvQkg7O0FBRXhCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi4vLi4vLi4vc3JjL2hvb2tzL3VzZVdyaXRlQ29udHJhY3QudHM/ZWI0MiJdLCJuYW1lcyI6WyJ1c2VNdXRhdGlvbiIsIndyaXRlQ29udHJhY3RNdXRhdGlvbk9wdGlvbnMiLCJ1c2VDb25maWciLCJ1c2VXcml0ZUNvbnRyYWN0IiwicGFyYW1ldGVycyIsIm11dGF0aW9uIiwiY29uZmlnIiwibXV0YXRpb25PcHRpb25zIiwibXV0YXRlIiwibXV0YXRlQXN5bmMiLCJyZXN1bHQiLCJ3cml0ZUNvbnRyYWN0Iiwid3JpdGVDb250cmFjdEFzeW5jIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hooks/useWriteContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/hydrate.js":
/*!************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/hydrate.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* binding */ Hydrate)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Hydrate auto */ \n\nfunction Hydrate(parameters) {\n    const { children, config, initialState, reconnectOnMount = true } = parameters;\n    const { onMount } = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_1__.hydrate)(config, {\n        initialState,\n        reconnectOnMount\n    });\n    // Hydrate for non-SSR\n    if (!config._internal.ssr) onMount();\n    // Hydrate for SSR\n    const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!active.current) return;\n        if (!config._internal.ssr) return;\n        onMount();\n        return ()=>{\n            active.current = false;\n        };\n    }, []);\n    return children;\n} //# sourceMappingURL=hydrate.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vaHlkcmF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7NkRBRXdFO0FBQ1o7QUFRdEQsU0FBVUcsUUFBUUMsVUFBaUQ7SUFDdkUsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsWUFBWSxFQUFFQyxtQkFBbUIsSUFBSSxFQUFFLEdBQUdKO0lBRXBFLE1BQU0sRUFBRUssT0FBTyxFQUFFLEdBQUdULG9EQUFBQSxDQUFRTSxRQUFRO1FBQ2xDQztRQUNBQzs7SUFHRixzQkFBc0I7SUFDdEIsSUFBSSxDQUFDRixPQUFPSSxTQUFTLENBQUNDLEdBQUcsRUFBRUY7SUFFM0Isa0JBQWtCO0lBQ2xCLE1BQU1HLFNBQVNWLDZDQUFBQSxDQUFPO0lBQ3RCLG1GQUFtRjtJQUNuRkQsZ0RBQUFBLENBQVU7UUFDUixJQUFJLENBQUNXLE9BQU9DLE9BQU8sRUFBRTtRQUNyQixJQUFJLENBQUNQLE9BQU9JLFNBQVMsQ0FBQ0MsR0FBRyxFQUFFO1FBQzNCRjtRQUNBLE9BQU87WUFDTEcsT0FBT0MsT0FBTyxHQUFHO1FBQ25CO0lBQ0YsR0FBRyxFQUFFO0lBRUwsT0FBT1I7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4uLy4uL3NyYy9oeWRyYXRlLnRzPzI1M2IiXSwibmFtZXMiOlsiaHlkcmF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIkh5ZHJhdGUiLCJwYXJhbWV0ZXJzIiwiY2hpbGRyZW4iLCJjb25maWciLCJpbml0aWFsU3RhdGUiLCJyZWNvbm5lY3RPbk1vdW50Iiwib25Nb3VudCIsIl9pbnRlcm5hbCIsInNzciIsImFjdGl2ZSIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/utils/getVersion.js":
/*!*********************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/utils/getVersion.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/wagmi/dist/esm/version.js\");\n\nconst getVersion = () => `wagmi@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQyxrQ0FBa0MsZ0RBQU8sQ0FBQztBQUNqRCIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL3V0aWxzL2dldFZlcnNpb24uanM/OTM2NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG5leHBvcnQgY29uc3QgZ2V0VmVyc2lvbiA9ICgpID0+IGB3YWdtaUAke3ZlcnNpb259YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/utils/query.js":
/*!****************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/utils/query.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery),\n/* harmony export */   useMutation: () => (/* reexport safe */ _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation),\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\n\n// Adding some basic customization.\n// Ideally we don't have this function, but `import('@tanstack/react-query').useQuery` currently has some quirks where it is super hard to\n// pass down the inferred `initialData` type because of it's discriminated overload in the on `useQuery`.\nfunction useQuery(parameters) {\n    const result = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        ...parameters,\n        queryKeyHashFn: _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.hashFn, // for bigint support\n    });\n    result.queryKey = parameters.queryKey;\n    return result;\n}\n// Adding some basic customization.\nfunction useInfiniteQuery(parameters) {\n    const result = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useInfiniteQuery)({\n        ...parameters,\n        queryKeyHashFn: _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.hashFn, // for bigint support\n    });\n    result.queryKey = parameters.queryKey;\n    return result;\n}\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vdXRpbHMvcXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFtSTtBQUN4RjtBQUNwQjtBQUN2QjtBQUNBO0FBQ0E7QUFDTztBQUNQLG1CQUFtQiwrREFBaUI7QUFDcEM7QUFDQSx3QkFBd0IscURBQU07QUFDOUIsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxtQkFBbUIsdUVBQXlCO0FBQzVDO0FBQ0Esd0JBQXdCLHFEQUFNO0FBQzlCLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL3V0aWxzL3F1ZXJ5LmpzP2U3YTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlSW5maW5pdGVRdWVyeSBhcyB0YW5zdGFja191c2VJbmZpbml0ZVF1ZXJ5LCB1c2VRdWVyeSBhcyB0YW5zdGFja191c2VRdWVyeSwgdXNlTXV0YXRpb24sIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IGhhc2hGbiB9IGZyb20gJ0B3YWdtaS9jb3JlL3F1ZXJ5JztcbmV4cG9ydCB7IHVzZU11dGF0aW9uIH07XG4vLyBBZGRpbmcgc29tZSBiYXNpYyBjdXN0b21pemF0aW9uLlxuLy8gSWRlYWxseSB3ZSBkb24ndCBoYXZlIHRoaXMgZnVuY3Rpb24sIGJ1dCBgaW1wb3J0KCdAdGFuc3RhY2svcmVhY3QtcXVlcnknKS51c2VRdWVyeWAgY3VycmVudGx5IGhhcyBzb21lIHF1aXJrcyB3aGVyZSBpdCBpcyBzdXBlciBoYXJkIHRvXG4vLyBwYXNzIGRvd24gdGhlIGluZmVycmVkIGBpbml0aWFsRGF0YWAgdHlwZSBiZWNhdXNlIG9mIGl0J3MgZGlzY3JpbWluYXRlZCBvdmVybG9hZCBpbiB0aGUgb24gYHVzZVF1ZXJ5YC5cbmV4cG9ydCBmdW5jdGlvbiB1c2VRdWVyeShwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgcmVzdWx0ID0gdGFuc3RhY2tfdXNlUXVlcnkoe1xuICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICBxdWVyeUtleUhhc2hGbjogaGFzaEZuLCAvLyBmb3IgYmlnaW50IHN1cHBvcnRcbiAgICB9KTtcbiAgICByZXN1bHQucXVlcnlLZXkgPSBwYXJhbWV0ZXJzLnF1ZXJ5S2V5O1xuICAgIHJldHVybiByZXN1bHQ7XG59XG4vLyBBZGRpbmcgc29tZSBiYXNpYyBjdXN0b21pemF0aW9uLlxuZXhwb3J0IGZ1bmN0aW9uIHVzZUluZmluaXRlUXVlcnkocGFyYW1ldGVycykge1xuICAgIGNvbnN0IHJlc3VsdCA9IHRhbnN0YWNrX3VzZUluZmluaXRlUXVlcnkoe1xuICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICBxdWVyeUtleUhhc2hGbjogaGFzaEZuLCAvLyBmb3IgYmlnaW50IHN1cHBvcnRcbiAgICB9KTtcbiAgICByZXN1bHQucXVlcnlLZXkgPSBwYXJhbWV0ZXJzLnF1ZXJ5S2V5O1xuICAgIHJldHVybiByZXN1bHQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1xdWVyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/utils/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/wagmi/dist/esm/version.js":
/*!************************************************!*\
  !*** ./node_modules/wagmi/dist/esm/version.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.16.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL3ZlcnNpb24uanM/NzZmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjE2LjAnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wagmi/dist/esm/version.js\n");

/***/ })

};
;