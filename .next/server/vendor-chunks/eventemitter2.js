/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventemitter2";
exports.ids = ["vendor-chunks/eventemitter2"];
exports.modules = {

/***/ "(ssr)/./node_modules/eventemitter2/lib/eventemitter2.js":
/*!*********************************************************!*\
  !*** ./node_modules/eventemitter2/lib/eventemitter2.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;/*!\n * EventEmitter2\n * https://github.com/hij1nx/EventEmitter2\n *\n * Copyright (c) 2013 hij1nx\n * Licensed under the MIT license.\n */\n;!function(undefined) {\n  var hasOwnProperty= Object.hasOwnProperty;\n  var isArray = Array.isArray ? Array.isArray : function _isArray(obj) {\n    return Object.prototype.toString.call(obj) === \"[object Array]\";\n  };\n  var defaultMaxListeners = 10;\n  var nextTickSupported= typeof process=='object' && typeof process.nextTick=='function';\n  var symbolsSupported= typeof Symbol==='function';\n  var reflectSupported= typeof Reflect === 'object';\n  var setImmediateSupported= typeof setImmediate === 'function';\n  var _setImmediate= setImmediateSupported ? setImmediate : setTimeout;\n  var ownKeys= symbolsSupported? (reflectSupported && typeof Reflect.ownKeys==='function'? Reflect.ownKeys : function(obj){\n    var arr= Object.getOwnPropertyNames(obj);\n    arr.push.apply(arr, Object.getOwnPropertySymbols(obj));\n    return arr;\n  }) : Object.keys;\n\n  function init() {\n    this._events = {};\n    if (this._conf) {\n      configure.call(this, this._conf);\n    }\n  }\n\n  function configure(conf) {\n    if (conf) {\n      this._conf = conf;\n\n      conf.delimiter && (this.delimiter = conf.delimiter);\n\n      if(conf.maxListeners!==undefined){\n          this._maxListeners= conf.maxListeners;\n      }\n\n      conf.wildcard && (this.wildcard = conf.wildcard);\n      conf.newListener && (this._newListener = conf.newListener);\n      conf.removeListener && (this._removeListener = conf.removeListener);\n      conf.verboseMemoryLeak && (this.verboseMemoryLeak = conf.verboseMemoryLeak);\n      conf.ignoreErrors && (this.ignoreErrors = conf.ignoreErrors);\n\n      if (this.wildcard) {\n        this.listenerTree = {};\n      }\n    }\n  }\n\n  function logPossibleMemoryLeak(count, eventName) {\n    var errorMsg = '(node) warning: possible EventEmitter memory ' +\n        'leak detected. ' + count + ' listeners added. ' +\n        'Use emitter.setMaxListeners() to increase limit.';\n\n    if(this.verboseMemoryLeak){\n      errorMsg += ' Event name: ' + eventName + '.';\n    }\n\n    if(typeof process !== 'undefined' && process.emitWarning){\n      var e = new Error(errorMsg);\n      e.name = 'MaxListenersExceededWarning';\n      e.emitter = this;\n      e.count = count;\n      process.emitWarning(e);\n    } else {\n      console.error(errorMsg);\n\n      if (console.trace){\n        console.trace();\n      }\n    }\n  }\n\n  var toArray = function (a, b, c) {\n    var n = arguments.length;\n    switch (n) {\n      case 0:\n        return [];\n      case 1:\n        return [a];\n      case 2:\n        return [a, b];\n      case 3:\n        return [a, b, c];\n      default:\n        var arr = new Array(n);\n        while (n--) {\n          arr[n] = arguments[n];\n        }\n        return arr;\n    }\n  };\n\n  function toObject(keys, values) {\n    var obj = {};\n    var key;\n    var len = keys.length;\n    var valuesCount = values ? values.length : 0;\n    for (var i = 0; i < len; i++) {\n      key = keys[i];\n      obj[key] = i < valuesCount ? values[i] : undefined;\n    }\n    return obj;\n  }\n\n  function TargetObserver(emitter, target, options) {\n    this._emitter = emitter;\n    this._target = target;\n    this._listeners = {};\n    this._listenersCount = 0;\n\n    var on, off;\n\n    if (options.on || options.off) {\n      on = options.on;\n      off = options.off;\n    }\n\n    if (target.addEventListener) {\n      on = target.addEventListener;\n      off = target.removeEventListener;\n    } else if (target.addListener) {\n      on = target.addListener;\n      off = target.removeListener;\n    } else if (target.on) {\n      on = target.on;\n      off = target.off;\n    }\n\n    if (!on && !off) {\n      throw Error('target does not implement any known event API');\n    }\n\n    if (typeof on !== 'function') {\n      throw TypeError('on method must be a function');\n    }\n\n    if (typeof off !== 'function') {\n      throw TypeError('off method must be a function');\n    }\n\n    this._on = on;\n    this._off = off;\n\n    var _observers= emitter._observers;\n    if(_observers){\n      _observers.push(this);\n    }else{\n      emitter._observers= [this];\n    }\n  }\n\n  Object.assign(TargetObserver.prototype, {\n    subscribe: function(event, localEvent, reducer){\n      var observer= this;\n      var target= this._target;\n      var emitter= this._emitter;\n      var listeners= this._listeners;\n      var handler= function(){\n        var args= toArray.apply(null, arguments);\n        var eventObj= {\n          data: args,\n          name: localEvent,\n          original: event\n        };\n        if(reducer){\n          var result= reducer.call(target, eventObj);\n          if(result!==false){\n            emitter.emit.apply(emitter, [eventObj.name].concat(args))\n          }\n          return;\n        }\n        emitter.emit.apply(emitter, [localEvent].concat(args));\n      };\n\n\n      if(listeners[event]){\n        throw Error('Event \\'' + event + '\\' is already listening');\n      }\n\n      this._listenersCount++;\n\n      if(emitter._newListener && emitter._removeListener && !observer._onNewListener){\n\n        this._onNewListener = function (_event) {\n          if (_event === localEvent && listeners[event] === null) {\n            listeners[event] = handler;\n            observer._on.call(target, event, handler);\n          }\n        };\n\n        emitter.on('newListener', this._onNewListener);\n\n        this._onRemoveListener= function(_event){\n          if(_event === localEvent && !emitter.hasListeners(_event) && listeners[event]){\n            listeners[event]= null;\n            observer._off.call(target, event, handler);\n          }\n        };\n\n        listeners[event]= null;\n\n        emitter.on('removeListener', this._onRemoveListener);\n      }else{\n        listeners[event]= handler;\n        observer._on.call(target, event, handler);\n      }\n    },\n\n    unsubscribe: function(event){\n      var observer= this;\n      var listeners= this._listeners;\n      var emitter= this._emitter;\n      var handler;\n      var events;\n      var off= this._off;\n      var target= this._target;\n      var i;\n\n      if(event && typeof event!=='string'){\n        throw TypeError('event must be a string');\n      }\n\n      function clearRefs(){\n        if(observer._onNewListener){\n          emitter.off('newListener', observer._onNewListener);\n          emitter.off('removeListener', observer._onRemoveListener);\n          observer._onNewListener= null;\n          observer._onRemoveListener= null;\n        }\n        var index= findTargetIndex.call(emitter, observer);\n        emitter._observers.splice(index, 1);\n      }\n\n      if(event){\n        handler= listeners[event];\n        if(!handler) return;\n        off.call(target, event, handler);\n        delete listeners[event];\n        if(!--this._listenersCount){\n          clearRefs();\n        }\n      }else{\n        events= ownKeys(listeners);\n        i= events.length;\n        while(i-->0){\n          event= events[i];\n          off.call(target, event, listeners[event]);\n        }\n        this._listeners= {};\n        this._listenersCount= 0;\n        clearRefs();\n      }\n    }\n  });\n\n  function resolveOptions(options, schema, reducers, allowUnknown) {\n    var computedOptions = Object.assign({}, schema);\n\n    if (!options) return computedOptions;\n\n    if (typeof options !== 'object') {\n      throw TypeError('options must be an object')\n    }\n\n    var keys = Object.keys(options);\n    var length = keys.length;\n    var option, value;\n    var reducer;\n\n    function reject(reason) {\n      throw Error('Invalid \"' + option + '\" option value' + (reason ? '. Reason: ' + reason : ''))\n    }\n\n    for (var i = 0; i < length; i++) {\n      option = keys[i];\n      if (!allowUnknown && !hasOwnProperty.call(schema, option)) {\n        throw Error('Unknown \"' + option + '\" option');\n      }\n      value = options[option];\n      if (value !== undefined) {\n        reducer = reducers[option];\n        computedOptions[option] = reducer ? reducer(value, reject) : value;\n      }\n    }\n    return computedOptions;\n  }\n\n  function constructorReducer(value, reject) {\n    if (typeof value !== 'function' || !value.hasOwnProperty('prototype')) {\n      reject('value must be a constructor');\n    }\n    return value;\n  }\n\n  function makeTypeReducer(types) {\n    var message= 'value must be type of ' + types.join('|');\n    var len= types.length;\n    var firstType= types[0];\n    var secondType= types[1];\n\n    if (len === 1) {\n      return function (v, reject) {\n        if (typeof v === firstType) {\n          return v;\n        }\n        reject(message);\n      }\n    }\n\n    if (len === 2) {\n      return function (v, reject) {\n        var kind= typeof v;\n        if (kind === firstType || kind === secondType) return v;\n        reject(message);\n      }\n    }\n\n    return function (v, reject) {\n      var kind = typeof v;\n      var i = len;\n      while (i-- > 0) {\n        if (kind === types[i]) return v;\n      }\n      reject(message);\n    }\n  }\n\n  var functionReducer= makeTypeReducer(['function']);\n\n  var objectFunctionReducer= makeTypeReducer(['object', 'function']);\n\n  function makeCancelablePromise(Promise, executor, options) {\n    var isCancelable;\n    var callbacks;\n    var timer= 0;\n    var subscriptionClosed;\n\n    var promise = new Promise(function (resolve, reject, onCancel) {\n      options= resolveOptions(options, {\n        timeout: 0,\n        overload: false\n      }, {\n        timeout: function(value, reject){\n          value*= 1;\n          if (typeof value !== 'number' || value < 0 || !Number.isFinite(value)) {\n            reject('timeout must be a positive number');\n          }\n          return value;\n        }\n      });\n\n      isCancelable = !options.overload && typeof Promise.prototype.cancel === 'function' && typeof onCancel === 'function';\n\n      function cleanup() {\n        if (callbacks) {\n          callbacks = null;\n        }\n        if (timer) {\n          clearTimeout(timer);\n          timer = 0;\n        }\n      }\n\n      var _resolve= function(value){\n        cleanup();\n        resolve(value);\n      };\n\n      var _reject= function(err){\n        cleanup();\n        reject(err);\n      };\n\n      if (isCancelable) {\n        executor(_resolve, _reject, onCancel);\n      } else {\n        callbacks = [function(reason){\n          _reject(reason || Error('canceled'));\n        }];\n        executor(_resolve, _reject, function (cb) {\n          if (subscriptionClosed) {\n            throw Error('Unable to subscribe on cancel event asynchronously')\n          }\n          if (typeof cb !== 'function') {\n            throw TypeError('onCancel callback must be a function');\n          }\n          callbacks.push(cb);\n        });\n        subscriptionClosed= true;\n      }\n\n      if (options.timeout > 0) {\n        timer= setTimeout(function(){\n          var reason= Error('timeout');\n          reason.code = 'ETIMEDOUT'\n          timer= 0;\n          promise.cancel(reason);\n          reject(reason);\n        }, options.timeout);\n      }\n    });\n\n    if (!isCancelable) {\n      promise.cancel = function (reason) {\n        if (!callbacks) {\n          return;\n        }\n        var length = callbacks.length;\n        for (var i = 1; i < length; i++) {\n          callbacks[i](reason);\n        }\n        // internal callback to reject the promise\n        callbacks[0](reason);\n        callbacks = null;\n      };\n    }\n\n    return promise;\n  }\n\n  function findTargetIndex(observer) {\n    var observers = this._observers;\n    if(!observers){\n      return -1;\n    }\n    var len = observers.length;\n    for (var i = 0; i < len; i++) {\n      if (observers[i]._target === observer) return i;\n    }\n    return -1;\n  }\n\n  // Attention, function return type now is array, always !\n  // It has zero elements if no any matches found and one or more\n  // elements (leafs) if there are matches\n  //\n  function searchListenerTree(handlers, type, tree, i, typeLength) {\n    if (!tree) {\n      return null;\n    }\n\n    if (i === 0) {\n      var kind = typeof type;\n      if (kind === 'string') {\n        var ns, n, l = 0, j = 0, delimiter = this.delimiter, dl = delimiter.length;\n        if ((n = type.indexOf(delimiter)) !== -1) {\n          ns = new Array(5);\n          do {\n            ns[l++] = type.slice(j, n);\n            j = n + dl;\n          } while ((n = type.indexOf(delimiter, j)) !== -1);\n\n          ns[l++] = type.slice(j);\n          type = ns;\n          typeLength = l;\n        } else {\n          type = [type];\n          typeLength = 1;\n        }\n      } else if (kind === 'object') {\n        typeLength = type.length;\n      } else {\n        type = [type];\n        typeLength = 1;\n      }\n    }\n\n    var listeners= null, branch, xTree, xxTree, isolatedBranch, endReached, currentType = type[i],\n        nextType = type[i + 1], branches, _listeners;\n\n    if (i === typeLength) {\n      //\n      // If at the end of the event(s) list and the tree has listeners\n      // invoke those listeners.\n      //\n\n      if(tree._listeners) {\n        if (typeof tree._listeners === 'function') {\n          handlers && handlers.push(tree._listeners);\n          listeners = [tree];\n        } else {\n          handlers && handlers.push.apply(handlers, tree._listeners);\n          listeners = [tree];\n        }\n      }\n    } else {\n\n      if (currentType === '*') {\n        //\n        // If the event emitted is '*' at this part\n        // or there is a concrete match at this patch\n        //\n        branches = ownKeys(tree);\n        n = branches.length;\n        while (n-- > 0) {\n          branch = branches[n];\n          if (branch !== '_listeners') {\n            _listeners = searchListenerTree(handlers, type, tree[branch], i + 1, typeLength);\n            if (_listeners) {\n              if (listeners) {\n                listeners.push.apply(listeners, _listeners);\n              } else {\n                listeners = _listeners;\n              }\n            }\n          }\n        }\n        return listeners;\n      } else if (currentType === '**') {\n        endReached = (i + 1 === typeLength || (i + 2 === typeLength && nextType === '*'));\n        if (endReached && tree._listeners) {\n          // The next element has a _listeners, add it to the handlers.\n          listeners = searchListenerTree(handlers, type, tree, typeLength, typeLength);\n        }\n\n        branches = ownKeys(tree);\n        n = branches.length;\n        while (n-- > 0) {\n          branch = branches[n];\n          if (branch !== '_listeners') {\n            if (branch === '*' || branch === '**') {\n              if (tree[branch]._listeners && !endReached) {\n                _listeners = searchListenerTree(handlers, type, tree[branch], typeLength, typeLength);\n                if (_listeners) {\n                  if (listeners) {\n                    listeners.push.apply(listeners, _listeners);\n                  } else {\n                    listeners = _listeners;\n                  }\n                }\n              }\n              _listeners = searchListenerTree(handlers, type, tree[branch], i, typeLength);\n            } else if (branch === nextType) {\n              _listeners = searchListenerTree(handlers, type, tree[branch], i + 2, typeLength);\n            } else {\n              // No match on this one, shift into the tree but not in the type array.\n              _listeners = searchListenerTree(handlers, type, tree[branch], i, typeLength);\n            }\n            if (_listeners) {\n              if (listeners) {\n                listeners.push.apply(listeners, _listeners);\n              } else {\n                listeners = _listeners;\n              }\n            }\n          }\n        }\n        return listeners;\n      } else if (tree[currentType]) {\n        listeners = searchListenerTree(handlers, type, tree[currentType], i + 1, typeLength);\n      }\n    }\n\n      xTree = tree['*'];\n    if (xTree) {\n      //\n      // If the listener tree will allow any match for this part,\n      // then recursively explore all branches of the tree\n      //\n      searchListenerTree(handlers, type, xTree, i + 1, typeLength);\n    }\n\n    xxTree = tree['**'];\n    if (xxTree) {\n      if (i < typeLength) {\n        if (xxTree._listeners) {\n          // If we have a listener on a '**', it will catch all, so add its handler.\n          searchListenerTree(handlers, type, xxTree, typeLength, typeLength);\n        }\n\n        // Build arrays of matching next branches and others.\n        branches= ownKeys(xxTree);\n        n= branches.length;\n        while(n-->0){\n          branch= branches[n];\n          if (branch !== '_listeners') {\n            if (branch === nextType) {\n              // We know the next element will match, so jump twice.\n              searchListenerTree(handlers, type, xxTree[branch], i + 2, typeLength);\n            } else if (branch === currentType) {\n              // Current node matches, move into the tree.\n              searchListenerTree(handlers, type, xxTree[branch], i + 1, typeLength);\n            } else {\n              isolatedBranch = {};\n              isolatedBranch[branch] = xxTree[branch];\n              searchListenerTree(handlers, type, {'**': isolatedBranch}, i + 1, typeLength);\n            }\n          }\n        }\n      } else if (xxTree._listeners) {\n        // We have reached the end and still on a '**'\n        searchListenerTree(handlers, type, xxTree, typeLength, typeLength);\n      } else if (xxTree['*'] && xxTree['*']._listeners) {\n        searchListenerTree(handlers, type, xxTree['*'], typeLength, typeLength);\n      }\n    }\n\n    return listeners;\n  }\n\n  function growListenerTree(type, listener, prepend) {\n    var len = 0, j = 0, i, delimiter = this.delimiter, dl= delimiter.length, ns;\n\n    if(typeof type==='string') {\n      if ((i = type.indexOf(delimiter)) !== -1) {\n        ns = new Array(5);\n        do {\n          ns[len++] = type.slice(j, i);\n          j = i + dl;\n        } while ((i = type.indexOf(delimiter, j)) !== -1);\n\n        ns[len++] = type.slice(j);\n      }else{\n        ns= [type];\n        len= 1;\n      }\n    }else{\n      ns= type;\n      len= type.length;\n    }\n\n    //\n    // Looks for two consecutive '**', if so, don't add the event at all.\n    //\n    if (len > 1) {\n      for (i = 0; i + 1 < len; i++) {\n        if (ns[i] === '**' && ns[i + 1] === '**') {\n          return;\n        }\n      }\n    }\n\n\n\n    var tree = this.listenerTree, name;\n\n    for (i = 0; i < len; i++) {\n      name = ns[i];\n\n      tree = tree[name] || (tree[name] = {});\n\n      if (i === len - 1) {\n        if (!tree._listeners) {\n          tree._listeners = listener;\n        } else {\n          if (typeof tree._listeners === 'function') {\n            tree._listeners = [tree._listeners];\n          }\n\n          if (prepend) {\n            tree._listeners.unshift(listener);\n          } else {\n            tree._listeners.push(listener);\n          }\n\n          if (\n              !tree._listeners.warned &&\n              this._maxListeners > 0 &&\n              tree._listeners.length > this._maxListeners\n          ) {\n            tree._listeners.warned = true;\n            logPossibleMemoryLeak.call(this, tree._listeners.length, name);\n          }\n        }\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function collectTreeEvents(tree, events, root, asArray){\n     var branches= ownKeys(tree);\n     var i= branches.length;\n     var branch, branchName, path;\n     var hasListeners= tree['_listeners'];\n     var isArrayPath;\n\n     while(i-->0){\n         branchName= branches[i];\n\n         branch= tree[branchName];\n\n         if(branchName==='_listeners'){\n             path= root;\n         }else {\n             path = root ? root.concat(branchName) : [branchName];\n         }\n\n         isArrayPath= asArray || typeof branchName==='symbol';\n\n         hasListeners && events.push(isArrayPath? path : path.join(this.delimiter));\n\n         if(typeof branch==='object'){\n             collectTreeEvents.call(this, branch, events, path, isArrayPath);\n         }\n     }\n\n     return events;\n  }\n\n  function recursivelyGarbageCollect(root) {\n    var keys = ownKeys(root);\n    var i= keys.length;\n    var obj, key, flag;\n    while(i-->0){\n      key = keys[i];\n      obj = root[key];\n\n      if(obj){\n          flag= true;\n          if(key !== '_listeners' && !recursivelyGarbageCollect(obj)){\n             delete root[key];\n          }\n      }\n    }\n\n    return flag;\n  }\n\n  function Listener(emitter, event, listener){\n    this.emitter= emitter;\n    this.event= event;\n    this.listener= listener;\n  }\n\n  Listener.prototype.off= function(){\n    this.emitter.off(this.event, this.listener);\n    return this;\n  };\n\n  function setupListener(event, listener, options){\n      if (options === true) {\n        promisify = true;\n      } else if (options === false) {\n        async = true;\n      } else {\n        if (!options || typeof options !== 'object') {\n          throw TypeError('options should be an object or true');\n        }\n        var async = options.async;\n        var promisify = options.promisify;\n        var nextTick = options.nextTick;\n        var objectify = options.objectify;\n      }\n\n      if (async || nextTick || promisify) {\n        var _listener = listener;\n        var _origin = listener._origin || listener;\n\n        if (nextTick && !nextTickSupported) {\n          throw Error('process.nextTick is not supported');\n        }\n\n        if (promisify === undefined) {\n          promisify = listener.constructor.name === 'AsyncFunction';\n        }\n\n        listener = function () {\n          var args = arguments;\n          var context = this;\n          var event = this.event;\n\n          return promisify ? (nextTick ? Promise.resolve() : new Promise(function (resolve) {\n            _setImmediate(resolve);\n          }).then(function () {\n            context.event = event;\n            return _listener.apply(context, args)\n          })) : (nextTick ? process.nextTick : _setImmediate)(function () {\n            context.event = event;\n            _listener.apply(context, args)\n          });\n        };\n\n        listener._async = true;\n        listener._origin = _origin;\n      }\n\n    return [listener, objectify? new Listener(this, event, listener): this];\n  }\n\n  function EventEmitter(conf) {\n    this._events = {};\n    this._newListener = false;\n    this._removeListener = false;\n    this.verboseMemoryLeak = false;\n    configure.call(this, conf);\n  }\n\n  EventEmitter.EventEmitter2 = EventEmitter; // backwards compatibility for exporting EventEmitter property\n\n  EventEmitter.prototype.listenTo= function(target, events, options){\n    if(typeof target!=='object'){\n      throw TypeError('target musts be an object');\n    }\n\n    var emitter= this;\n\n    options = resolveOptions(options, {\n      on: undefined,\n      off: undefined,\n      reducers: undefined\n    }, {\n      on: functionReducer,\n      off: functionReducer,\n      reducers: objectFunctionReducer\n    });\n\n    function listen(events){\n      if(typeof events!=='object'){\n        throw TypeError('events must be an object');\n      }\n\n      var reducers= options.reducers;\n      var index= findTargetIndex.call(emitter, target);\n      var observer;\n\n      if(index===-1){\n        observer= new TargetObserver(emitter, target, options);\n      }else{\n        observer= emitter._observers[index];\n      }\n\n      var keys= ownKeys(events);\n      var len= keys.length;\n      var event;\n      var isSingleReducer= typeof reducers==='function';\n\n      for(var i=0; i<len; i++){\n        event= keys[i];\n        observer.subscribe(\n            event,\n            events[event] || event,\n            isSingleReducer ? reducers : reducers && reducers[event]\n        );\n      }\n    }\n\n    isArray(events)?\n        listen(toObject(events)) :\n        (typeof events==='string'? listen(toObject(events.split(/\\s+/))): listen(events));\n\n    return this;\n  };\n\n  EventEmitter.prototype.stopListeningTo = function (target, event) {\n    var observers = this._observers;\n\n    if(!observers){\n      return false;\n    }\n\n    var i = observers.length;\n    var observer;\n    var matched= false;\n\n    if(target && typeof target!=='object'){\n      throw TypeError('target should be an object');\n    }\n\n    while (i-- > 0) {\n      observer = observers[i];\n      if (!target || observer._target === target) {\n        observer.unsubscribe(event);\n        matched= true;\n      }\n    }\n\n    return matched;\n  };\n\n  // By default EventEmitters will print a warning if more than\n  // 10 listeners are added to it. This is a useful default which\n  // helps finding memory leaks.\n  //\n  // Obviously not all Emitters should be limited to 10. This function allows\n  // that to be increased. Set to zero for unlimited.\n\n  EventEmitter.prototype.delimiter = '.';\n\n  EventEmitter.prototype.setMaxListeners = function(n) {\n    if (n !== undefined) {\n      this._maxListeners = n;\n      if (!this._conf) this._conf = {};\n      this._conf.maxListeners = n;\n    }\n  };\n\n  EventEmitter.prototype.getMaxListeners = function() {\n    return this._maxListeners;\n  };\n\n  EventEmitter.prototype.event = '';\n\n  EventEmitter.prototype.once = function(event, fn, options) {\n    return this._once(event, fn, false, options);\n  };\n\n  EventEmitter.prototype.prependOnceListener = function(event, fn, options) {\n    return this._once(event, fn, true, options);\n  };\n\n  EventEmitter.prototype._once = function(event, fn, prepend, options) {\n    return this._many(event, 1, fn, prepend, options);\n  };\n\n  EventEmitter.prototype.many = function(event, ttl, fn, options) {\n    return this._many(event, ttl, fn, false, options);\n  };\n\n  EventEmitter.prototype.prependMany = function(event, ttl, fn, options) {\n    return this._many(event, ttl, fn, true, options);\n  };\n\n  EventEmitter.prototype._many = function(event, ttl, fn, prepend, options) {\n    var self = this;\n\n    if (typeof fn !== 'function') {\n      throw new Error('many only accepts instances of Function');\n    }\n\n    function listener() {\n      if (--ttl === 0) {\n        self.off(event, listener);\n      }\n      return fn.apply(this, arguments);\n    }\n\n    listener._origin = fn;\n\n    return this._on(event, listener, prepend, options);\n  };\n\n  EventEmitter.prototype.emit = function() {\n    if (!this._events && !this._all) {\n      return false;\n    }\n\n    this._events || init.call(this);\n\n    var type = arguments[0], ns, wildcard= this.wildcard;\n    var args,l,i,j, containsSymbol;\n\n    if (type === 'newListener' && !this._newListener) {\n      if (!this._events.newListener) {\n        return false;\n      }\n    }\n\n    if (wildcard) {\n      ns= type;\n      if(type!=='newListener' && type!=='removeListener'){\n        if (typeof type === 'object') {\n          l = type.length;\n          if (symbolsSupported) {\n            for (i = 0; i < l; i++) {\n              if (typeof type[i] === 'symbol') {\n                containsSymbol = true;\n                break;\n              }\n            }\n          }\n          if (!containsSymbol) {\n            type = type.join(this.delimiter);\n          }\n        }\n      }\n    }\n\n    var al = arguments.length;\n    var handler;\n\n    if (this._all && this._all.length) {\n      handler = this._all.slice();\n\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler[i].call(this, type);\n          break;\n        case 2:\n          handler[i].call(this, type, arguments[1]);\n          break;\n        case 3:\n          handler[i].call(this, type, arguments[1], arguments[2]);\n          break;\n        default:\n          handler[i].apply(this, arguments);\n        }\n      }\n    }\n\n    if (wildcard) {\n      handler = [];\n      searchListenerTree.call(this, handler, ns, this.listenerTree, 0, l);\n    } else {\n      handler = this._events[type];\n      if (typeof handler === 'function') {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler.call(this);\n          break;\n        case 2:\n          handler.call(this, arguments[1]);\n          break;\n        case 3:\n          handler.call(this, arguments[1], arguments[2]);\n          break;\n        default:\n          args = new Array(al - 1);\n          for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n          handler.apply(this, args);\n        }\n        return true;\n      } else if (handler) {\n        // need to make copy of handlers because list can change in the middle\n        // of emit call\n        handler = handler.slice();\n      }\n    }\n\n    if (handler && handler.length) {\n      if (al > 3) {\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n      }\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler[i].call(this);\n          break;\n        case 2:\n          handler[i].call(this, arguments[1]);\n          break;\n        case 3:\n          handler[i].call(this, arguments[1], arguments[2]);\n          break;\n        default:\n          handler[i].apply(this, args);\n        }\n      }\n      return true;\n    } else if (!this.ignoreErrors && !this._all && type === 'error') {\n      if (arguments[1] instanceof Error) {\n        throw arguments[1]; // Unhandled 'error' event\n      } else {\n        throw new Error(\"Uncaught, unspecified 'error' event.\");\n      }\n    }\n\n    return !!this._all;\n  };\n\n  EventEmitter.prototype.emitAsync = function() {\n    if (!this._events && !this._all) {\n      return false;\n    }\n\n    this._events || init.call(this);\n\n    var type = arguments[0], wildcard= this.wildcard, ns, containsSymbol;\n    var args,l,i,j;\n\n    if (type === 'newListener' && !this._newListener) {\n        if (!this._events.newListener) { return Promise.resolve([false]); }\n    }\n\n    if (wildcard) {\n      ns= type;\n      if(type!=='newListener' && type!=='removeListener'){\n        if (typeof type === 'object') {\n          l = type.length;\n          if (symbolsSupported) {\n            for (i = 0; i < l; i++) {\n              if (typeof type[i] === 'symbol') {\n                containsSymbol = true;\n                break;\n              }\n            }\n          }\n          if (!containsSymbol) {\n            type = type.join(this.delimiter);\n          }\n        }\n      }\n    }\n\n    var promises= [];\n\n    var al = arguments.length;\n    var handler;\n\n    if (this._all) {\n      for (i = 0, l = this._all.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          promises.push(this._all[i].call(this, type));\n          break;\n        case 2:\n          promises.push(this._all[i].call(this, type, arguments[1]));\n          break;\n        case 3:\n          promises.push(this._all[i].call(this, type, arguments[1], arguments[2]));\n          break;\n        default:\n          promises.push(this._all[i].apply(this, arguments));\n        }\n      }\n    }\n\n    if (wildcard) {\n      handler = [];\n      searchListenerTree.call(this, handler, ns, this.listenerTree, 0);\n    } else {\n      handler = this._events[type];\n    }\n\n    if (typeof handler === 'function') {\n      this.event = type;\n      switch (al) {\n      case 1:\n        promises.push(handler.call(this));\n        break;\n      case 2:\n        promises.push(handler.call(this, arguments[1]));\n        break;\n      case 3:\n        promises.push(handler.call(this, arguments[1], arguments[2]));\n        break;\n      default:\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n        promises.push(handler.apply(this, args));\n      }\n    } else if (handler && handler.length) {\n      handler = handler.slice();\n      if (al > 3) {\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n      }\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          promises.push(handler[i].call(this));\n          break;\n        case 2:\n          promises.push(handler[i].call(this, arguments[1]));\n          break;\n        case 3:\n          promises.push(handler[i].call(this, arguments[1], arguments[2]));\n          break;\n        default:\n          promises.push(handler[i].apply(this, args));\n        }\n      }\n    } else if (!this.ignoreErrors && !this._all && type === 'error') {\n      if (arguments[1] instanceof Error) {\n        return Promise.reject(arguments[1]); // Unhandled 'error' event\n      } else {\n        return Promise.reject(\"Uncaught, unspecified 'error' event.\");\n      }\n    }\n\n    return Promise.all(promises);\n  };\n\n  EventEmitter.prototype.on = function(type, listener, options) {\n    return this._on(type, listener, false, options);\n  };\n\n  EventEmitter.prototype.prependListener = function(type, listener, options) {\n    return this._on(type, listener, true, options);\n  };\n\n  EventEmitter.prototype.onAny = function(fn) {\n    return this._onAny(fn, false);\n  };\n\n  EventEmitter.prototype.prependAny = function(fn) {\n    return this._onAny(fn, true);\n  };\n\n  EventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n  EventEmitter.prototype._onAny = function(fn, prepend){\n    if (typeof fn !== 'function') {\n      throw new Error('onAny only accepts instances of Function');\n    }\n\n    if (!this._all) {\n      this._all = [];\n    }\n\n    // Add the function to the event listener collection.\n    if(prepend){\n      this._all.unshift(fn);\n    }else{\n      this._all.push(fn);\n    }\n\n    return this;\n  };\n\n  EventEmitter.prototype._on = function(type, listener, prepend, options) {\n    if (typeof type === 'function') {\n      this._onAny(type, listener);\n      return this;\n    }\n\n    if (typeof listener !== 'function') {\n      throw new Error('on only accepts instances of Function');\n    }\n    this._events || init.call(this);\n\n    var returnValue= this, temp;\n\n    if (options !== undefined) {\n      temp = setupListener.call(this, type, listener, options);\n      listener = temp[0];\n      returnValue = temp[1];\n    }\n\n    // To avoid recursion in the case that type == \"newListeners\"! Before\n    // adding it to the listeners, first emit \"newListeners\".\n    if (this._newListener) {\n      this.emit('newListener', type, listener);\n    }\n\n    if (this.wildcard) {\n      growListenerTree.call(this, type, listener, prepend);\n      return returnValue;\n    }\n\n    if (!this._events[type]) {\n      // Optimize the case of one listener. Don't need the extra array object.\n      this._events[type] = listener;\n    } else {\n      if (typeof this._events[type] === 'function') {\n        // Change to array.\n        this._events[type] = [this._events[type]];\n      }\n\n      // If we've already got an array, just add\n      if(prepend){\n        this._events[type].unshift(listener);\n      }else{\n        this._events[type].push(listener);\n      }\n\n      // Check for listener leak\n      if (\n        !this._events[type].warned &&\n        this._maxListeners > 0 &&\n        this._events[type].length > this._maxListeners\n      ) {\n        this._events[type].warned = true;\n        logPossibleMemoryLeak.call(this, this._events[type].length, type);\n      }\n    }\n\n    return returnValue;\n  };\n\n  EventEmitter.prototype.off = function(type, listener) {\n    if (typeof listener !== 'function') {\n      throw new Error('removeListener only takes instances of Function');\n    }\n\n    var handlers,leafs=[];\n\n    if(this.wildcard) {\n      var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n      leafs = searchListenerTree.call(this, null, ns, this.listenerTree, 0);\n      if(!leafs) return this;\n    } else {\n      // does not use listeners(), so no side effect of creating _events[type]\n      if (!this._events[type]) return this;\n      handlers = this._events[type];\n      leafs.push({_listeners:handlers});\n    }\n\n    for (var iLeaf=0; iLeaf<leafs.length; iLeaf++) {\n      var leaf = leafs[iLeaf];\n      handlers = leaf._listeners;\n      if (isArray(handlers)) {\n\n        var position = -1;\n\n        for (var i = 0, length = handlers.length; i < length; i++) {\n          if (handlers[i] === listener ||\n            (handlers[i].listener && handlers[i].listener === listener) ||\n            (handlers[i]._origin && handlers[i]._origin === listener)) {\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0) {\n          continue;\n        }\n\n        if(this.wildcard) {\n          leaf._listeners.splice(position, 1);\n        }\n        else {\n          this._events[type].splice(position, 1);\n        }\n\n        if (handlers.length === 0) {\n          if(this.wildcard) {\n            delete leaf._listeners;\n          }\n          else {\n            delete this._events[type];\n          }\n        }\n        if (this._removeListener)\n          this.emit(\"removeListener\", type, listener);\n\n        return this;\n      }\n      else if (handlers === listener ||\n        (handlers.listener && handlers.listener === listener) ||\n        (handlers._origin && handlers._origin === listener)) {\n        if(this.wildcard) {\n          delete leaf._listeners;\n        }\n        else {\n          delete this._events[type];\n        }\n        if (this._removeListener)\n          this.emit(\"removeListener\", type, listener);\n      }\n    }\n\n    this.listenerTree && recursivelyGarbageCollect(this.listenerTree);\n\n    return this;\n  };\n\n  EventEmitter.prototype.offAny = function(fn) {\n    var i = 0, l = 0, fns;\n    if (fn && this._all && this._all.length > 0) {\n      fns = this._all;\n      for(i = 0, l = fns.length; i < l; i++) {\n        if(fn === fns[i]) {\n          fns.splice(i, 1);\n          if (this._removeListener)\n            this.emit(\"removeListenerAny\", fn);\n          return this;\n        }\n      }\n    } else {\n      fns = this._all;\n      if (this._removeListener) {\n        for(i = 0, l = fns.length; i < l; i++)\n          this.emit(\"removeListenerAny\", fns[i]);\n      }\n      this._all = [];\n    }\n    return this;\n  };\n\n  EventEmitter.prototype.removeListener = EventEmitter.prototype.off;\n\n  EventEmitter.prototype.removeAllListeners = function (type) {\n    if (type === undefined) {\n      !this._events || init.call(this);\n      return this;\n    }\n\n    if (this.wildcard) {\n      var leafs = searchListenerTree.call(this, null, type, this.listenerTree, 0), leaf, i;\n      if (!leafs) return this;\n      for (i = 0; i < leafs.length; i++) {\n        leaf = leafs[i];\n        leaf._listeners = null;\n      }\n      this.listenerTree && recursivelyGarbageCollect(this.listenerTree);\n    } else if (this._events) {\n      this._events[type] = null;\n    }\n    return this;\n  };\n\n  EventEmitter.prototype.listeners = function (type) {\n    var _events = this._events;\n    var keys, listeners, allListeners;\n    var i;\n    var listenerTree;\n\n    if (type === undefined) {\n      if (this.wildcard) {\n        throw Error('event name required for wildcard emitter');\n      }\n\n      if (!_events) {\n        return [];\n      }\n\n      keys = ownKeys(_events);\n      i = keys.length;\n      allListeners = [];\n      while (i-- > 0) {\n        listeners = _events[keys[i]];\n        if (typeof listeners === 'function') {\n          allListeners.push(listeners);\n        } else {\n          allListeners.push.apply(allListeners, listeners);\n        }\n      }\n      return allListeners;\n    } else {\n      if (this.wildcard) {\n        listenerTree= this.listenerTree;\n        if(!listenerTree) return [];\n        var handlers = [];\n        var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n        searchListenerTree.call(this, handlers, ns, listenerTree, 0);\n        return handlers;\n      }\n\n      if (!_events) {\n        return [];\n      }\n\n      listeners = _events[type];\n\n      if (!listeners) {\n        return [];\n      }\n      return typeof listeners === 'function' ? [listeners] : listeners;\n    }\n  };\n\n  EventEmitter.prototype.eventNames = function(nsAsArray){\n    var _events= this._events;\n    return this.wildcard? collectTreeEvents.call(this, this.listenerTree, [], null, nsAsArray) : (_events? ownKeys(_events) : []);\n  };\n\n  EventEmitter.prototype.listenerCount = function(type) {\n    return this.listeners(type).length;\n  };\n\n  EventEmitter.prototype.hasListeners = function (type) {\n    if (this.wildcard) {\n      var handlers = [];\n      var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n      searchListenerTree.call(this, handlers, ns, this.listenerTree, 0);\n      return handlers.length > 0;\n    }\n\n    var _events = this._events;\n    var _all = this._all;\n\n    return !!(_all && _all.length || _events && (type === undefined ? ownKeys(_events).length : _events[type]));\n  };\n\n  EventEmitter.prototype.listenersAny = function() {\n\n    if(this._all) {\n      return this._all;\n    }\n    else {\n      return [];\n    }\n\n  };\n\n  EventEmitter.prototype.waitFor = function (event, options) {\n    var self = this;\n    var type = typeof options;\n    if (type === 'number') {\n      options = {timeout: options};\n    } else if (type === 'function') {\n      options = {filter: options};\n    }\n\n    options= resolveOptions(options, {\n      timeout: 0,\n      filter: undefined,\n      handleError: false,\n      Promise: Promise,\n      overload: false\n    }, {\n      filter: functionReducer,\n      Promise: constructorReducer\n    });\n\n    return makeCancelablePromise(options.Promise, function (resolve, reject, onCancel) {\n      function listener() {\n        var filter= options.filter;\n        if (filter && !filter.apply(self, arguments)) {\n          return;\n        }\n        self.off(event, listener);\n        if (options.handleError) {\n          var err = arguments[0];\n          err ? reject(err) : resolve(toArray.apply(null, arguments).slice(1));\n        } else {\n          resolve(toArray.apply(null, arguments));\n        }\n      }\n\n      onCancel(function(){\n        self.off(event, listener);\n      });\n\n      self._on(event, listener, false);\n    }, {\n      timeout: options.timeout,\n      overload: options.overload\n    })\n  };\n\n  function once(emitter, name, options) {\n    options= resolveOptions(options, {\n      Promise: Promise,\n      timeout: 0,\n      overload: false\n    }, {\n      Promise: constructorReducer\n    });\n\n    var _Promise= options.Promise;\n\n    return makeCancelablePromise(_Promise, function(resolve, reject, onCancel){\n      var handler;\n      if (typeof emitter.addEventListener === 'function') {\n        handler=  function () {\n          resolve(toArray.apply(null, arguments));\n        };\n\n        onCancel(function(){\n          emitter.removeEventListener(name, handler);\n        });\n\n        emitter.addEventListener(\n            name,\n            handler,\n            {once: true}\n        );\n        return;\n      }\n\n      var eventListener = function(){\n        errorListener && emitter.removeListener('error', errorListener);\n        resolve(toArray.apply(null, arguments));\n      };\n\n      var errorListener;\n\n      if (name !== 'error') {\n        errorListener = function (err){\n          emitter.removeListener(name, eventListener);\n          reject(err);\n        };\n\n        emitter.once('error', errorListener);\n      }\n\n      onCancel(function(){\n        errorListener && emitter.removeListener('error', errorListener);\n        emitter.removeListener(name, eventListener);\n      });\n\n      emitter.once(name, eventListener);\n    }, {\n      timeout: options.timeout,\n      overload: options.overload\n    });\n  }\n\n  var prototype= EventEmitter.prototype;\n\n  Object.defineProperties(EventEmitter, {\n    defaultMaxListeners: {\n      get: function () {\n        return prototype._maxListeners;\n      },\n      set: function (n) {\n        if (typeof n !== 'number' || n < 0 || Number.isNaN(n)) {\n          throw TypeError('n must be a non-negative number')\n        }\n        prototype._maxListeners = n;\n      },\n      enumerable: true\n    },\n    once: {\n      value: once,\n      writable: true,\n      configurable: true\n    }\n  });\n\n  Object.defineProperties(prototype, {\n      _maxListeners: {\n          value: defaultMaxListeners,\n          writable: true,\n          configurable: true\n      },\n      _observers: {value: null, writable: true, configurable: true}\n  });\n\n  if (true) {\n     // AMD. Register as an anonymous module.\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n      return EventEmitter;\n    }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else { var _global; }\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eventemitter2/lib/eventemitter2.js\n");

/***/ })

};
;