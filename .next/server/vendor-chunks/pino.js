"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino";
exports.ids = ["vendor-chunks/pino"];
exports.modules = {

/***/ "(ssr)/./node_modules/pino/lib/caller.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/caller.js ***!
  \*****************************************/
/***/ ((module) => {

eval("\n\nfunction noOpPrepareStackTrace (_, stack) {\n  return stack\n}\n\nmodule.exports = function getCallers () {\n  const originalPrepare = Error.prepareStackTrace\n  Error.prepareStackTrace = noOpPrepareStackTrace\n  const stack = new Error().stack\n  Error.prepareStackTrace = originalPrepare\n\n  if (!Array.isArray(stack)) {\n    return undefined\n  }\n\n  const entries = stack.slice(2)\n\n  const fileNames = []\n\n  for (const entry of entries) {\n    if (!entry) {\n      continue\n    }\n\n    fileNames.push(entry.getFileName())\n  }\n\n  return fileNames\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93ZXJhbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9waW5vL2xpYi9jYWxsZXIuanM/ZjFiNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuZnVuY3Rpb24gbm9PcFByZXBhcmVTdGFja1RyYWNlIChfLCBzdGFjaykge1xuICByZXR1cm4gc3RhY2tcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBnZXRDYWxsZXJzICgpIHtcbiAgY29uc3Qgb3JpZ2luYWxQcmVwYXJlID0gRXJyb3IucHJlcGFyZVN0YWNrVHJhY2VcbiAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBub09wUHJlcGFyZVN0YWNrVHJhY2VcbiAgY29uc3Qgc3RhY2sgPSBuZXcgRXJyb3IoKS5zdGFja1xuICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IG9yaWdpbmFsUHJlcGFyZVxuXG4gIGlmICghQXJyYXkuaXNBcnJheShzdGFjaykpIHtcbiAgICByZXR1cm4gdW5kZWZpbmVkXG4gIH1cblxuICBjb25zdCBlbnRyaWVzID0gc3RhY2suc2xpY2UoMilcblxuICBjb25zdCBmaWxlTmFtZXMgPSBbXVxuXG4gIGZvciAoY29uc3QgZW50cnkgb2YgZW50cmllcykge1xuICAgIGlmICghZW50cnkpIHtcbiAgICAgIGNvbnRpbnVlXG4gICAgfVxuXG4gICAgZmlsZU5hbWVzLnB1c2goZW50cnkuZ2V0RmlsZU5hbWUoKSlcbiAgfVxuXG4gIHJldHVybiBmaWxlTmFtZXNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/caller.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/deprecations.js":
/*!***********************************************!*\
  !*** ./node_modules/pino/lib/deprecations.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst warning = __webpack_require__(/*! process-warning */ \"(ssr)/./node_modules/process-warning/index.js\")()\nmodule.exports = warning\n\nconst warnName = 'PinoWarning'\n\nwarning.create(warnName, 'PINODEP008', 'prettyPrint is deprecated, look at https://github.com/pinojs/pino-pretty for alternatives.')\n\nwarning.create(warnName, 'PINODEP009', 'The use of pino.final is discouraged in Node.js v14+ and not required. It will be removed in the next major version')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvZGVwcmVjYXRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLHNFQUFpQjtBQUN6Qzs7QUFFQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dlcmFudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3Bpbm8vbGliL2RlcHJlY2F0aW9ucy5qcz80MTg4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCB3YXJuaW5nID0gcmVxdWlyZSgncHJvY2Vzcy13YXJuaW5nJykoKVxubW9kdWxlLmV4cG9ydHMgPSB3YXJuaW5nXG5cbmNvbnN0IHdhcm5OYW1lID0gJ1Bpbm9XYXJuaW5nJ1xuXG53YXJuaW5nLmNyZWF0ZSh3YXJuTmFtZSwgJ1BJTk9ERVAwMDgnLCAncHJldHR5UHJpbnQgaXMgZGVwcmVjYXRlZCwgbG9vayBhdCBodHRwczovL2dpdGh1Yi5jb20vcGlub2pzL3Bpbm8tcHJldHR5IGZvciBhbHRlcm5hdGl2ZXMuJylcblxud2FybmluZy5jcmVhdGUod2Fybk5hbWUsICdQSU5PREVQMDA5JywgJ1RoZSB1c2Ugb2YgcGluby5maW5hbCBpcyBkaXNjb3VyYWdlZCBpbiBOb2RlLmpzIHYxNCsgYW5kIG5vdCByZXF1aXJlZC4gSXQgd2lsbCBiZSByZW1vdmVkIGluIHRoZSBuZXh0IG1ham9yIHZlcnNpb24nKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/deprecations.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/levels.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/levels.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/* eslint no-prototype-builtins: 0 */\nconst {\n  lsCacheSym,\n  levelValSym,\n  useOnlyCustomLevelsSym,\n  streamSym,\n  formattersSym,\n  hooksSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { noop, genLog } = __webpack_require__(/*! ./tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\n\nconst levels = {\n  trace: 10,\n  debug: 20,\n  info: 30,\n  warn: 40,\n  error: 50,\n  fatal: 60\n}\nconst levelMethods = {\n  fatal: (hook) => {\n    const logFatal = genLog(levels.fatal, hook)\n    return function (...args) {\n      const stream = this[streamSym]\n      logFatal.call(this, ...args)\n      if (typeof stream.flushSync === 'function') {\n        try {\n          stream.flushSync()\n        } catch (e) {\n          // https://github.com/pinojs/pino/pull/740#discussion_r346788313\n        }\n      }\n    }\n  },\n  error: (hook) => genLog(levels.error, hook),\n  warn: (hook) => genLog(levels.warn, hook),\n  info: (hook) => genLog(levels.info, hook),\n  debug: (hook) => genLog(levels.debug, hook),\n  trace: (hook) => genLog(levels.trace, hook)\n}\n\nconst nums = Object.keys(levels).reduce((o, k) => {\n  o[levels[k]] = k\n  return o\n}, {})\n\nconst initialLsCache = Object.keys(nums).reduce((o, k) => {\n  o[k] = '{\"level\":' + Number(k)\n  return o\n}, {})\n\nfunction genLsCache (instance) {\n  const formatter = instance[formattersSym].level\n  const { labels } = instance.levels\n  const cache = {}\n  for (const label in labels) {\n    const level = formatter(labels[label], Number(label))\n    cache[label] = JSON.stringify(level).slice(0, -1)\n  }\n  instance[lsCacheSym] = cache\n  return instance\n}\n\nfunction isStandardLevel (level, useOnlyCustomLevels) {\n  if (useOnlyCustomLevels) {\n    return false\n  }\n\n  switch (level) {\n    case 'fatal':\n    case 'error':\n    case 'warn':\n    case 'info':\n    case 'debug':\n    case 'trace':\n      return true\n    default:\n      return false\n  }\n}\n\nfunction setLevel (level) {\n  const { labels, values } = this.levels\n  if (typeof level === 'number') {\n    if (labels[level] === undefined) throw Error('unknown level value' + level)\n    level = labels[level]\n  }\n  if (values[level] === undefined) throw Error('unknown level ' + level)\n  const preLevelVal = this[levelValSym]\n  const levelVal = this[levelValSym] = values[level]\n  const useOnlyCustomLevelsVal = this[useOnlyCustomLevelsSym]\n  const hook = this[hooksSym].logMethod\n\n  for (const key in values) {\n    if (levelVal > values[key]) {\n      this[key] = noop\n      continue\n    }\n    this[key] = isStandardLevel(key, useOnlyCustomLevelsVal) ? levelMethods[key](hook) : genLog(values[key], hook)\n  }\n\n  this.emit(\n    'level-change',\n    level,\n    levelVal,\n    labels[preLevelVal],\n    preLevelVal\n  )\n}\n\nfunction getLevel (level) {\n  const { levels, levelVal } = this\n  // protection against potential loss of Pino scope from serializers (edge case with circular refs - https://github.com/pinojs/pino/issues/833)\n  return (levels && levels.labels) ? levels.labels[levelVal] : ''\n}\n\nfunction isLevelEnabled (logLevel) {\n  const { values } = this.levels\n  const logLevelVal = values[logLevel]\n  return logLevelVal !== undefined && (logLevelVal >= this[levelValSym])\n}\n\nfunction mappings (customLevels = null, useOnlyCustomLevels = false) {\n  const customNums = customLevels\n    /* eslint-disable */\n    ? Object.keys(customLevels).reduce((o, k) => {\n        o[customLevels[k]] = k\n        return o\n      }, {})\n    : null\n    /* eslint-enable */\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { Infinity: { value: 'silent' } }),\n    useOnlyCustomLevels ? null : nums,\n    customNums\n  )\n  const values = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : levels,\n    customLevels\n  )\n  return { labels, values }\n}\n\nfunction assertDefaultLevelFound (defaultLevel, customLevels, useOnlyCustomLevels) {\n  if (typeof defaultLevel === 'number') {\n    const values = [].concat(\n      Object.keys(customLevels || {}).map(key => customLevels[key]),\n      useOnlyCustomLevels ? [] : Object.keys(nums).map(level => +level),\n      Infinity\n    )\n    if (!values.includes(defaultLevel)) {\n      throw Error(`default level:${defaultLevel} must be included in custom levels`)\n    }\n    return\n  }\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : levels,\n    customLevels\n  )\n  if (!(defaultLevel in labels)) {\n    throw Error(`default level:${defaultLevel} must be included in custom levels`)\n  }\n}\n\nfunction assertNoLevelCollisions (levels, customLevels) {\n  const { labels, values } = levels\n  for (const k in customLevels) {\n    if (k in values) {\n      throw Error('levels cannot be overridden')\n    }\n    if (customLevels[k] in labels) {\n      throw Error('pre-existing level values cannot be used for new levels')\n    }\n  }\n}\n\nmodule.exports = {\n  initialLsCache,\n  genLsCache,\n  levelMethods,\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  levels,\n  assertNoLevelCollisions,\n  assertDefaultLevelFound\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/levels.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/meta.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/meta.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { version } = __webpack_require__(/*! ../package.json */ \"(ssr)/./node_modules/pino/package.json\")\n\nmodule.exports = { version }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbWV0YS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixRQUFRLFVBQVUsRUFBRSxtQkFBTyxDQUFDLCtEQUFpQjs7QUFFN0MsbUJBQW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbWV0YS5qcz80YjZhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCB7IHZlcnNpb24gfSA9IHJlcXVpcmUoJy4uL3BhY2thZ2UuanNvbicpXG5cbm1vZHVsZS5leHBvcnRzID0geyB2ZXJzaW9uIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/meta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/multistream.js":
/*!**********************************************!*\
  !*** ./node_modules/pino/lib/multistream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst metadata = Symbol.for('pino.metadata')\nconst { levels } = __webpack_require__(/*! ./levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\n\nconst defaultLevels = Object.create(levels)\ndefaultLevels.silent = Infinity\n\nconst DEFAULT_INFO_LEVEL = levels.info\n\nfunction multistream (streamsArray, opts) {\n  let counter = 0\n  streamsArray = streamsArray || []\n  opts = opts || { dedupe: false }\n\n  let levels = defaultLevels\n  if (opts.levels && typeof opts.levels === 'object') {\n    levels = opts.levels\n  }\n\n  const res = {\n    write,\n    add,\n    flushSync,\n    end,\n    minLevel: 0,\n    streams: [],\n    clone,\n    [metadata]: true\n  }\n\n  if (Array.isArray(streamsArray)) {\n    streamsArray.forEach(add, res)\n  } else {\n    add.call(res, streamsArray)\n  }\n\n  // clean this object up\n  // or it will stay allocated forever\n  // as it is closed on the following closures\n  streamsArray = null\n\n  return res\n\n  // we can exit early because the streams are ordered by level\n  function write (data) {\n    let dest\n    const level = this.lastLevel\n    const { streams } = this\n    let stream\n    for (let i = 0; i < streams.length; i++) {\n      dest = streams[i]\n      if (dest.level <= level) {\n        stream = dest.stream\n        if (stream[metadata]) {\n          const { lastTime, lastMsg, lastObj, lastLogger } = this\n          stream.lastLevel = level\n          stream.lastTime = lastTime\n          stream.lastMsg = lastMsg\n          stream.lastObj = lastObj\n          stream.lastLogger = lastLogger\n        }\n        if (!opts.dedupe || dest.level === level) {\n          stream.write(data)\n        }\n      } else {\n        break\n      }\n    }\n  }\n\n  function flushSync () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n    }\n  }\n\n  function add (dest) {\n    if (!dest) {\n      return res\n    }\n\n    // Check that dest implements either StreamEntry or DestinationStream\n    const isStream = typeof dest.write === 'function' || dest.stream\n    const stream_ = dest.write ? dest : dest.stream\n    // This is necessary to provide a meaningful error message, otherwise it throws somewhere inside write()\n    if (!isStream) {\n      throw Error('stream object needs to implement either StreamEntry or DestinationStream interface')\n    }\n\n    const { streams } = this\n\n    let level\n    if (typeof dest.levelVal === 'number') {\n      level = dest.levelVal\n    } else if (typeof dest.level === 'string') {\n      level = levels[dest.level]\n    } else if (typeof dest.level === 'number') {\n      level = dest.level\n    } else {\n      level = DEFAULT_INFO_LEVEL\n    }\n\n    const dest_ = {\n      stream: stream_,\n      level,\n      levelVal: undefined,\n      id: counter++\n    }\n\n    streams.unshift(dest_)\n    streams.sort(compareByLevel)\n\n    this.minLevel = streams[0].level\n\n    return res\n  }\n\n  function end () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n      stream.end()\n    }\n  }\n\n  function clone (level) {\n    const streams = new Array(this.streams.length)\n\n    for (let i = 0; i < streams.length; i++) {\n      streams[i] = {\n        level: level,\n        stream: this.streams[i].stream\n      }\n    }\n\n    return {\n      write,\n      add,\n      minLevel: level,\n      streams,\n      clone,\n      flushSync,\n      [metadata]: true\n    }\n  }\n}\n\nfunction compareByLevel (a, b) {\n  return a.level - b.level\n}\n\nmodule.exports = multistream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/multistream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/proto.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/proto.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst {\n  lsCacheSym,\n  levelValSym,\n  setLevelSym,\n  getLevelSym,\n  chindingsSym,\n  parsedChindingsSym,\n  mixinSym,\n  asJsonSym,\n  writeSym,\n  mixinMergeStrategySym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  serializersSym,\n  formattersSym,\n  useOnlyCustomLevelsSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  stringifySym,\n  formatOptsSym,\n  stringifiersSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst {\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  initialLsCache,\n  genLsCache,\n  assertNoLevelCollisions\n} = __webpack_require__(/*! ./levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\nconst {\n  asChindings,\n  asJson,\n  buildFormatters,\n  stringify\n} = __webpack_require__(/*! ./tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\nconst {\n  version\n} = __webpack_require__(/*! ./meta */ \"(ssr)/./node_modules/pino/lib/meta.js\")\nconst redaction = __webpack_require__(/*! ./redaction */ \"(ssr)/./node_modules/pino/lib/redaction.js\")\n\n// note: use of class is satirical\n// https://github.com/pinojs/pino/pull/433#pullrequestreview-127703127\nconst constructor = class Pino {}\nconst prototype = {\n  constructor,\n  child,\n  bindings,\n  setBindings,\n  flush,\n  isLevelEnabled,\n  version,\n  get level () { return this[getLevelSym]() },\n  set level (lvl) { this[setLevelSym](lvl) },\n  get levelVal () { return this[levelValSym] },\n  set levelVal (n) { throw Error('levelVal is read-only') },\n  [lsCacheSym]: initialLsCache,\n  [writeSym]: write,\n  [asJsonSym]: asJson,\n  [getLevelSym]: getLevel,\n  [setLevelSym]: setLevel\n}\n\nObject.setPrototypeOf(prototype, EventEmitter.prototype)\n\n// exporting and consuming the prototype object using factory pattern fixes scoping issues with getters when serializing\nmodule.exports = function () {\n  return Object.create(prototype)\n}\n\nconst resetChildingsFormatter = bindings => bindings\nfunction child (bindings, options) {\n  if (!bindings) {\n    throw Error('missing bindings for child Pino')\n  }\n  options = options || {} // default options to empty object\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const instance = Object.create(this)\n\n  if (options.hasOwnProperty('serializers') === true) {\n    instance[serializersSym] = Object.create(null)\n\n    for (const k in serializers) {\n      instance[serializersSym][k] = serializers[k]\n    }\n    const parentSymbols = Object.getOwnPropertySymbols(serializers)\n    /* eslint no-var: off */\n    for (var i = 0; i < parentSymbols.length; i++) {\n      const ks = parentSymbols[i]\n      instance[serializersSym][ks] = serializers[ks]\n    }\n\n    for (const bk in options.serializers) {\n      instance[serializersSym][bk] = options.serializers[bk]\n    }\n    const bindingsSymbols = Object.getOwnPropertySymbols(options.serializers)\n    for (var bi = 0; bi < bindingsSymbols.length; bi++) {\n      const bks = bindingsSymbols[bi]\n      instance[serializersSym][bks] = options.serializers[bks]\n    }\n  } else instance[serializersSym] = serializers\n  if (options.hasOwnProperty('formatters')) {\n    const { level, bindings: chindings, log } = options.formatters\n    instance[formattersSym] = buildFormatters(\n      level || formatters.level,\n      chindings || resetChildingsFormatter,\n      log || formatters.log\n    )\n  } else {\n    instance[formattersSym] = buildFormatters(\n      formatters.level,\n      resetChildingsFormatter,\n      formatters.log\n    )\n  }\n  if (options.hasOwnProperty('customLevels') === true) {\n    assertNoLevelCollisions(this.levels, options.customLevels)\n    instance.levels = mappings(options.customLevels, instance[useOnlyCustomLevelsSym])\n    genLsCache(instance)\n  }\n\n  // redact must place before asChindings and only replace if exist\n  if ((typeof options.redact === 'object' && options.redact !== null) || Array.isArray(options.redact)) {\n    instance.redact = options.redact // replace redact directly\n    const stringifiers = redaction(instance.redact, stringify)\n    const formatOpts = { stringify: stringifiers[redactFmtSym] }\n    instance[stringifySym] = stringify\n    instance[stringifiersSym] = stringifiers\n    instance[formatOptsSym] = formatOpts\n  }\n\n  instance[chindingsSym] = asChindings(instance, bindings)\n  const childLevel = options.level || this.level\n  instance[setLevelSym](childLevel)\n\n  return instance\n}\n\nfunction bindings () {\n  const chindings = this[chindingsSym]\n  const chindingsJson = `{${chindings.substr(1)}}` // at least contains ,\"pid\":7068,\"hostname\":\"myMac\"\n  const bindingsFromJson = JSON.parse(chindingsJson)\n  delete bindingsFromJson.pid\n  delete bindingsFromJson.hostname\n  return bindingsFromJson\n}\n\nfunction setBindings (newBindings) {\n  const chindings = asChindings(this, newBindings)\n  this[chindingsSym] = chindings\n  delete this[parsedChindingsSym]\n}\n\n/**\n * Default strategy for creating `mergeObject` from arguments and the result from `mixin()`.\n * Fields from `mergeObject` have higher priority in this strategy.\n *\n * @param {Object} mergeObject The object a user has supplied to the logging function.\n * @param {Object} mixinObject The result of the `mixin` method.\n * @return {Object}\n */\nfunction defaultMixinMergeStrategy (mergeObject, mixinObject) {\n  return Object.assign(mixinObject, mergeObject)\n}\n\nfunction write (_obj, msg, num) {\n  const t = this[timeSym]()\n  const mixin = this[mixinSym]\n  const mixinMergeStrategy = this[mixinMergeStrategySym] || defaultMixinMergeStrategy\n  let obj\n\n  if (_obj === undefined || _obj === null) {\n    obj = {}\n  } else if (_obj instanceof Error) {\n    obj = { err: _obj }\n    if (msg === undefined) {\n      msg = _obj.message\n    }\n  } else {\n    obj = _obj\n    if (msg === undefined && _obj.err) {\n      msg = _obj.err.message\n    }\n  }\n\n  if (mixin) {\n    obj = mixinMergeStrategy(obj, mixin(obj, num))\n  }\n\n  const s = this[asJsonSym](obj, msg, num, t)\n\n  const stream = this[streamSym]\n  if (stream[needsMetadataGsym] === true) {\n    stream.lastLevel = num\n    stream.lastObj = obj\n    stream.lastMsg = msg\n    stream.lastTime = t.slice(this[timeSliceIndexSym])\n    stream.lastLogger = this // for child loggers\n  }\n  stream.write(s)\n}\n\nfunction noop () {}\n\nfunction flush () {\n  const stream = this[streamSym]\n  if ('flush' in stream) stream.flush(noop)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/proto.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/redaction.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/redaction.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst fastRedact = __webpack_require__(/*! fast-redact */ \"(ssr)/./node_modules/fast-redact/index.js\")\nconst { redactFmtSym, wildcardFirstSym } = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { rx, validator } = fastRedact\n\nconst validate = validator({\n  ERR_PATHS_MUST_BE_STRINGS: () => 'pino – redacted paths must be strings',\n  ERR_INVALID_PATH: (s) => `pino – redact paths array contains an invalid path (${s})`\n})\n\nconst CENSOR = '[Redacted]'\nconst strict = false // TODO should this be configurable?\n\nfunction redaction (opts, serialize) {\n  const { paths, censor } = handle(opts)\n\n  const shape = paths.reduce((o, str) => {\n    rx.lastIndex = 0\n    const first = rx.exec(str)\n    const next = rx.exec(str)\n\n    // ns is the top-level path segment, brackets + quoting removed.\n    let ns = first[1] !== undefined\n      ? first[1].replace(/^(?:\"|'|`)(.*)(?:\"|'|`)$/, '$1')\n      : first[0]\n\n    if (ns === '*') {\n      ns = wildcardFirstSym\n    }\n\n    // top level key:\n    if (next === null) {\n      o[ns] = null\n      return o\n    }\n\n    // path with at least two segments:\n    // if ns is already redacted at the top level, ignore lower level redactions\n    if (o[ns] === null) {\n      return o\n    }\n\n    const { index } = next\n    const nextPath = `${str.substr(index, str.length - 1)}`\n\n    o[ns] = o[ns] || []\n\n    // shape is a mix of paths beginning with literal values and wildcard\n    // paths [ \"a.b.c\", \"*.b.z\" ] should reduce to a shape of\n    // { \"a\": [ \"b.c\", \"b.z\" ], *: [ \"b.z\" ] }\n    // note: \"b.z\" is in both \"a\" and * arrays because \"a\" matches the wildcard.\n    // (* entry has wildcardFirstSym as key)\n    if (ns !== wildcardFirstSym && o[ns].length === 0) {\n      // first time ns's get all '*' redactions so far\n      o[ns].push(...(o[wildcardFirstSym] || []))\n    }\n\n    if (ns === wildcardFirstSym) {\n      // new * path gets added to all previously registered literal ns's.\n      Object.keys(o).forEach(function (k) {\n        if (o[k]) {\n          o[k].push(nextPath)\n        }\n      })\n    }\n\n    o[ns].push(nextPath)\n    return o\n  }, {})\n\n  // the redactor assigned to the format symbol key\n  // provides top level redaction for instances where\n  // an object is interpolated into the msg string\n  const result = {\n    [redactFmtSym]: fastRedact({ paths, censor, serialize, strict })\n  }\n\n  const topCensor = (...args) => {\n    return typeof censor === 'function' ? serialize(censor(...args)) : serialize(censor)\n  }\n\n  return [...Object.keys(shape), ...Object.getOwnPropertySymbols(shape)].reduce((o, k) => {\n    // top level key:\n    if (shape[k] === null) {\n      o[k] = (value) => topCensor(value, [k])\n    } else {\n      const wrappedCensor = typeof censor === 'function'\n        ? (value, path) => {\n            return censor(value, [k, ...path])\n          }\n        : censor\n      o[k] = fastRedact({\n        paths: shape[k],\n        censor: wrappedCensor,\n        serialize,\n        strict\n      })\n    }\n    return o\n  }, result)\n}\n\nfunction handle (opts) {\n  if (Array.isArray(opts)) {\n    opts = { paths: opts, censor: CENSOR }\n    validate(opts)\n    return opts\n  }\n  let { paths, censor = CENSOR, remove } = opts\n  if (Array.isArray(paths) === false) { throw Error('pino – redact must contain an array of strings') }\n  if (remove === true) censor = undefined\n  validate({ paths, censor })\n\n  return { paths, censor }\n}\n\nmodule.exports = redaction\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/redaction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/symbols.js":
/*!******************************************!*\
  !*** ./node_modules/pino/lib/symbols.js ***!
  \******************************************/
/***/ ((module) => {

eval("\n\nconst setLevelSym = Symbol('pino.setLevel')\nconst getLevelSym = Symbol('pino.getLevel')\nconst levelValSym = Symbol('pino.levelVal')\nconst useLevelLabelsSym = Symbol('pino.useLevelLabels')\nconst useOnlyCustomLevelsSym = Symbol('pino.useOnlyCustomLevels')\nconst mixinSym = Symbol('pino.mixin')\n\nconst lsCacheSym = Symbol('pino.lsCache')\nconst chindingsSym = Symbol('pino.chindings')\nconst parsedChindingsSym = Symbol('pino.parsedChindings')\n\nconst asJsonSym = Symbol('pino.asJson')\nconst writeSym = Symbol('pino.write')\nconst redactFmtSym = Symbol('pino.redactFmt')\n\nconst timeSym = Symbol('pino.time')\nconst timeSliceIndexSym = Symbol('pino.timeSliceIndex')\nconst streamSym = Symbol('pino.stream')\nconst stringifySym = Symbol('pino.stringify')\nconst stringifySafeSym = Symbol('pino.stringifySafe')\nconst stringifiersSym = Symbol('pino.stringifiers')\nconst endSym = Symbol('pino.end')\nconst formatOptsSym = Symbol('pino.formatOpts')\nconst messageKeySym = Symbol('pino.messageKey')\nconst nestedKeySym = Symbol('pino.nestedKey')\nconst nestedKeyStrSym = Symbol('pino.nestedKeyStr')\nconst mixinMergeStrategySym = Symbol('pino.mixinMergeStrategy')\n\nconst wildcardFirstSym = Symbol('pino.wildcardFirst')\n\n// public symbols, no need to use the same pino\n// version for these\nconst serializersSym = Symbol.for('pino.serializers')\nconst formattersSym = Symbol.for('pino.formatters')\nconst hooksSym = Symbol.for('pino.hooks')\nconst needsMetadataGsym = Symbol.for('pino.metadata')\n\nmodule.exports = {\n  setLevelSym,\n  getLevelSym,\n  levelValSym,\n  useLevelLabelsSym,\n  mixinSym,\n  lsCacheSym,\n  chindingsSym,\n  parsedChindingsSym,\n  asJsonSym,\n  writeSym,\n  serializersSym,\n  redactFmtSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  nestedKeySym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/symbols.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/time.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/time.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nconst nullTime = () => ''\n\nconst epochTime = () => `,\"time\":${Date.now()}`\n\nconst unixTime = () => `,\"time\":${Math.round(Date.now() / 1000.0)}`\n\nconst isoTime = () => `,\"time\":\"${new Date(Date.now()).toISOString()}\"` // using Date.now() for testability\n\nmodule.exports = { nullTime, epochTime, unixTime, isoTime }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxtQ0FBbUMsV0FBVzs7QUFFOUMsa0NBQWtDLGdDQUFnQzs7QUFFbEUsa0NBQWtDLG1DQUFtQzs7QUFFckUsbUJBQW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2VyYW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdGltZS5qcz8wMzI3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBudWxsVGltZSA9ICgpID0+ICcnXG5cbmNvbnN0IGVwb2NoVGltZSA9ICgpID0+IGAsXCJ0aW1lXCI6JHtEYXRlLm5vdygpfWBcblxuY29uc3QgdW5peFRpbWUgPSAoKSA9PiBgLFwidGltZVwiOiR7TWF0aC5yb3VuZChEYXRlLm5vdygpIC8gMTAwMC4wKX1gXG5cbmNvbnN0IGlzb1RpbWUgPSAoKSA9PiBgLFwidGltZVwiOlwiJHtuZXcgRGF0ZShEYXRlLm5vdygpKS50b0lTT1N0cmluZygpfVwiYCAvLyB1c2luZyBEYXRlLm5vdygpIGZvciB0ZXN0YWJpbGl0eVxuXG5tb2R1bGUuZXhwb3J0cyA9IHsgbnVsbFRpbWUsIGVwb2NoVGltZSwgdW5peFRpbWUsIGlzb1RpbWUgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/tools.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/tools.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst format = __webpack_require__(/*! quick-format-unescaped */ \"(ssr)/./node_modules/quick-format-unescaped/index.js\")\nconst { mapHttpRequest, mapHttpResponse } = __webpack_require__(/*! pino-std-serializers */ \"(ssr)/./node_modules/pino-std-serializers/index.js\")\nconst SonicBoom = __webpack_require__(/*! sonic-boom */ \"(ssr)/./node_modules/sonic-boom/index.js\")\nconst warning = __webpack_require__(/*! ./deprecations */ \"(ssr)/./node_modules/pino/lib/deprecations.js\")\nconst {\n  lsCacheSym,\n  chindingsSym,\n  parsedChindingsSym,\n  writeSym,\n  serializersSym,\n  formatOptsSym,\n  endSym,\n  stringifiersSym,\n  stringifySym,\n  stringifySafeSym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  streamSym,\n  nestedKeySym,\n  formattersSym,\n  messageKeySym,\n  nestedKeyStrSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { isMainThread } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst transport = __webpack_require__(/*! ./transport */ \"(ssr)/./node_modules/pino/lib/transport.js\")\n\nfunction noop () {}\n\nfunction genLog (level, hook) {\n  if (!hook) return LOG\n\n  return function hookWrappedLog (...args) {\n    hook.call(this, args, LOG, level)\n  }\n\n  function LOG (o, ...n) {\n    if (typeof o === 'object') {\n      let msg = o\n      if (o !== null) {\n        if (o.method && o.headers && o.socket) {\n          o = mapHttpRequest(o)\n        } else if (typeof o.setHeader === 'function') {\n          o = mapHttpResponse(o)\n        }\n      }\n      let formatParams\n      if (msg === null && n.length === 0) {\n        formatParams = [null]\n      } else {\n        msg = n.shift()\n        formatParams = n\n      }\n      this[writeSym](o, format(msg, formatParams, this[formatOptsSym]), level)\n    } else {\n      this[writeSym](null, format(o, n, this[formatOptsSym]), level)\n    }\n  }\n}\n\n// magically escape strings for json\n// relying on their charCodeAt\n// everything below 32 needs JSON.stringify()\n// 34 and 92 happens all the time, so we\n// have a fast case for them\nfunction asString (str) {\n  let result = ''\n  let last = 0\n  let found = false\n  let point = 255\n  const l = str.length\n  if (l > 100) {\n    return JSON.stringify(str)\n  }\n  for (var i = 0; i < l && point >= 32; i++) {\n    point = str.charCodeAt(i)\n    if (point === 34 || point === 92) {\n      result += str.slice(last, i) + '\\\\'\n      last = i\n      found = true\n    }\n  }\n  if (!found) {\n    result = str\n  } else {\n    result += str.slice(last)\n  }\n  return point < 32 ? JSON.stringify(str) : '\"' + result + '\"'\n}\n\nfunction asJson (obj, msg, num, time) {\n  const stringify = this[stringifySym]\n  const stringifySafe = this[stringifySafeSym]\n  const stringifiers = this[stringifiersSym]\n  const end = this[endSym]\n  const chindings = this[chindingsSym]\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const messageKey = this[messageKeySym]\n  let data = this[lsCacheSym][num] + time\n\n  // we need the child bindings added to the output first so instance logged\n  // objects can take precedence when JSON.parse-ing the resulting log line\n  data = data + chindings\n\n  let value\n  if (formatters.log) {\n    obj = formatters.log(obj)\n  }\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  let propStr = ''\n  for (const key in obj) {\n    value = obj[key]\n    if (Object.prototype.hasOwnProperty.call(obj, key) && value !== undefined) {\n      value = serializers[key] ? serializers[key](value) : value\n\n      const stringifier = stringifiers[key] || wildcardStringifier\n\n      switch (typeof value) {\n        case 'undefined':\n        case 'function':\n          continue\n        case 'number':\n          /* eslint no-fallthrough: \"off\" */\n          if (Number.isFinite(value) === false) {\n            value = null\n          }\n        // this case explicitly falls through to the next one\n        case 'boolean':\n          if (stringifier) value = stringifier(value)\n          break\n        case 'string':\n          value = (stringifier || asString)(value)\n          break\n        default:\n          value = (stringifier || stringify)(value, stringifySafe)\n      }\n      if (value === undefined) continue\n      propStr += ',\"' + key + '\":' + value\n    }\n  }\n\n  let msgStr = ''\n  if (msg !== undefined) {\n    value = serializers[messageKey] ? serializers[messageKey](msg) : msg\n    const stringifier = stringifiers[messageKey] || wildcardStringifier\n\n    switch (typeof value) {\n      case 'function':\n        break\n      case 'number':\n        /* eslint no-fallthrough: \"off\" */\n        if (Number.isFinite(value) === false) {\n          value = null\n        }\n      // this case explicitly falls through to the next one\n      case 'boolean':\n        if (stringifier) value = stringifier(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      case 'string':\n        value = (stringifier || asString)(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      default:\n        value = (stringifier || stringify)(value, stringifySafe)\n        msgStr = ',\"' + messageKey + '\":' + value\n    }\n  }\n\n  if (this[nestedKeySym] && propStr) {\n    // place all the obj properties under the specified key\n    // the nested key is already formatted from the constructor\n    return data + this[nestedKeyStrSym] + propStr.slice(1) + '}' + msgStr + end\n  } else {\n    return data + propStr + msgStr + end\n  }\n}\n\nfunction asChindings (instance, bindings) {\n  let value\n  let data = instance[chindingsSym]\n  const stringify = instance[stringifySym]\n  const stringifySafe = instance[stringifySafeSym]\n  const stringifiers = instance[stringifiersSym]\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  const serializers = instance[serializersSym]\n  const formatter = instance[formattersSym].bindings\n  bindings = formatter(bindings)\n\n  for (const key in bindings) {\n    value = bindings[key]\n    const valid = key !== 'level' &&\n      key !== 'serializers' &&\n      key !== 'formatters' &&\n      key !== 'customLevels' &&\n      bindings.hasOwnProperty(key) &&\n      value !== undefined\n    if (valid === true) {\n      value = serializers[key] ? serializers[key](value) : value\n      value = (stringifiers[key] || wildcardStringifier || stringify)(value, stringifySafe)\n      if (value === undefined) continue\n      data += ',\"' + key + '\":' + value\n    }\n  }\n  return data\n}\n\nfunction getPrettyStream (opts, prettifier, dest, instance) {\n  if (prettifier && typeof prettifier === 'function') {\n    prettifier = prettifier.bind(instance)\n    return prettifierMetaWrapper(prettifier(opts), dest, opts)\n  }\n  try {\n    const prettyFactory = (__webpack_require__(/*! pino-pretty */ \"(ssr)/./node_modules/pino-pretty/index.js\").prettyFactory)\n    prettyFactory.asMetaWrapper = prettifierMetaWrapper\n    return prettifierMetaWrapper(prettyFactory(opts), dest, opts)\n  } catch (e) {\n    if (e.message.startsWith(\"Cannot find module 'pino-pretty'\")) {\n      throw Error('Missing `pino-pretty` module: `pino-pretty` must be installed separately')\n    };\n    throw e\n  }\n}\n\nfunction prettifierMetaWrapper (pretty, dest, opts) {\n  opts = Object.assign({ suppressFlushSyncWarning: false }, opts)\n  let warned = false\n  return {\n    [needsMetadataGsym]: true,\n    lastLevel: 0,\n    lastMsg: null,\n    lastObj: null,\n    lastLogger: null,\n    flushSync () {\n      if (opts.suppressFlushSyncWarning || warned) {\n        return\n      }\n      warned = true\n      setMetadataProps(dest, this)\n      dest.write(pretty(Object.assign({\n        level: 40, // warn\n        msg: 'pino.final with prettyPrint does not support flushing',\n        time: Date.now()\n      }, this.chindings())))\n    },\n    chindings () {\n      const lastLogger = this.lastLogger\n      let chindings = null\n\n      // protection against flushSync being called before logging\n      // anything\n      if (!lastLogger) {\n        return null\n      }\n\n      if (lastLogger.hasOwnProperty(parsedChindingsSym)) {\n        chindings = lastLogger[parsedChindingsSym]\n      } else {\n        chindings = JSON.parse('{' + lastLogger[chindingsSym].substr(1) + '}')\n        lastLogger[parsedChindingsSym] = chindings\n      }\n\n      return chindings\n    },\n    write (chunk) {\n      const lastLogger = this.lastLogger\n      const chindings = this.chindings()\n\n      let time = this.lastTime\n\n      /* istanbul ignore next */\n      if (typeof time === 'number') {\n        // do nothing!\n      } else if (time.match(/^\\d+/)) {\n        time = parseInt(time)\n      } else {\n        time = time.slice(1, -1)\n      }\n\n      const lastObj = this.lastObj\n      const lastMsg = this.lastMsg\n      const errorProps = null\n\n      const formatters = lastLogger[formattersSym]\n      const formattedObj = formatters.log ? formatters.log(lastObj) : lastObj\n\n      const messageKey = lastLogger[messageKeySym]\n      if (lastMsg && formattedObj && !Object.prototype.hasOwnProperty.call(formattedObj, messageKey)) {\n        formattedObj[messageKey] = lastMsg\n      }\n\n      const obj = Object.assign({\n        level: this.lastLevel,\n        time\n      }, formattedObj, errorProps)\n\n      const serializers = lastLogger[serializersSym]\n      const keys = Object.keys(serializers)\n\n      for (var i = 0; i < keys.length; i++) {\n        const key = keys[i]\n        if (obj[key] !== undefined) {\n          obj[key] = serializers[key](obj[key])\n        }\n      }\n\n      for (const key in chindings) {\n        if (!obj.hasOwnProperty(key)) {\n          obj[key] = chindings[key]\n        }\n      }\n\n      const stringifiers = lastLogger[stringifiersSym]\n      const redact = stringifiers[redactFmtSym]\n\n      const formatted = pretty(typeof redact === 'function' ? redact(obj) : obj)\n      if (formatted === undefined) return\n\n      setMetadataProps(dest, this)\n      dest.write(formatted)\n    }\n  }\n}\n\nfunction hasBeenTampered (stream) {\n  return stream.write !== stream.constructor.prototype.write\n}\n\nfunction buildSafeSonicBoom (opts) {\n  const stream = new SonicBoom(opts)\n  stream.on('error', filterBrokenPipe)\n  // if we are sync: false, we must flush on exit\n  if (!opts.sync && isMainThread) {\n    setupOnExit(stream)\n  }\n  return stream\n\n  function filterBrokenPipe (err) {\n    // TODO verify on Windows\n    if (err.code === 'EPIPE') {\n      // If we get EPIPE, we should stop logging here\n      // however we have no control to the consumer of\n      // SonicBoom, so we just overwrite the write method\n      stream.write = noop\n      stream.end = noop\n      stream.flushSync = noop\n      stream.destroy = noop\n      return\n    }\n    stream.removeListener('error', filterBrokenPipe)\n    stream.emit('error', err)\n  }\n}\n\nfunction setupOnExit (stream) {\n  /* istanbul ignore next */\n  if (global.WeakRef && global.WeakMap && global.FinalizationRegistry) {\n    // This is leak free, it does not leave event handlers\n    const onExit = __webpack_require__(/*! on-exit-leak-free */ \"(ssr)/./node_modules/on-exit-leak-free/index.js\")\n\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  }\n}\n\nfunction autoEnd (stream, eventName) {\n  // This check is needed only on some platforms\n  /* istanbul ignore next */\n  if (stream.destroyed) {\n    return\n  }\n\n  if (eventName === 'beforeExit') {\n    // We still have an event loop, let's use it\n    stream.flush()\n    stream.on('drain', function () {\n      stream.end()\n    })\n  } else {\n    // We do not have an event loop, so flush synchronously\n    stream.flushSync()\n  }\n}\n\nfunction createArgsNormalizer (defaultOptions) {\n  return function normalizeArgs (instance, caller, opts = {}, stream) {\n    // support stream as a string\n    if (typeof opts === 'string') {\n      stream = buildSafeSonicBoom({ dest: opts, sync: true })\n      opts = {}\n    } else if (typeof stream === 'string') {\n      if (opts && opts.transport) {\n        throw Error('only one of option.transport or stream can be specified')\n      }\n      stream = buildSafeSonicBoom({ dest: stream, sync: true })\n    } else if (opts instanceof SonicBoom || opts.writable || opts._writableState) {\n      stream = opts\n      opts = {}\n    } else if (opts.transport) {\n      if (opts.transport instanceof SonicBoom || opts.transport.writable || opts.transport._writableState) {\n        throw Error('option.transport do not allow stream, please pass to option directly. e.g. pino(transport)')\n      }\n      if (opts.transport.targets && opts.transport.targets.length && opts.formatters && typeof opts.formatters.level === 'function') {\n        throw Error('option.transport.targets do not allow custom level formatters')\n      }\n\n      let customLevels\n      if (opts.customLevels) {\n        customLevels = opts.useOnlyCustomLevels ? opts.customLevels : Object.assign({}, opts.levels, opts.customLevels)\n      }\n      stream = transport({ caller, ...opts.transport, levels: customLevels })\n    }\n    opts = Object.assign({}, defaultOptions, opts)\n    opts.serializers = Object.assign({}, defaultOptions.serializers, opts.serializers)\n    opts.formatters = Object.assign({}, defaultOptions.formatters, opts.formatters)\n\n    if ('onTerminated' in opts) {\n      throw Error('The onTerminated option has been removed, use pino.final instead')\n    }\n    if ('changeLevelName' in opts) {\n      process.emitWarning(\n        'The changeLevelName option is deprecated and will be removed in v7. Use levelKey instead.',\n        { code: 'changeLevelName_deprecation' }\n      )\n      opts.levelKey = opts.changeLevelName\n      delete opts.changeLevelName\n    }\n    const { enabled, prettyPrint, prettifier, messageKey } = opts\n    if (enabled === false) opts.level = 'silent'\n    stream = stream || process.stdout\n    if (stream === process.stdout && stream.fd >= 0 && !hasBeenTampered(stream)) {\n      stream = buildSafeSonicBoom({ fd: stream.fd, sync: true })\n    }\n    if (prettyPrint) {\n      warning.emit('PINODEP008')\n      const prettyOpts = Object.assign({ messageKey }, prettyPrint)\n      stream = getPrettyStream(prettyOpts, prettifier, stream, instance)\n    }\n    return { opts, stream }\n  }\n}\n\nfunction final (logger, handler) {\n  const major = Number(process.versions.node.split('.')[0])\n  if (major >= 14) warning.emit('PINODEP009')\n\n  if (typeof logger === 'undefined' || typeof logger.child !== 'function') {\n    throw Error('expected a pino logger instance')\n  }\n  const hasHandler = (typeof handler !== 'undefined')\n  if (hasHandler && typeof handler !== 'function') {\n    throw Error('if supplied, the handler parameter should be a function')\n  }\n  const stream = logger[streamSym]\n  if (typeof stream.flushSync !== 'function') {\n    throw Error('final requires a stream that has a flushSync method, such as pino.destination')\n  }\n\n  const finalLogger = new Proxy(logger, {\n    get: (logger, key) => {\n      if (key in logger.levels.values) {\n        return (...args) => {\n          logger[key](...args)\n          stream.flushSync()\n        }\n      }\n      return logger[key]\n    }\n  })\n\n  if (!hasHandler) {\n    try {\n      stream.flushSync()\n    } catch {\n      // it's too late to wait for the stream to be ready\n      // because this is a final tick scenario.\n      // in practice there shouldn't be a situation where it isn't\n      // however, swallow the error just in case (and for easier testing)\n    }\n    return finalLogger\n  }\n\n  return (err = null, ...args) => {\n    try {\n      stream.flushSync()\n    } catch (e) {\n      // it's too late to wait for the stream to be ready\n      // because this is a final tick scenario.\n      // in practice there shouldn't be a situation where it isn't\n      // however, swallow the error just in case (and for easier testing)\n    }\n    return handler(err, finalLogger, ...args)\n  }\n}\n\nfunction stringify (obj, stringifySafeFn) {\n  try {\n    return JSON.stringify(obj)\n  } catch (_) {\n    try {\n      const stringify = stringifySafeFn || this[stringifySafeSym]\n      return stringify(obj)\n    } catch (_) {\n      return '\"[unable to serialize, circular reference is too complex to analyze]\"'\n    }\n  }\n}\n\nfunction buildFormatters (level, bindings, log) {\n  return {\n    level,\n    bindings,\n    log\n  }\n}\n\nfunction setMetadataProps (dest, that) {\n  if (dest[needsMetadataGsym] === true) {\n    dest.lastLevel = that.lastLevel\n    dest.lastMsg = that.lastMsg\n    dest.lastObj = that.lastObj\n    dest.lastTime = that.lastTime\n    dest.lastLogger = that.lastLogger\n  }\n}\n\n/**\n * Convert a string integer file descriptor to a proper native integer\n * file descriptor.\n *\n * @param {string} destination The file descriptor string to attempt to convert.\n *\n * @returns {Number}\n */\nfunction normalizeDestFileDescriptor (destination) {\n  const fd = Number(destination)\n  if (typeof destination === 'string' && Number.isFinite(fd)) {\n    return fd\n  }\n  return destination\n}\n\nmodule.exports = {\n  noop,\n  buildSafeSonicBoom,\n  getPrettyStream,\n  asChindings,\n  asJson,\n  genLog,\n  createArgsNormalizer,\n  final,\n  stringify,\n  buildFormatters,\n  normalizeDestFileDescriptor\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/tools.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/transport.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/transport.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { createRequire } = __webpack_require__(/*! module */ \"module\")\nconst getCallers = __webpack_require__(/*! ./caller */ \"(ssr)/./node_modules/pino/lib/caller.js\")\nconst { join, isAbsolute } = __webpack_require__(/*! path */ \"path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(ssr)/./node_modules/atomic-sleep/index.js\")\n\nlet onExit\n\nif (global.WeakRef && global.WeakMap && global.FinalizationRegistry) {\n  // This require MUST be top level otherwise the transport would\n  // not work from within Jest as it hijacks require.\n  onExit = __webpack_require__(/*! on-exit-leak-free */ \"(ssr)/./node_modules/on-exit-leak-free/index.js\")\n}\n\nconst ThreadStream = __webpack_require__(/*! thread-stream */ \"(ssr)/./node_modules/thread-stream/index.js\")\n\nfunction setupOnExit (stream) {\n  /* istanbul ignore next */\n  if (onExit) {\n    // This is leak free, it does not leave event handlers\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  } else {\n    const fn = autoEnd.bind(null, stream)\n    process.once('beforeExit', fn)\n    process.once('exit', fn)\n\n    stream.on('close', function () {\n      process.removeListener('beforeExit', fn)\n      process.removeListener('exit', fn)\n    })\n  }\n}\n\nfunction buildStream (filename, workerData, workerOpts) {\n  const stream = new ThreadStream({\n    filename,\n    workerData,\n    workerOpts\n  })\n\n  stream.on('ready', onReady)\n  stream.on('close', function () {\n    process.removeListener('exit', onExit)\n  })\n\n  process.on('exit', onExit)\n\n  function onReady () {\n    process.removeListener('exit', onExit)\n    stream.unref()\n\n    if (workerOpts.autoEnd !== false) {\n      setupOnExit(stream)\n    }\n  }\n\n  function onExit () {\n    if (stream.closed) {\n      return\n    }\n    stream.flushSync()\n    // Apparently there is a very sporadic race condition\n    // that in certain OS would prevent the messages to be flushed\n    // because the thread might not have been created still.\n    // Unfortunately we need to sleep(100) in this case.\n    sleep(100)\n    stream.end()\n  }\n\n  return stream\n}\n\nfunction autoEnd (stream) {\n  stream.ref()\n  stream.flushSync()\n  stream.end()\n  stream.once('close', function () {\n    stream.unref()\n  })\n}\n\nfunction transport (fullOptions) {\n  const { pipeline, targets, levels, options = {}, worker = {}, caller = getCallers() } = fullOptions\n\n  // Backwards compatibility\n  const callers = typeof caller === 'string' ? [caller] : caller\n\n  // This will be eventually modified by bundlers\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n\n  let target = fullOptions.target\n\n  if (target && targets) {\n    throw new Error('only one of target or targets can be specified')\n  }\n\n  if (targets) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.targets = targets.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n  } else if (pipeline) {\n    target = bundlerOverrides['pino-pipeline-worker'] || join(__dirname, 'worker-pipeline.js')\n    options.targets = pipeline.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n  }\n\n  if (levels) {\n    options.levels = levels\n  }\n\n  return buildStream(fixTarget(target), options, worker)\n\n  function fixTarget (origin) {\n    origin = bundlerOverrides[origin] || origin\n\n    if (isAbsolute(origin) || origin.indexOf('file://') === 0) {\n      return origin\n    }\n\n    if (origin === 'pino/file') {\n      return join(__dirname, '..', 'file.js')\n    }\n\n    let fixTarget\n\n    for (const filePath of callers) {\n      try {\n        fixTarget = createRequire(filePath).resolve(origin)\n        break\n      } catch (err) {\n        // Silent catch\n        continue\n      }\n    }\n\n    if (!fixTarget) {\n      throw new Error(`unable to determine transport target for \"${origin}\"`)\n    }\n\n    return fixTarget\n  }\n}\n\nmodule.exports = transport\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/pino.js":
/*!***********************************!*\
  !*** ./node_modules/pino/pino.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/* eslint no-prototype-builtins: 0 */\nconst os = __webpack_require__(/*! os */ \"os\")\nconst stdSerializers = __webpack_require__(/*! pino-std-serializers */ \"(ssr)/./node_modules/pino-std-serializers/index.js\")\nconst caller = __webpack_require__(/*! ./lib/caller */ \"(ssr)/./node_modules/pino/lib/caller.js\")\nconst redaction = __webpack_require__(/*! ./lib/redaction */ \"(ssr)/./node_modules/pino/lib/redaction.js\")\nconst time = __webpack_require__(/*! ./lib/time */ \"(ssr)/./node_modules/pino/lib/time.js\")\nconst proto = __webpack_require__(/*! ./lib/proto */ \"(ssr)/./node_modules/pino/lib/proto.js\")\nconst symbols = __webpack_require__(/*! ./lib/symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { configure } = __webpack_require__(/*! safe-stable-stringify */ \"(ssr)/./node_modules/safe-stable-stringify/index.js\")\nconst { assertDefaultLevelFound, mappings, genLsCache, levels } = __webpack_require__(/*! ./lib/levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\nconst {\n  createArgsNormalizer,\n  asChindings,\n  final,\n  buildSafeSonicBoom,\n  buildFormatters,\n  stringify,\n  normalizeDestFileDescriptor,\n  noop\n} = __webpack_require__(/*! ./lib/tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\nconst { version } = __webpack_require__(/*! ./lib/meta */ \"(ssr)/./node_modules/pino/lib/meta.js\")\nconst {\n  chindingsSym,\n  redactFmtSym,\n  serializersSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  setLevelSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  nestedKeySym,\n  mixinSym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym\n} = symbols\nconst { epochTime, nullTime } = time\nconst { pid } = process\nconst hostname = os.hostname()\nconst defaultErrorSerializer = stdSerializers.err\nconst defaultOptions = {\n  level: 'info',\n  levels,\n  messageKey: 'msg',\n  nestedKey: null,\n  enabled: true,\n  prettyPrint: false,\n  base: { pid, hostname },\n  serializers: Object.assign(Object.create(null), {\n    err: defaultErrorSerializer\n  }),\n  formatters: Object.assign(Object.create(null), {\n    bindings (bindings) {\n      return bindings\n    },\n    level (label, number) {\n      return { level: number }\n    }\n  }),\n  hooks: {\n    logMethod: undefined\n  },\n  timestamp: epochTime,\n  name: undefined,\n  redact: null,\n  customLevels: null,\n  useOnlyCustomLevels: false,\n  depthLimit: 5,\n  edgeLimit: 100\n}\n\nconst normalize = createArgsNormalizer(defaultOptions)\n\nconst serializers = Object.assign(Object.create(null), stdSerializers)\n\nfunction pino (...args) {\n  const instance = {}\n  const { opts, stream } = normalize(instance, caller(), ...args)\n  const {\n    redact,\n    crlf,\n    serializers,\n    timestamp,\n    messageKey,\n    nestedKey,\n    base,\n    name,\n    level,\n    customLevels,\n    mixin,\n    mixinMergeStrategy,\n    useOnlyCustomLevels,\n    formatters,\n    hooks,\n    depthLimit,\n    edgeLimit\n  } = opts\n\n  const stringifySafe = configure({\n    maximumDepth: depthLimit,\n    maximumBreadth: edgeLimit\n  })\n\n  const allFormatters = buildFormatters(\n    formatters.level,\n    formatters.bindings,\n    formatters.log\n  )\n\n  const stringifiers = redact ? redaction(redact, stringify) : {}\n  const stringifyFn = stringify.bind({\n    [stringifySafeSym]: stringifySafe\n  })\n  const formatOpts = redact\n    ? { stringify: stringifiers[redactFmtSym] }\n    : { stringify: stringifyFn }\n  const end = '}' + (crlf ? '\\r\\n' : '\\n')\n  const coreChindings = asChindings.bind(null, {\n    [chindingsSym]: '',\n    [serializersSym]: serializers,\n    [stringifiersSym]: stringifiers,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [formattersSym]: allFormatters\n  })\n\n  let chindings = ''\n  if (base !== null) {\n    if (name === undefined) {\n      chindings = coreChindings(base)\n    } else {\n      chindings = coreChindings(Object.assign({}, base, { name }))\n    }\n  }\n\n  const time = (timestamp instanceof Function)\n    ? timestamp\n    : (timestamp ? epochTime : nullTime)\n  const timeSliceIndex = time().indexOf(':') + 1\n\n  if (useOnlyCustomLevels && !customLevels) throw Error('customLevels is required if useOnlyCustomLevels is set true')\n  if (mixin && typeof mixin !== 'function') throw Error(`Unknown mixin type \"${typeof mixin}\" - expected \"function\"`)\n\n  assertDefaultLevelFound(level, customLevels, useOnlyCustomLevels)\n  const levels = mappings(customLevels, useOnlyCustomLevels)\n\n  Object.assign(instance, {\n    levels,\n    [useOnlyCustomLevelsSym]: useOnlyCustomLevels,\n    [streamSym]: stream,\n    [timeSym]: time,\n    [timeSliceIndexSym]: timeSliceIndex,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [stringifiersSym]: stringifiers,\n    [endSym]: end,\n    [formatOptsSym]: formatOpts,\n    [messageKeySym]: messageKey,\n    [nestedKeySym]: nestedKey,\n    // protect against injection\n    [nestedKeyStrSym]: nestedKey ? `,${JSON.stringify(nestedKey)}:{` : '',\n    [serializersSym]: serializers,\n    [mixinSym]: mixin,\n    [mixinMergeStrategySym]: mixinMergeStrategy,\n    [chindingsSym]: chindings,\n    [formattersSym]: allFormatters,\n    [hooksSym]: hooks,\n    silent: noop\n  })\n\n  Object.setPrototypeOf(instance, proto())\n\n  genLsCache(instance)\n\n  instance[setLevelSym](level)\n\n  return instance\n}\n\nmodule.exports = pino\n\nmodule.exports.destination = (dest = process.stdout.fd) => {\n  if (typeof dest === 'object') {\n    dest.dest = normalizeDestFileDescriptor(dest.dest || process.stdout.fd)\n    return buildSafeSonicBoom(dest)\n  } else {\n    return buildSafeSonicBoom({ dest: normalizeDestFileDescriptor(dest), minLength: 0, sync: true })\n  }\n}\n\nmodule.exports.transport = __webpack_require__(/*! ./lib/transport */ \"(ssr)/./node_modules/pino/lib/transport.js\")\nmodule.exports.multistream = __webpack_require__(/*! ./lib/multistream */ \"(ssr)/./node_modules/pino/lib/multistream.js\")\n\nmodule.exports.final = final\nmodule.exports.levels = mappings()\nmodule.exports.stdSerializers = serializers\nmodule.exports.stdTimeFunctions = Object.assign({}, time)\nmodule.exports.symbols = symbols\nmodule.exports.version = version\n\n// Enables default and name export with TypeScript and Babel\nmodule.exports[\"default\"] = pino\nmodule.exports.pino = pino\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/pino.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/package.json":
/*!****************************************!*\
  !*** ./node_modules/pino/package.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"pino","version":"7.11.0","description":"super fast, all natural json logger","main":"pino.js","type":"commonjs","types":"pino.d.ts","browser":"./browser.js","files":["pino.js","file.js","pino.d.ts","bin.js","browser.js","pretty.js","usage.txt","test","docs","example.js","lib"],"scripts":{"docs":"docsify serve","browser-test":"airtap --local 8080 test/browser*test.js","lint":"eslint .","test":"npm run lint && npm run transpile && tap --ts && jest test/jest && npm run test-types","test-ci":"npm run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly && npm run test-types","test-ci-pnpm":"pnpm run lint && npm run transpile && tap --ts --no-coverage --no-check-coverage && pnpm run test-types","test-ci-yarn-pnp":"yarn run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly","test-types":"tsc && tsd && ts-node test/types/pino.ts","transpile":"node ./test/fixtures/ts/transpile.cjs","cov-ui":"tap --ts --coverage-report=html","bench":"node benchmarks/utils/runbench all","bench-basic":"node benchmarks/utils/runbench basic","bench-object":"node benchmarks/utils/runbench object","bench-deep-object":"node benchmarks/utils/runbench deep-object","bench-multi-arg":"node benchmarks/utils/runbench multi-arg","bench-longs-tring":"node benchmarks/utils/runbench long-string","bench-child":"node benchmarks/utils/runbench child","bench-child-child":"node benchmarks/utils/runbench child-child","bench-child-creation":"node benchmarks/utils/runbench child-creation","bench-formatters":"node benchmarks/utils/runbench formatters","update-bench-doc":"node benchmarks/utils/generate-benchmark-doc > docs/benchmarks.md"},"bin":{"pino":"./bin.js"},"precommit":"test","repository":{"type":"git","url":"git+https://github.com/pinojs/pino.git"},"keywords":["fast","logger","stream","json"],"author":"Matteo Collina <<EMAIL>>","contributors":["David Mark Clements <<EMAIL>>","James Sumners <<EMAIL>>","Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)"],"license":"MIT","bugs":{"url":"https://github.com/pinojs/pino/issues"},"homepage":"http://getpino.io","devDependencies":{"@types/flush-write-stream":"^1.0.0","@types/node":"^17.0.0","@types/tap":"^15.0.6","airtap":"4.0.4","benchmark":"^2.1.4","bole":"^4.0.0","bunyan":"^1.8.14","docsify-cli":"^4.4.1","eslint":"^7.17.0","eslint-config-standard":"^16.0.3","eslint-plugin-import":"^2.22.1","eslint-plugin-node":"^11.1.0","eslint-plugin-promise":"^5.1.0","execa":"^5.0.0","fastbench":"^1.0.1","flush-write-stream":"^2.0.0","import-fresh":"^3.2.1","jest":"^27.3.1","log":"^6.0.0","loglevel":"^1.6.7","pino-pretty":"^v7.6.0","pre-commit":"^1.2.2","proxyquire":"^2.1.3","pump":"^3.0.0","rimraf":"^3.0.2","semver":"^7.0.0","split2":"^4.0.0","steed":"^1.1.3","strip-ansi":"^6.0.0","tap":"^16.0.0","tape":"^5.0.0","through2":"^4.0.0","ts-node":"^10.7.0","tsd":"^0.20.0","typescript":"^4.4.4","winston":"^3.3.3"},"dependencies":{"atomic-sleep":"^1.0.0","fast-redact":"^3.0.0","on-exit-leak-free":"^0.2.0","pino-abstract-transport":"v0.5.0","pino-std-serializers":"^4.0.0","process-warning":"^1.0.0","quick-format-unescaped":"^4.0.3","real-require":"^0.1.0","safe-stable-stringify":"^2.1.0","sonic-boom":"^2.2.1","thread-stream":"^0.15.1"},"tsd":{"directory":"test/types"}}');

/***/ })

};
;