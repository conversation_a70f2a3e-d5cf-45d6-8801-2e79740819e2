"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/big.js";
exports.ids = ["vendor-chunks/big.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/big.js/big.mjs":
/*!*************************************!*\
  !*** ./node_modules/big.js/big.mjs ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Big: () => (/* binding */ Big),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  big.js v6.2.2\r\n *  A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic.\r\n *  Copyright (c) 2024 Michael Mclaughlin\r\n *  https://github.com/MikeMcl/big.js/LICENCE.md\r\n */\r\n\r\n\r\n/************************************** EDITABLE DEFAULTS *****************************************/\r\n\r\n\r\n  // The default values below must be integers within the stated ranges.\r\n\r\n  /*\r\n   * The maximum number of decimal places (DP) of the results of operations involving division:\r\n   * div and sqrt, and pow with negative exponents.\r\n   */\r\nvar DP = 20,          // 0 to MAX_DP\r\n\r\n  /*\r\n   * The rounding mode (RM) used when rounding to the above decimal places.\r\n   *\r\n   *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)\r\n   *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)\r\n   *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)\r\n   *  3  Away from zero.                                  (ROUND_UP)\r\n   */\r\n  RM = 1,             // 0, 1, 2 or 3\r\n\r\n  // The maximum value of DP and Big.DP.\r\n  MAX_DP = 1E6,       // 0 to 1000000\r\n\r\n  // The maximum magnitude of the exponent argument to the pow method.\r\n  MAX_POWER = 1E6,    // 1 to 1000000\r\n\r\n  /*\r\n   * The negative exponent (NE) at and beneath which toString returns exponential notation.\r\n   * (JavaScript numbers: -7)\r\n   * -1000000 is the minimum recommended exponent value of a Big.\r\n   */\r\n  NE = -7,            // 0 to -1000000\r\n\r\n  /*\r\n   * The positive exponent (PE) at and above which toString returns exponential notation.\r\n   * (JavaScript numbers: 21)\r\n   * 1000000 is the maximum recommended exponent value of a Big, but this limit is not enforced.\r\n   */\r\n  PE = 21,            // 0 to 1000000\r\n\r\n  /*\r\n   * When true, an error will be thrown if a primitive number is passed to the Big constructor,\r\n   * or if valueOf is called, or if toNumber is called on a Big which cannot be converted to a\r\n   * primitive number without a loss of precision.\r\n   */\r\n  STRICT = false,     // true or false\r\n\r\n\r\n/**************************************************************************************************/\r\n\r\n\r\n  // Error messages.\r\n  NAME = '[big.js] ',\r\n  INVALID = NAME + 'Invalid ',\r\n  INVALID_DP = INVALID + 'decimal places',\r\n  INVALID_RM = INVALID + 'rounding mode',\r\n  DIV_BY_ZERO = NAME + 'Division by zero',\r\n\r\n  // The shared prototype object.\r\n  P = {},\r\n  UNDEFINED = void 0,\r\n  NUMERIC = /^-?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\r\n\r\n\r\n/*\r\n * Create and return a Big constructor.\r\n */\r\nfunction _Big_() {\r\n\r\n  /*\r\n   * The Big constructor and exported function.\r\n   * Create and return a new instance of a Big number object.\r\n   *\r\n   * n {number|string|Big} A numeric value.\r\n   */\r\n  function Big(n) {\r\n    var x = this;\r\n\r\n    // Enable constructor usage without new.\r\n    if (!(x instanceof Big)) return n === UNDEFINED ? _Big_() : new Big(n);\r\n\r\n    // Duplicate.\r\n    if (n instanceof Big) {\r\n      x.s = n.s;\r\n      x.e = n.e;\r\n      x.c = n.c.slice();\r\n    } else {\r\n      if (typeof n !== 'string') {\r\n        if (Big.strict === true && typeof n !== 'bigint') {\r\n          throw TypeError(INVALID + 'value');\r\n        }\r\n\r\n        // Minus zero?\r\n        n = n === 0 && 1 / n < 0 ? '-0' : String(n);\r\n      }\r\n\r\n      parse(x, n);\r\n    }\r\n\r\n    // Retain a reference to this Big constructor.\r\n    // Shadow Big.prototype.constructor which points to Object.\r\n    x.constructor = Big;\r\n  }\r\n\r\n  Big.prototype = P;\r\n  Big.DP = DP;\r\n  Big.RM = RM;\r\n  Big.NE = NE;\r\n  Big.PE = PE;\r\n  Big.strict = STRICT;\r\n  Big.roundDown = 0;\r\n  Big.roundHalfUp = 1;\r\n  Big.roundHalfEven = 2;\r\n  Big.roundUp = 3;\r\n\r\n  return Big;\r\n}\r\n\r\n\r\n/*\r\n * Parse the number or string value passed to a Big constructor.\r\n *\r\n * x {Big} A Big number instance.\r\n * n {number|string} A numeric value.\r\n */\r\nfunction parse(x, n) {\r\n  var e, i, nl;\r\n\r\n  if (!NUMERIC.test(n)) {\r\n    throw Error(INVALID + 'number');\r\n  }\r\n\r\n  // Determine sign.\r\n  x.s = n.charAt(0) == '-' ? (n = n.slice(1), -1) : 1;\r\n\r\n  // Decimal point?\r\n  if ((e = n.indexOf('.')) > -1) n = n.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = n.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +n.slice(i + 1);\r\n    n = n.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = n.length;\r\n  }\r\n\r\n  nl = n.length;\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; i < nl && n.charAt(i) == '0';) ++i;\r\n\r\n  if (i == nl) {\r\n\r\n    // Zero.\r\n    x.c = [x.e = 0];\r\n  } else {\r\n\r\n    // Determine trailing zeros.\r\n    for (; nl > 0 && n.charAt(--nl) == '0';);\r\n    x.e = e - i - 1;\r\n    x.c = [];\r\n\r\n    // Convert string to array of digits without leading/trailing zeros.\r\n    for (e = 0; i <= nl;) x.c[e++] = +n.charAt(i++);\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Round Big x to a maximum of sd significant digits using rounding mode rm.\r\n *\r\n * x {Big} The Big to round.\r\n * sd {number} Significant digits: integer, 0 to MAX_DP inclusive.\r\n * rm {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n * [more] {boolean} Whether the result of division was truncated.\r\n */\r\nfunction round(x, sd, rm, more) {\r\n  var xc = x.c;\r\n\r\n  if (rm === UNDEFINED) rm = x.constructor.RM;\r\n  if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {\r\n    throw Error(INVALID_RM);\r\n  }\r\n\r\n  if (sd < 1) {\r\n    more =\r\n      rm === 3 && (more || !!xc[0]) || sd === 0 && (\r\n      rm === 1 && xc[0] >= 5 ||\r\n      rm === 2 && (xc[0] > 5 || xc[0] === 5 && (more || xc[1] !== UNDEFINED))\r\n    );\r\n\r\n    xc.length = 1;\r\n\r\n    if (more) {\r\n\r\n      // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n      x.e = x.e - sd + 1;\r\n      xc[0] = 1;\r\n    } else {\r\n\r\n      // Zero.\r\n      xc[0] = x.e = 0;\r\n    }\r\n  } else if (sd < xc.length) {\r\n\r\n    // xc[sd] is the digit after the digit that may be rounded up.\r\n    more =\r\n      rm === 1 && xc[sd] >= 5 ||\r\n      rm === 2 && (xc[sd] > 5 || xc[sd] === 5 &&\r\n        (more || xc[sd + 1] !== UNDEFINED || xc[sd - 1] & 1)) ||\r\n      rm === 3 && (more || !!xc[0]);\r\n\r\n    // Remove any digits after the required precision.\r\n    xc.length = sd;\r\n\r\n    // Round up?\r\n    if (more) {\r\n\r\n      // Rounding up may mean the previous digit has to be rounded up.\r\n      for (; ++xc[--sd] > 9;) {\r\n        xc[sd] = 0;\r\n        if (sd === 0) {\r\n          ++x.e;\r\n          xc.unshift(1);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (sd = xc.length; !xc[--sd];) xc.pop();\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a string representing the value of Big x in normal or exponential notation.\r\n * Handles P.toExponential, P.toFixed, P.toJSON, P.toPrecision, P.toString and P.valueOf.\r\n */\r\nfunction stringify(x, doExponential, isNonzero) {\r\n  var e = x.e,\r\n    s = x.c.join(''),\r\n    n = s.length;\r\n\r\n  // Exponential notation?\r\n  if (doExponential) {\r\n    s = s.charAt(0) + (n > 1 ? '.' + s.slice(1) : '') + (e < 0 ? 'e' : 'e+') + e;\r\n\r\n  // Normal notation.\r\n  } else if (e < 0) {\r\n    for (; ++e;) s = '0' + s;\r\n    s = '0.' + s;\r\n  } else if (e > 0) {\r\n    if (++e > n) {\r\n      for (e -= n; e--;) s += '0';\r\n    } else if (e < n) {\r\n      s = s.slice(0, e) + '.' + s.slice(e);\r\n    }\r\n  } else if (n > 1) {\r\n    s = s.charAt(0) + '.' + s.slice(1);\r\n  }\r\n\r\n  return x.s < 0 && isNonzero ? '-' + s : s;\r\n}\r\n\r\n\r\n// Prototype/instance methods\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the absolute value of this Big.\r\n */\r\nP.abs = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = 1;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return 1 if the value of this Big is greater than the value of Big y,\r\n *       -1 if the value of this Big is less than the value of Big y, or\r\n *        0 if they have the same value.\r\n */\r\nP.cmp = function (y) {\r\n  var isneg,\r\n    x = this,\r\n    xc = x.c,\r\n    yc = (y = new x.constructor(y)).c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) return !xc[0] ? !yc[0] ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  isneg = i < 0;\r\n\r\n  // Compare exponents.\r\n  if (k != l) return k > l ^ isneg ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = -1; ++i < j;) {\r\n    if (xc[i] != yc[i]) return xc[i] > yc[i] ^ isneg ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ isneg ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big divided by the value of Big y, rounded,\r\n * if necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.div = function (y) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    a = x.c,                  // dividend\r\n    b = (y = new Big(y)).c,   // divisor\r\n    k = x.s == y.s ? 1 : -1,\r\n    dp = Big.DP;\r\n\r\n  if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n\r\n  // Divisor is zero?\r\n  if (!b[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  // Dividend is 0? Return +-0.\r\n  if (!a[0]) {\r\n    y.s = k;\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  var bl, bt, n, cmp, ri,\r\n    bz = b.slice(),\r\n    ai = bl = b.length,\r\n    al = a.length,\r\n    r = a.slice(0, bl),   // remainder\r\n    rl = r.length,\r\n    q = y,                // quotient\r\n    qc = q.c = [],\r\n    qi = 0,\r\n    p = dp + (q.e = x.e - y.e) + 1;    // precision of the result\r\n\r\n  q.s = k;\r\n  k = p < 0 ? 0 : p;\r\n\r\n  // Create version of divisor with leading zero.\r\n  bz.unshift(0);\r\n\r\n  // Add zeros to make remainder as long as divisor.\r\n  for (; rl++ < bl;) r.push(0);\r\n\r\n  do {\r\n\r\n    // n is how many times the divisor goes into current remainder.\r\n    for (n = 0; n < 10; n++) {\r\n\r\n      // Compare divisor and remainder.\r\n      if (bl != (rl = r.length)) {\r\n        cmp = bl > rl ? 1 : -1;\r\n      } else {\r\n        for (ri = -1, cmp = 0; ++ri < bl;) {\r\n          if (b[ri] != r[ri]) {\r\n            cmp = b[ri] > r[ri] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If divisor < remainder, subtract divisor from remainder.\r\n      if (cmp < 0) {\r\n\r\n        // Remainder can't be more than 1 digit longer than divisor.\r\n        // Equalise lengths using divisor with extra leading zero?\r\n        for (bt = rl == bl ? b : bz; rl;) {\r\n          if (r[--rl] < bt[rl]) {\r\n            ri = rl;\r\n            for (; ri && !r[--ri];) r[ri] = 9;\r\n            --r[ri];\r\n            r[rl] += 10;\r\n          }\r\n          r[rl] -= bt[rl];\r\n        }\r\n\r\n        for (; !r[0];) r.shift();\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Add the digit n to the result array.\r\n    qc[qi++] = cmp ? n : ++n;\r\n\r\n    // Update the remainder.\r\n    if (r[0] && cmp) r[rl] = a[ai] || 0;\r\n    else r = [a[ai]];\r\n\r\n  } while ((ai++ < al || r[0] !== UNDEFINED) && k--);\r\n\r\n  // Leading zero? Do not remove if result is simply zero (qi == 1).\r\n  if (!qc[0] && qi != 1) {\r\n\r\n    // There can't be more than one zero.\r\n    qc.shift();\r\n    q.e--;\r\n    p--;\r\n  }\r\n\r\n  // Round?\r\n  if (qi > p) round(q, p, Big.RM, r[0] !== UNDEFINED);\r\n\r\n  return q;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is equal to the value of Big y, otherwise return false.\r\n */\r\nP.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than the value of Big y, otherwise return\r\n * false.\r\n */\r\nP.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.gte = function (y) {\r\n  return this.cmp(y) > -1;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than the value of Big y, otherwise return false.\r\n */\r\nP.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big minus the value of Big y.\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var i, j, t, xlty,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  // Signs differ?\r\n  if (a != b) {\r\n    y.s = -b;\r\n    return x.plus(y);\r\n  }\r\n\r\n  var xc = x.c.slice(),\r\n    xe = x.e,\r\n    yc = y.c,\r\n    ye = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (yc[0]) {\r\n      y.s = -b;\r\n    } else if (xc[0]) {\r\n      y = new Big(x);\r\n    } else {\r\n      y.s = 1;\r\n    }\r\n    return y;\r\n  }\r\n\r\n  // Determine which is the bigger number. Prepend zeros to equalise exponents.\r\n  if (a = xe - ye) {\r\n\r\n    if (xlty = a < 0) {\r\n      a = -a;\r\n      t = xc;\r\n    } else {\r\n      ye = xe;\r\n      t = yc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (b = a; b--;) t.push(0);\r\n    t.reverse();\r\n  } else {\r\n\r\n    // Exponents equal. Check digit by digit.\r\n    j = ((xlty = xc.length < yc.length) ? xc : yc).length;\r\n\r\n    for (a = b = 0; b < j; b++) {\r\n      if (xc[b] != yc[b]) {\r\n        xlty = xc[b] < yc[b];\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // x < y? Point xc to the array of the bigger number.\r\n  if (xlty) {\r\n    t = xc;\r\n    xc = yc;\r\n    yc = t;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  /*\r\n   * Append zeros to xc if shorter. No need to add zeros to yc if shorter as subtraction only\r\n   * needs to start at yc.length.\r\n   */\r\n  if ((b = (j = yc.length) - (i = xc.length)) > 0) for (; b--;) xc[i++] = 0;\r\n\r\n  // Subtract yc from xc.\r\n  for (b = i; j > a;) {\r\n    if (xc[--j] < yc[j]) {\r\n      for (i = j; i && !xc[--i];) xc[i] = 9;\r\n      --xc[i];\r\n      xc[j] += 10;\r\n    }\r\n\r\n    xc[j] -= yc[j];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xc[--b] === 0;) xc.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xc[0] === 0;) {\r\n    xc.shift();\r\n    --ye;\r\n  }\r\n\r\n  if (!xc[0]) {\r\n\r\n    // n - n = +0\r\n    y.s = 1;\r\n\r\n    // Result must be zero.\r\n    xc = [ye = 0];\r\n  }\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big modulo the value of Big y.\r\n */\r\nP.mod = function (y) {\r\n  var ygtx,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  if (!y.c[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  x.s = y.s = 1;\r\n  ygtx = y.cmp(x) == 1;\r\n  x.s = a;\r\n  y.s = b;\r\n\r\n  if (ygtx) return new Big(x);\r\n\r\n  a = Big.DP;\r\n  b = Big.RM;\r\n  Big.DP = Big.RM = 0;\r\n  x = x.div(y);\r\n  Big.DP = a;\r\n  Big.RM = b;\r\n\r\n  return this.minus(x.times(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big negated.\r\n */\r\nP.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big plus the value of Big y.\r\n */\r\nP.plus = P.add = function (y) {\r\n  var e, k, t,\r\n    x = this,\r\n    Big = x.constructor;\r\n\r\n  y = new Big(y);\r\n\r\n  // Signs differ?\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  var xe = x.e,\r\n    xc = x.c,\r\n    ye = y.e,\r\n    yc = y.c;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (!yc[0]) {\r\n      if (xc[0]) {\r\n        y = new Big(x);\r\n      } else {\r\n        y.s = x.s;\r\n      }\r\n    }\r\n    return y;\r\n  }\r\n\r\n  xc = xc.slice();\r\n\r\n  // Prepend zeros to equalise exponents.\r\n  // Note: reverse faster than unshifts.\r\n  if (e = xe - ye) {\r\n    if (e > 0) {\r\n      ye = xe;\r\n      t = yc;\r\n    } else {\r\n      e = -e;\r\n      t = xc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (; e--;) t.push(0);\r\n    t.reverse();\r\n  }\r\n\r\n  // Point xc to the longer array.\r\n  if (xc.length - yc.length < 0) {\r\n    t = yc;\r\n    yc = xc;\r\n    xc = t;\r\n  }\r\n\r\n  e = yc.length;\r\n\r\n  // Only start adding at yc.length - 1 as the further digits of xc can be left as they are.\r\n  for (k = 0; e; xc[e] %= 10) k = (xc[--e] = xc[e] + yc[e] + k) / 10 | 0;\r\n\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n\r\n  if (k) {\r\n    xc.unshift(k);\r\n    ++ye;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (e = xc.length; xc[--e] === 0;) xc.pop();\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a Big whose value is the value of this Big raised to the power n.\r\n * If n is negative, round to a maximum of Big.DP decimal places using rounding\r\n * mode Big.RM.\r\n *\r\n * n {number} Integer, -MAX_POWER to MAX_POWER inclusive.\r\n */\r\nP.pow = function (n) {\r\n  var x = this,\r\n    one = new x.constructor('1'),\r\n    y = one,\r\n    isneg = n < 0;\r\n\r\n  if (n !== ~~n || n < -MAX_POWER || n > MAX_POWER) {\r\n    throw Error(INVALID + 'exponent');\r\n  }\r\n\r\n  if (isneg) n = -n;\r\n\r\n  for (;;) {\r\n    if (n & 1) y = y.times(x);\r\n    n >>= 1;\r\n    if (!n) break;\r\n    x = x.times(x);\r\n  }\r\n\r\n  return isneg ? one.div(y) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum precision of sd\r\n * significant digits using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.prec = function (sd, rm) {\r\n  if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n    throw Error(INVALID + 'precision');\r\n  }\r\n  return round(new this.constructor(this), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum of dp decimal places\r\n * using rounding mode rm, or Big.RM if rm is not specified.\r\n * If dp is negative, round to an integer which is a multiple of 10**-dp.\r\n * If dp is not specified, round to 0 decimal places.\r\n *\r\n * dp? {number} Integer, -MAX_DP to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.round = function (dp, rm) {\r\n  if (dp === UNDEFINED) dp = 0;\r\n  else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n  return round(new this.constructor(this), dp + this.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the square root of the value of this Big, rounded, if\r\n * necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.sqrt = function () {\r\n  var r, c, t,\r\n    x = this,\r\n    Big = x.constructor,\r\n    s = x.s,\r\n    e = x.e,\r\n    half = new Big('0.5');\r\n\r\n  // Zero?\r\n  if (!x.c[0]) return new Big(x);\r\n\r\n  // Negative?\r\n  if (s < 0) {\r\n    throw Error(NAME + 'No square root');\r\n  }\r\n\r\n  // Estimate.\r\n  s = Math.sqrt(+stringify(x, true, true));\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Re-estimate: pass x coefficient to Math.sqrt as integer, then adjust the result exponent.\r\n  if (s === 0 || s === 1 / 0) {\r\n    c = x.c.join('');\r\n    if (!(c.length + e & 1)) c += '0';\r\n    s = Math.sqrt(c);\r\n    e = ((e + 1) / 2 | 0) - (e < 0 || e & 1);\r\n    r = new Big((s == 1 / 0 ? '5e' : (s = s.toExponential()).slice(0, s.indexOf('e') + 1)) + e);\r\n  } else {\r\n    r = new Big(s + '');\r\n  }\r\n\r\n  e = r.e + (Big.DP += 4);\r\n\r\n  // Newton-Raphson iteration.\r\n  do {\r\n    t = r;\r\n    r = half.times(t.plus(x.div(t)));\r\n  } while (t.c.slice(0, e).join('') !== r.c.slice(0, e).join(''));\r\n\r\n  return round(r, (Big.DP -= 4) + r.e + 1, Big.RM);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big times the value of Big y.\r\n */\r\nP.times = P.mul = function (y) {\r\n  var c,\r\n    x = this,\r\n    Big = x.constructor,\r\n    xc = x.c,\r\n    yc = (y = new Big(y)).c,\r\n    a = xc.length,\r\n    b = yc.length,\r\n    i = x.e,\r\n    j = y.e;\r\n\r\n  // Determine sign of result.\r\n  y.s = x.s == y.s ? 1 : -1;\r\n\r\n  // Return signed 0 if either 0.\r\n  if (!xc[0] || !yc[0]) {\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  // Initialise exponent of result as x.e + y.e.\r\n  y.e = i + j;\r\n\r\n  // If array xc has fewer digits than yc, swap xc and yc, and lengths.\r\n  if (a < b) {\r\n    c = xc;\r\n    xc = yc;\r\n    yc = c;\r\n    j = a;\r\n    a = b;\r\n    b = j;\r\n  }\r\n\r\n  // Initialise coefficient array of result with zeros.\r\n  for (c = new Array(j = a + b); j--;) c[j] = 0;\r\n\r\n  // Multiply.\r\n\r\n  // i is initially xc.length.\r\n  for (i = b; i--;) {\r\n    b = 0;\r\n\r\n    // a is yc.length.\r\n    for (j = a + i; j > i;) {\r\n\r\n      // Current sum of products at this digit position, plus carry.\r\n      b = c[j] + yc[i] * xc[j - i - 1] + b;\r\n      c[j--] = b % 10;\r\n\r\n      // carry\r\n      b = b / 10 | 0;\r\n    }\r\n\r\n    c[j] = b;\r\n  }\r\n\r\n  // Increment result exponent if there is a final carry, otherwise remove leading zero.\r\n  if (b) ++y.e;\r\n  else c.shift();\r\n\r\n  // Remove trailing zeros.\r\n  for (i = c.length; !c[--i];) c.pop();\r\n  y.c = c;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in exponential notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), ++dp, rm);\r\n    for (; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, true, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in normal notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), dp + x.e + 1, rm);\r\n\r\n    // x.e may have changed if the value is rounded up.\r\n    for (dp = dp + x.e + 1; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, false, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Omit the sign for negative zero.\r\n */\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toJSON = P.toString = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, !!x.c[0]);\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Big as a primitve number.\r\n */\r\nP.toNumber = function () {\r\n  var n = +stringify(this, true, true);\r\n  if (this.constructor.strict === true && !this.eq(n.toString())) {\r\n    throw Error(NAME + 'Imprecise conversion');\r\n  }\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big rounded to sd significant digits using\r\n * rounding mode rm, or Big.RM if rm is not specified.\r\n * Use exponential notation if sd is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    n = x.c[0];\r\n\r\n  if (sd !== UNDEFINED) {\r\n    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n      throw Error(INVALID + 'precision');\r\n    }\r\n    x = round(new Big(x), sd, rm);\r\n    for (; x.c.length < sd;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, sd <= x.e || x.e <= Big.NE || x.e >= Big.PE, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Include the sign for negative zero.\r\n */\r\nP.valueOf = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  if (Big.strict === true) {\r\n    throw Error(NAME + 'valueOf disallowed');\r\n  }\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, true);\r\n};\r\n\r\n\r\n// Export\r\n\r\n\r\nvar Big = _Big_();\r\n\r\n/// <reference types=\"https://raw.githubusercontent.com/DefinitelyTyped/DefinitelyTyped/master/types/big.js/index.d.ts\" />\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Big);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/big.js/big.mjs\n");

/***/ })

};
;