"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_wagmi_connectors_dist_esm_exports_index_js"],{

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/baseAccount.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/baseAccount.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseAccount: function() { return /* binding */ baseAccount; }\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\nfunction baseAccount(parameters = {}) {\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'baseAccount',\n        name: 'Base Account',\n        rdns: 'app.base.account',\n        type: 'baseAccount',\n        async connect({ chainId } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                    params: [],\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = (await provider.request({\n                method: 'eth_chainId',\n            }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                const preference = (() => {\n                    if (typeof parameters.preference === 'string')\n                        return { options: parameters.preference };\n                    return {\n                        ...parameters.preference,\n                        options: parameters.preference?.options ?? 'all',\n                    };\n                })();\n                const { createBaseAccountSDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_base-org_account_dist_index_js-_app-pages-browser_node_module-928d3d\").then(__webpack_require__.bind(__webpack_require__, /*! @base-org/account */ \"(app-pages-browser)/./node_modules/@base-org/account/dist/index.js\"));\n                const sdk = createBaseAccountSDK({\n                    ...parameters,\n                    appChainIds: config.chains.map((x) => x.id),\n                    preference,\n                });\n                walletProvider = sdk.getProvider();\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\n//# sourceMappingURL=baseAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/baseAccount.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: function() { return /* binding */ coinbaseWallet; }\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\ncoinbaseWallet.type = 'coinbaseWallet';\nfunction coinbaseWallet(parameters = {}) {\n    if (parameters.version === '3' || parameters.headlessMode)\n        return version3(parameters);\n    return version4(parameters);\n}\nfunction version4(parameters) {\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                    params: 'instantOnboarding' in rest && rest.instantOnboarding\n                        ? [{ onboarding: 'instant' }]\n                        : [],\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close?.();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = (await provider.request({\n                method: 'eth_chainId',\n            }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                const preference = (() => {\n                    if (typeof parameters.preference === 'string')\n                        return { options: parameters.preference };\n                    return {\n                        ...parameters.preference,\n                        options: parameters.preference?.options ?? 'all',\n                    };\n                })();\n                const { createCoinbaseWalletSDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_coinbase_wallet-sdk_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @coinbase/wallet-sdk */ \"(app-pages-browser)/./node_modules/@coinbase/wallet-sdk/dist/index.js\"));\n                const sdk = createCoinbaseWalletSDK({\n                    ...parameters,\n                    appChainIds: config.chains.map((x) => x.id),\n                    preference,\n                });\n                walletProvider = sdk.getProvider();\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\nfunction version3(parameters) {\n    const reloadOnDisconnect = false;\n    let sdk;\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = await provider.request({\n                method: 'eth_chainId',\n            });\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const CoinbaseWalletSDK = await (async () => {\n                    const { default: SDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cbw-sdk_dist_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cbw-sdk */ \"(app-pages-browser)/./node_modules/cbw-sdk/dist/index.js\", 19));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                sdk = new CoinbaseWalletSDK({ ...parameters, reloadOnDisconnect });\n                // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.\n                const walletExtensionChainId = sdk.walletExtension?.getChainId();\n                const chain = config.chains.find((chain) => parameters.chainId\n                    ? chain.id === parameters.chainId\n                    : chain.id === walletExtensionChainId) || config.chains[0];\n                const chainId = parameters.chainId || chain?.id;\n                const jsonRpcUrl = parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0];\n                walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId);\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\n//# sourceMappingURL=coinbaseWallet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/exports/index.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseAccount: function() { return /* reexport safe */ _baseAccount_js__WEBPACK_IMPORTED_MODULE_2__.baseAccount; },\n/* harmony export */   coinbaseWallet: function() { return /* reexport safe */ _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_3__.coinbaseWallet; },\n/* harmony export */   injected: function() { return /* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_0__.injected; },\n/* harmony export */   metaMask: function() { return /* reexport safe */ _metaMask_js__WEBPACK_IMPORTED_MODULE_4__.metaMask; },\n/* harmony export */   mock: function() { return /* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.mock; },\n/* harmony export */   safe: function() { return /* reexport safe */ _safe_js__WEBPACK_IMPORTED_MODULE_5__.safe; },\n/* harmony export */   version: function() { return /* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_7__.version; },\n/* harmony export */   walletConnect: function() { return /* reexport safe */ _walletConnect_js__WEBPACK_IMPORTED_MODULE_6__.walletConnect; }\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\");\n/* harmony import */ var _baseAccount_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../baseAccount.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/baseAccount.js\");\n/* harmony import */ var _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../coinbaseWallet.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\");\n/* harmony import */ var _metaMask_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../metaMask.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\");\n/* harmony import */ var _safe_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../safe.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/safe.js\");\n/* harmony import */ var _walletConnect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../walletConnect.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../version.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/version.js\");\n// biome-ignore lint/performance/noBarrelFile: entrypoint module\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS9leHBvcnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUM4QztBQUNFO0FBQ087QUFDYjtBQUNSO0FBQ21CO0FBQ2I7QUFDeEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb25uZWN0b3JzL2Rpc3QvZXNtL2V4cG9ydHMvaW5kZXguanM/MzBmNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBiaW9tZS1pZ25vcmUgbGludC9wZXJmb3JtYW5jZS9ub0JhcnJlbEZpbGU6IGVudHJ5cG9pbnQgbW9kdWxlXG5leHBvcnQgeyBpbmplY3RlZCwgbW9jaywgfSBmcm9tICdAd2FnbWkvY29yZSc7XG5leHBvcnQgeyBiYXNlQWNjb3VudCB9IGZyb20gJy4uL2Jhc2VBY2NvdW50LmpzJztcbmV4cG9ydCB7IGNvaW5iYXNlV2FsbGV0LCB9IGZyb20gJy4uL2NvaW5iYXNlV2FsbGV0LmpzJztcbmV4cG9ydCB7IG1ldGFNYXNrIH0gZnJvbSAnLi4vbWV0YU1hc2suanMnO1xuZXhwb3J0IHsgc2FmZSB9IGZyb20gJy4uL3NhZmUuanMnO1xuZXhwb3J0IHsgd2FsbGV0Q29ubmVjdCwgfSBmcm9tICcuLi93YWxsZXRDb25uZWN0LmpzJztcbmV4cG9ydCB7IHZlcnNpb24gfSBmcm9tICcuLi92ZXJzaW9uLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: function() { return /* binding */ metaMask; }\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_metamask_sdk_dist_browser_es_metamask-sdk_js\").then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(app-pages-browser)/./node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS9tZXRhTWFzay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRztBQUMrQztBQUM5SjtBQUNPLGlDQUFpQztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0REFBZTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Qsd0JBQXdCLDBCQUEwQixJQUFJO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLGdEQUFVO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsU0FBUztBQUNwRSwyQ0FBMkMsMERBQXdCO0FBQ25FO0FBQ0EsaUNBQWlDO0FBQ2pDLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQywwREFBd0I7QUFDM0QsOEJBQThCLDBEQUF3QjtBQUN0RCxtQ0FBbUMsNkRBQTJCO0FBQzlELDhCQUE4Qiw2REFBMkI7QUFDekQ7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYix1Q0FBdUMsZ0RBQVU7QUFDakQsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyx1QkFBdUI7QUFDbEU7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixlQUFlLFFBQVEsa1JBQXVCO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsbUNBQW1DLGlEQUFXLGNBQWMsMkRBQWM7QUFDMUU7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsOEJBQThCLDhEQUFxQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsK0NBQVMsT0FBTyxpREFBVyw2QkFBNkIsU0FBUztBQUN4RztBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsNEJBQTRCLG9DQUFvQztBQUNoRTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsa0RBQWdCLEtBQUssZ0VBQXVCO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixTQUFTLGlEQUFXLFdBQVc7QUFDOUQsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsMERBQXdCO0FBQzNELDhCQUE4QiwwREFBd0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELDRDQUE0QztBQUM1RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLDZDQUE2QyxpREFBVztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLGlDQUFpQztBQUNqQztBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsMERBQXdCO0FBQ25FLHNDQUFzQywwREFBd0I7QUFDOUQsa0NBQWtDLGtEQUFnQjtBQUNsRDtBQUNBO0FBQ0EsMEJBQTBCLGtEQUFnQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwrQ0FBUztBQUMvQixrQ0FBa0MsaURBQVc7QUFDN0M7QUFDQSw4Q0FBOEMsdUJBQXVCO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLG9EQUFvRCxTQUFTO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxTQUFTO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELGdEQUFVO0FBQzVELGlCQUFpQjtBQUNqQixTQUFTO0FBQ1Q7QUFDQTtBQUNBLDRDQUE0QyxTQUFTO0FBQ3JELFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLG1CQUFtQjtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsNkNBQTZDLGdDQUFnQztBQUM3RSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb25uZWN0b3JzL2Rpc3QvZXNtL21ldGFNYXNrLmpzPzFlNTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IsIFByb3ZpZGVyTm90Rm91bmRFcnJvciwgY3JlYXRlQ29ubmVjdG9yLCBleHRyYWN0UnBjVXJscywgfSBmcm9tICdAd2FnbWkvY29yZSc7XG5pbXBvcnQgeyBSZXNvdXJjZVVuYXZhaWxhYmxlUnBjRXJyb3IsIFN3aXRjaENoYWluRXJyb3IsIFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvciwgZ2V0QWRkcmVzcywgaGV4VG9OdW1iZXIsIG51bWJlclRvSGV4LCB3aXRoUmV0cnksIHdpdGhUaW1lb3V0LCB9IGZyb20gJ3ZpZW0nO1xubWV0YU1hc2sudHlwZSA9ICdtZXRhTWFzayc7XG5leHBvcnQgZnVuY3Rpb24gbWV0YU1hc2socGFyYW1ldGVycyA9IHt9KSB7XG4gICAgbGV0IHNkaztcbiAgICBsZXQgcHJvdmlkZXI7XG4gICAgbGV0IHByb3ZpZGVyUHJvbWlzZTtcbiAgICBsZXQgYWNjb3VudHNDaGFuZ2VkO1xuICAgIGxldCBjaGFpbkNoYW5nZWQ7XG4gICAgbGV0IGNvbm5lY3Q7XG4gICAgbGV0IGRpc3BsYXlVcmk7XG4gICAgbGV0IGRpc2Nvbm5lY3Q7XG4gICAgcmV0dXJuIGNyZWF0ZUNvbm5lY3RvcigoY29uZmlnKSA9PiAoe1xuICAgICAgICBpZDogJ21ldGFNYXNrU0RLJyxcbiAgICAgICAgbmFtZTogJ01ldGFNYXNrJyxcbiAgICAgICAgcmRuczogWydpby5tZXRhbWFzaycsICdpby5tZXRhbWFzay5tb2JpbGUnXSxcbiAgICAgICAgdHlwZTogbWV0YU1hc2sudHlwZSxcbiAgICAgICAgYXN5bmMgc2V0dXAoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGlmIChwcm92aWRlcj8ub24pIHtcbiAgICAgICAgICAgICAgICBpZiAoIWNvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdCA9IHRoaXMub25Db25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFdlIHNob3VsZG4ndCBuZWVkIHRvIGxpc3RlbiBmb3IgYCdhY2NvdW50c0NoYW5nZWQnYCBoZXJlIHNpbmNlIHRoZSBgJ2Nvbm5lY3QnYCBldmVudCBzaG91bGQgc3VmZmljZSAoYW5kIHdhbGxldCBzaG91bGRuJ3QgYmUgY29ubmVjdGVkIHlldCkuXG4gICAgICAgICAgICAgICAgLy8gU29tZSB3YWxsZXRzLCBsaWtlIE1ldGFNYXNrLCBkbyBub3QgaW1wbGVtZW50IHRoZSBgJ2Nvbm5lY3QnYCBldmVudCBhbmQgb3ZlcmxvYWQgYCdhY2NvdW50c0NoYW5nZWQnYCBpbnN0ZWFkLlxuICAgICAgICAgICAgICAgIGlmICghYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgICAgIGFjY291bnRzQ2hhbmdlZCA9IHRoaXMub25BY2NvdW50c0NoYW5nZWQuYmluZCh0aGlzKTtcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2FjY291bnRzQ2hhbmdlZCcsIGFjY291bnRzQ2hhbmdlZCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBjb25uZWN0KHsgY2hhaW5JZCwgaXNSZWNvbm5lY3RpbmcgfSA9IHt9KSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGlmICghZGlzcGxheVVyaSkge1xuICAgICAgICAgICAgICAgIGRpc3BsYXlVcmkgPSB0aGlzLm9uRGlzcGxheVVyaTtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzcGxheV91cmknLCBkaXNwbGF5VXJpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBhY2NvdW50cyA9IFtdO1xuICAgICAgICAgICAgaWYgKGlzUmVjb25uZWN0aW5nKVxuICAgICAgICAgICAgICAgIGFjY291bnRzID0gYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpLmNhdGNoKCgpID0+IFtdKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgbGV0IHNpZ25SZXNwb25zZTtcbiAgICAgICAgICAgICAgICBsZXQgY29ubmVjdFdpdGhSZXNwb25zZTtcbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzPy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdEFuZFNpZ24gfHwgcGFyYW1ldGVycy5jb25uZWN0V2l0aCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdEFuZFNpZ24pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnblJlc3BvbnNlID0gYXdhaXQgc2RrLmNvbm5lY3RBbmRTaWduKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbXNnOiBwYXJhbWV0ZXJzLmNvbm5lY3RBbmRTaWduLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAocGFyYW1ldGVycy5jb25uZWN0V2l0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25uZWN0V2l0aFJlc3BvbnNlID0gYXdhaXQgc2RrLmNvbm5lY3RXaXRoKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiBwYXJhbWV0ZXJzLmNvbm5lY3RXaXRoLm1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBwYXJhbWV0ZXJzLmNvbm5lY3RXaXRoLnBhcmFtcyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY291bnRzID0gYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVxdWVzdGVkQWNjb3VudHMgPSAoYXdhaXQgc2RrLmNvbm5lY3QoKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NvdW50cyA9IHJlcXVlc3RlZEFjY291bnRzLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gU3dpdGNoIHRvIGNoYWluIGlmIHByb3ZpZGVkXG4gICAgICAgICAgICAgICAgbGV0IGN1cnJlbnRDaGFpbklkID0gKGF3YWl0IHRoaXMuZ2V0Q2hhaW5JZCgpKTtcbiAgICAgICAgICAgICAgICBpZiAoY2hhaW5JZCAmJiBjdXJyZW50Q2hhaW5JZCAhPT0gY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGFpbiA9IGF3YWl0IHRoaXMuc3dpdGNoQ2hhaW4oeyBjaGFpbklkIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgaWQ6IGN1cnJlbnRDaGFpbklkIH07XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhaW5JZCA9IGNoYWluPy5pZCA/PyBjdXJyZW50Q2hhaW5JZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGRpc3BsYXlVcmkpIHtcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc3BsYXlfdXJpJywgZGlzcGxheVVyaSk7XG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXlVcmkgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChzaWduUmVzcG9uc2UpXG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLmVtaXQoJ2Nvbm5lY3RBbmRTaWduJywge1xuICAgICAgICAgICAgICAgICAgICAgICAgYWNjb3VudHMsXG4gICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBjdXJyZW50Q2hhaW5JZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25SZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoY29ubmVjdFdpdGhSZXNwb25zZSlcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIuZW1pdCgnY29ubmVjdFdpdGgnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NvdW50cyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluSWQ6IGN1cnJlbnRDaGFpbklkLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29ubmVjdFdpdGhSZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gTWFuYWdlIEVJUC0xMTkzIGV2ZW50IGxpc3RlbmVyc1xuICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZWlwcy5ldGhlcmV1bS5vcmcvRUlQUy9laXAtMTE5MyNldmVudHNcbiAgICAgICAgICAgICAgICBpZiAoY29ubmVjdCkge1xuICAgICAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY29ubmVjdCcsIGNvbm5lY3QpO1xuICAgICAgICAgICAgICAgICAgICBjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBhY2NvdW50c0NoYW5nZWQgPSB0aGlzLm9uQWNjb3VudHNDaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHRoaXMub25EaXNjb25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB7IGFjY291bnRzLCBjaGFpbklkOiBjdXJyZW50Q2hhaW5JZCB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZXJyO1xuICAgICAgICAgICAgICAgIGlmIChlcnJvci5jb2RlID09PSBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IuY29kZSlcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvcihlcnJvcik7XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFJlc291cmNlVW5hdmFpbGFibGVScGNFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUmVzb3VyY2VVbmF2YWlsYWJsZVJwY0Vycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZGlzY29ubmVjdCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgLy8gTWFuYWdlIEVJUC0xMTkzIGV2ZW50IGxpc3RlbmVyc1xuICAgICAgICAgICAgaWYgKGNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGNoYWluQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChkaXNjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc2Nvbm5lY3QnLCBkaXNjb25uZWN0KTtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgY29ubmVjdCA9IHRoaXMub25Db25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2Nvbm5lY3QnLCBjb25uZWN0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGF3YWl0IHNkay50ZXJtaW5hdGUoKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0QWNjb3VudHMoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGNvbnN0IGFjY291bnRzID0gKGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgIG1ldGhvZDogJ2V0aF9hY2NvdW50cycsXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICByZXR1cm4gYWNjb3VudHMubWFwKCh4KSA9PiBnZXRBZGRyZXNzKHgpKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0Q2hhaW5JZCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IHByb3ZpZGVyLmdldENoYWluSWQoKSB8fFxuICAgICAgICAgICAgICAgIChhd2FpdCBwcm92aWRlcj8ucmVxdWVzdCh7IG1ldGhvZDogJ2V0aF9jaGFpbklkJyB9KSk7XG4gICAgICAgICAgICByZXR1cm4gTnVtYmVyKGNoYWluSWQpO1xuICAgICAgICB9LFxuICAgICAgICBhc3luYyBnZXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIGluaXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgICAgICAvLyBVbndyYXBwaW5nIGltcG9ydCBmb3IgVml0ZSBjb21wYXRpYmlsaXR5LlxuICAgICAgICAgICAgICAgIC8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3ZpdGVqcy92aXRlL2lzc3Vlcy85NzAzXG4gICAgICAgICAgICAgICAgY29uc3QgTWV0YU1hc2tTREsgPSBhd2FpdCAoYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGRlZmF1bHQ6IFNESyB9ID0gYXdhaXQgaW1wb3J0KCdAbWV0YW1hc2svc2RrJyk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgU0RLICE9PSAnZnVuY3Rpb24nICYmIHR5cGVvZiBTREsuZGVmYXVsdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBTREsuZGVmYXVsdDtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFNESztcbiAgICAgICAgICAgICAgICB9KSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlYWRvbmx5UlBDTWFwID0ge307XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBjaGFpbiBvZiBjb25maWcuY2hhaW5zKVxuICAgICAgICAgICAgICAgICAgICByZWFkb25seVJQQ01hcFtudW1iZXJUb0hleChjaGFpbi5pZCldID0gZXh0cmFjdFJwY1VybHMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgY2hhaW4sXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc3BvcnRzOiBjb25maWcudHJhbnNwb3J0cyxcbiAgICAgICAgICAgICAgICAgICAgfSk/LlswXTtcbiAgICAgICAgICAgICAgICBzZGsgPSBuZXcgTWV0YU1hc2tTREsoe1xuICAgICAgICAgICAgICAgICAgICBfc291cmNlOiAnd2FnbWknLFxuICAgICAgICAgICAgICAgICAgICBmb3JjZURlbGV0ZVByb3ZpZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgZm9yY2VJbmplY3RQcm92aWRlcjogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGluamVjdFByb3ZpZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgLy8gV29ya2Fyb3VuZCBjYXN0IHNpbmNlIE1ldGFNYXNrIFNESyBkb2VzIG5vdCBzdXBwb3J0IGAnZXhhY3RPcHRpb25hbFByb3BlcnR5VHlwZXMnYFxuICAgICAgICAgICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICAgICAgICAgICAgICByZWFkb25seVJQQ01hcCxcbiAgICAgICAgICAgICAgICAgICAgZGFwcE1ldGFkYXRhOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRlc3QgaWYgbmFtZSBhbmQgdXJsIGFyZSBzZXQgQU5EIG5vdCBlbXB0eVxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogcGFyYW1ldGVycy5kYXBwTWV0YWRhdGE/Lm5hbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHBhcmFtZXRlcnMuZGFwcE1ldGFkYXRhPy5uYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnd2FnbWknLFxuICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiBwYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YT8udXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBwYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YT8udXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnaHR0cHM6Ly93YWdtaS5zaCcsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHVzZURlZXBsaW5rOiBwYXJhbWV0ZXJzLnVzZURlZXBsaW5rID8/IHRydWUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2RrLmluaXQoKTtcbiAgICAgICAgICAgICAgICAvLyBPbiBpbml0aWFsIGxvYWQsIHNvbWV0aW1lcyBgc2RrLmdldFByb3ZpZGVyYCBkb2VzIG5vdCByZXR1cm4gcHJvdmlkZXIuXG4gICAgICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3dldm0vd2FnbWkvaXNzdWVzLzQzNjdcbiAgICAgICAgICAgICAgICAvLyBVc2UgcmVzdWx0IG9mIGBpbml0YCBjYWxsIGlmIGF2YWlsYWJsZS5cbiAgICAgICAgICAgICAgICBjb25zdCBwcm92aWRlciA9ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXN1bHQ/LmFjdGl2ZVByb3ZpZGVyKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdC5hY3RpdmVQcm92aWRlcjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNkay5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgICAgIH0pKCk7XG4gICAgICAgICAgICAgICAgaWYgKCFwcm92aWRlcilcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFByb3ZpZGVyTm90Rm91bmRFcnJvcigpO1xuICAgICAgICAgICAgICAgIHJldHVybiBwcm92aWRlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghcHJvdmlkZXIpIHtcbiAgICAgICAgICAgICAgICBpZiAoIXByb3ZpZGVyUHJvbWlzZSlcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXJQcm9taXNlID0gaW5pdFByb3ZpZGVyKCk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIgPSBhd2FpdCBwcm92aWRlclByb21pc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcHJvdmlkZXI7XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGlzQXV0aG9yaXplZCgpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8gTWV0YU1hc2sgbW9iaWxlIHByb3ZpZGVyIHNvbWV0aW1lcyBmYWlscyB0byBpbW1lZGlhdGVseSByZXNvbHZlXG4gICAgICAgICAgICAgICAgLy8gSlNPTi1SUEMgcmVxdWVzdHMgb24gcGFnZSBsb2FkXG4gICAgICAgICAgICAgICAgY29uc3QgdGltZW91dCA9IDIwMDtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHdpdGhSZXRyeSgoKSA9PiB3aXRoVGltZW91dCgoKSA9PiB0aGlzLmdldEFjY291bnRzKCksIHsgdGltZW91dCB9KSwge1xuICAgICAgICAgICAgICAgICAgICBkZWxheTogdGltZW91dCArIDEsXG4gICAgICAgICAgICAgICAgICAgIHJldHJ5Q291bnQ6IDMsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuICEhYWNjb3VudHMubGVuZ3RoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgc3dpdGNoQ2hhaW4oeyBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLCBjaGFpbklkIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKHgpID0+IHguaWQgPT09IGNoYWluSWQpO1xuICAgICAgICAgICAgaWYgKCFjaGFpbilcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgU3dpdGNoQ2hhaW5FcnJvcihuZXcgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IoKSk7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfc3dpdGNoRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtczogW3sgY2hhaW5JZDogbnVtYmVyVG9IZXgoY2hhaW5JZCkgfV0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gRHVyaW5nIGAnd2FsbGV0X3N3aXRjaEV0aGVyZXVtQ2hhaW4nYCwgTWV0YU1hc2sgbWFrZXMgYSBgJ25ldF92ZXJzaW9uJ2AgUlBDIGNhbGwgdG8gdGhlIHRhcmdldCBjaGFpbi5cbiAgICAgICAgICAgICAgICAvLyBJZiB0aGlzIHJlcXVlc3QgZmFpbHMsIE1ldGFNYXNrIGRvZXMgbm90IGVtaXQgdGhlIGAnY2hhaW5DaGFuZ2VkJ2AgZXZlbnQsIGJ1dCB3aWxsIHN0aWxsIHN3aXRjaCB0aGUgY2hhaW4uXG4gICAgICAgICAgICAgICAgLy8gVG8gY291bnRlciB0aGlzIGJlaGF2aW9yLCB3ZSByZXF1ZXN0IGFuZCBlbWl0IHRoZSBjdXJyZW50IGNoYWluIElEIHRvIGNvbmZpcm0gdGhlIGNoYWluIHN3aXRjaCBlaXRoZXIgdmlhXG4gICAgICAgICAgICAgICAgLy8gdGhpcyBjYWxsYmFjayBvciBhbiBleHRlcm5hbGx5IGVtaXR0ZWQgYCdjaGFpbkNoYW5nZWQnYCBldmVudC5cbiAgICAgICAgICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vTWV0YU1hc2svbWV0YW1hc2stZXh0ZW5zaW9uL2lzc3Vlcy8yNDI0N1xuICAgICAgICAgICAgICAgIGF3YWl0IHdhaXRGb3JDaGFpbklkVG9TeW5jKCk7XG4gICAgICAgICAgICAgICAgYXdhaXQgc2VuZEFuZFdhaXRGb3JDaGFuZ2VFdmVudChjaGFpbklkKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBlcnI7XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgVXNlclJlamVjdGVkUmVxdWVzdEVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBJbmRpY2F0ZXMgY2hhaW4gaXMgbm90IGFkZGVkIHRvIHByb3ZpZGVyXG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQ5MDIgfHxcbiAgICAgICAgICAgICAgICAgICAgLy8gVW53cmFwcGluZyBmb3IgTWV0YU1hc2sgTW9iaWxlXG4gICAgICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9NZXRhTWFzay9tZXRhbWFzay1tb2JpbGUvaXNzdWVzLzI5NDQjaXNzdWVjb21tZW50LTk3Njk4ODcxOVxuICAgICAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgPy5kYXRhPy5vcmlnaW5hbEVycm9yPy5jb2RlID09PSA0OTAyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJsb2NrRXhwbG9yZXJVcmxzOiAoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgZGVmYXVsdDogYmxvY2tFeHBsb3JlciwgLi4uYmxvY2tFeHBsb3JlcnMgfSA9IGNoYWluLmJsb2NrRXhwbG9yZXJzID8/IHt9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5ibG9ja0V4cGxvcmVyVXJscylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIuYmxvY2tFeHBsb3JlclVybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGJsb2NrRXhwbG9yZXIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyLnVybCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLk9iamVjdC52YWx1ZXMoYmxvY2tFeHBsb3JlcnMpLm1hcCgoeCkgPT4geC51cmwpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKCksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBudW1iZXJUb0hleChjaGFpbklkKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluTmFtZTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8uY2hhaW5OYW1lID8/IGNoYWluLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uVXJsczogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8uaWNvblVybHMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYXRpdmVDdXJyZW5jeTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ubmF0aXZlQ3VycmVuY3kgPz9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbi5uYXRpdmVDdXJyZW5jeSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJwY1VybHM6ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXI/LnJwY1VybHM/Lmxlbmd0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIucnBjVXJscztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gW2NoYWluLnJwY1VybHMuZGVmYXVsdD8uaHR0cFswXSA/PyAnJ107XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IHdhaXRGb3JDaGFpbklkVG9TeW5jKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBzZW5kQW5kV2FpdEZvckNoYW5nZUV2ZW50KGNoYWluSWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNoYWluO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZXJyO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFN3aXRjaENoYWluRXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIHdhaXRGb3JDaGFpbklkVG9TeW5jKCkge1xuICAgICAgICAgICAgICAgIC8vIE9uIG1vYmlsZSwgdGhlcmUgaXMgYSByYWNlIGNvbmRpdGlvbiBiZXR3ZWVuIHRoZSByZXN1bHQgb2YgYCd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbidgIGFuZCBgJ2V0aF9jaGFpbklkJ2AuXG4gICAgICAgICAgICAgICAgLy8gVG8gYXZvaWQgdGhpcywgd2Ugd2FpdCBmb3IgYCdldGhfY2hhaW5JZCdgIHRvIHJldHVybiB0aGUgZXhwZWN0ZWQgY2hhaW4gSUQgd2l0aCBhIHJldHJ5IGxvb3AuXG4gICAgICAgICAgICAgICAgYXdhaXQgd2l0aFJldHJ5KGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBoZXhUb051bWJlcihcbiAgICAgICAgICAgICAgICAgICAgLy8gYCdldGhfY2hhaW5JZCdgIGlzIGNhY2hlZCBieSB0aGUgTWV0YU1hc2sgU0RLIHNpZGUgdG8gYXZvaWQgdW5uZWNlc3NhcnkgZGVlcGxpbmtzXG4gICAgICAgICAgICAgICAgICAgIChhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHsgbWV0aG9kOiAnZXRoX2NoYWluSWQnIH0pKSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIGB2YWx1ZWAgZG9lc24ndCBtYXRjaCBleHBlY3RlZCBgY2hhaW5JZGAsIHRocm93IHRvIHRyaWdnZXIgcmV0cnlcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbHVlICE9PSBjaGFpbklkKVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIHJlamVjdGVkIHN3aXRjaCBhZnRlciBhZGRpbmcgbmV0d29yay4nKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICAgICAgZGVsYXk6IDUwLFxuICAgICAgICAgICAgICAgICAgICByZXRyeUNvdW50OiAyMCwgLy8gYW5kcm9pZCBkZXZpY2UgZW5jcnlwdGlvbiBpcyBzbG93ZXJcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIHNlbmRBbmRXYWl0Rm9yQ2hhbmdlRXZlbnQoY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxpc3RlbmVyID0gKChkYXRhKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoJ2NoYWluSWQnIGluIGRhdGEgJiYgZGF0YS5jaGFpbklkID09PSBjaGFpbklkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIub2ZmKCdjaGFuZ2UnLCBsaXN0ZW5lcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIub24oJ2NoYW5nZScsIGxpc3RlbmVyKTtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIuZW1pdCgnY2hhbmdlJywgeyBjaGFpbklkIH0pO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBvbkFjY291bnRzQ2hhbmdlZChhY2NvdW50cykge1xuICAgICAgICAgICAgLy8gRGlzY29ubmVjdCBpZiB0aGVyZSBhcmUgbm8gYWNjb3VudHNcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICAvLyAuLi4gYW5kIHVzaW5nIGJyb3dzZXIgZXh0ZW5zaW9uXG4gICAgICAgICAgICAgICAgaWYgKHNkay5pc0V4dGVuc2lvbkFjdGl2ZSgpKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLm9uRGlzY29ubmVjdCgpO1xuICAgICAgICAgICAgICAgIC8vIEZJWE1FKHVwc3RyZWFtKTogTW9iaWxlIGFwcCBzb21ldGltZXMgZW1pdHMgaW52YWxpZCBgYWNjb3VudHNDaGFuZ2VkYCBldmVudCB3aXRoIGVtcHR5IGFjY291bnRzIGFycmF5XG4gICAgICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBDb25uZWN0IGlmIGVtaXR0ZXIgaXMgbGlzdGVuaW5nIGZvciBjb25uZWN0IGV2ZW50IChlLmcuIGlzIGRpc2Nvbm5lY3RlZCBhbmQgY29ubmVjdHMgdGhyb3VnaCB3YWxsZXQgaW50ZXJmYWNlKVxuICAgICAgICAgICAgZWxzZSBpZiAoY29uZmlnLmVtaXR0ZXIubGlzdGVuZXJDb3VudCgnY29ubmVjdCcpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IChhd2FpdCB0aGlzLmdldENoYWluSWQoKSkudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICB0aGlzLm9uQ29ubmVjdCh7IGNoYWluSWQgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBSZWd1bGFyIGNoYW5nZSBldmVudFxuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2NoYW5nZScsIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjb3VudHM6IGFjY291bnRzLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uQ2hhaW5DaGFuZ2VkKGNoYWluKSB7XG4gICAgICAgICAgICBjb25zdCBjaGFpbklkID0gTnVtYmVyKGNoYWluKTtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2NoYW5nZScsIHsgY2hhaW5JZCB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgb25Db25uZWN0KGNvbm5lY3RJbmZvKSB7XG4gICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHRoaXMuZ2V0QWNjb3VudHMoKTtcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IE51bWJlcihjb25uZWN0SW5mby5jaGFpbklkKTtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Nvbm5lY3QnLCB7IGFjY291bnRzLCBjaGFpbklkIH0pO1xuICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICBpZiAoY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgYWNjb3VudHNDaGFuZ2VkID0gdGhpcy5vbkFjY291bnRzQ2hhbmdlZC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2NoYWluQ2hhbmdlZCcsIGNoYWluQ2hhbmdlZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdGhpcy5vbkRpc2Nvbm5lY3QuYmluZCh0aGlzKTtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzY29ubmVjdCcsIGRpc2Nvbm5lY3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBvbkRpc2Nvbm5lY3QoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgLy8gSWYgTWV0YU1hc2sgZW1pdHMgYSBgY29kZTogMTAxM2AgZXJyb3IsIHdhaXQgZm9yIHJlY29ubmVjdGlvbiBiZWZvcmUgZGlzY29ubmVjdGluZ1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL01ldGFNYXNrL3Byb3ZpZGVycy9wdWxsLzEyMFxuICAgICAgICAgICAgaWYgKGVycm9yICYmIGVycm9yLmNvZGUgPT09IDEwMTMpIHtcbiAgICAgICAgICAgICAgICBpZiAocHJvdmlkZXIgJiYgISEoYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpKS5sZW5ndGgpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Rpc2Nvbm5lY3QnKTtcbiAgICAgICAgICAgIC8vIE1hbmFnZSBFSVAtMTE5MyBldmVudCBsaXN0ZW5lcnNcbiAgICAgICAgICAgIGlmIChjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGlzY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIGNvbm5lY3QgPSB0aGlzLm9uQ29ubmVjdC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG9uRGlzcGxheVVyaSh1cmkpIHtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ21lc3NhZ2UnLCB7IHR5cGU6ICdkaXNwbGF5X3VyaScsIGRhdGE6IHVyaSB9KTtcbiAgICAgICAgfSxcbiAgICB9KSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXRhTWFzay5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/safe.js":
/*!*********************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/safe.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: function() { return /* binding */ safe; }\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n\n\nsafe.type = 'safe';\nfunction safe(parameters = {}) {\n    const { shimDisconnect = false } = parameters;\n    let provider_;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'safe',\n        name: 'Safe',\n        type: safe.type,\n        async connect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await this.getAccounts();\n            const chainId = await this.getChainId();\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n            // Remove disconnected shim if it exists\n            if (shimDisconnect)\n                await config.storage?.removeItem('safe.disconnected');\n            return { accounts, chainId };\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect)\n                await config.storage?.setItem('safe.disconnected', true);\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return (await provider.request({ method: 'eth_accounts' })).map(viem__WEBPACK_IMPORTED_MODULE_2__.getAddress);\n        },\n        async getProvider() {\n            // Only allowed in iframe context\n            const isIframe = typeof window !== 'undefined' && window?.parent !== window;\n            if (!isIframe)\n                return;\n            if (!provider_) {\n                const { default: SDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_safe-global_safe-apps-sdk_dist_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @safe-global/safe-apps-sdk */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\"));\n                const sdk = new SDK(parameters);\n                // `getInfo` hangs when not used in Safe App iFrame\n                // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********\n                const safe = await (0,viem__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => sdk.safe.getInfo(), {\n                    timeout: parameters.unstable_getInfoTimeout ?? 10,\n                });\n                if (!safe)\n                    throw new Error('Could not load Safe information');\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const SafeAppProvider = await (async () => {\n                    const Provider = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_safe-global_safe-apps-provider_dist_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! @safe-global/safe-apps-provider */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-provider/dist/index.js\", 19));\n                    if (typeof Provider.SafeAppProvider !== 'function' &&\n                        typeof Provider.default.SafeAppProvider === 'function')\n                        return Provider.default.SafeAppProvider;\n                    return Provider.SafeAppProvider;\n                })();\n                provider_ = new SafeAppProvider(safe, sdk);\n            }\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return Number(provider.chainId);\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem('safe.disconnected'));\n                if (isDisconnected)\n                    return false;\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        onAccountsChanged() {\n            // Not relevant for Safe because changing account requires app reload.\n        },\n        onChainChanged() {\n            // Not relevant for Safe because Safe smart contract wallets only exist on single chain.\n        },\n        onDisconnect() {\n            config.emitter.emit('disconnect');\n        },\n    }));\n}\n//# sourceMappingURL=safe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/safe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/version.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/version.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: function() { return /* binding */ version; }\n/* harmony export */ });\nconst version = '5.9.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS92ZXJzaW9uLmpzP2MwMDEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnNS45LjAnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/version.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/walletConnect.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnect: function() { return /* binding */ walletConnect; }\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\nwalletConnect.type = 'walletConnect';\nfunction walletConnect(parameters) {\n    const isNewChainsStale = parameters.isNewChainsStale ?? true;\n    let provider_;\n    let providerPromise;\n    const NAMESPACE = 'eip155';\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let sessionDelete;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'walletConnect',\n        name: 'WalletConnect',\n        type: walletConnect.type,\n        async setup() {\n            const provider = await this.getProvider().catch(() => null);\n            if (!provider)\n                return;\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            if (!sessionDelete) {\n                sessionDelete = this.onSessionDelete.bind(this);\n                provider.on('session_delete', sessionDelete);\n            }\n        },\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                if (!displayUri) {\n                    displayUri = this.onDisplayUri;\n                    provider.on('display_uri', displayUri);\n                }\n                let targetChainId = chainId;\n                if (!targetChainId) {\n                    const state = (await config.storage?.getItem('state')) ?? {};\n                    const isChainSupported = config.chains.some((x) => x.id === state.chainId);\n                    if (isChainSupported)\n                        targetChainId = state.chainId;\n                    else\n                        targetChainId = config.chains[0]?.id;\n                }\n                if (!targetChainId)\n                    throw new Error('No chains found on connector.');\n                const isChainsStale = await this.isChainsStale();\n                // If there is an active session with stale chains, disconnect current session.\n                if (provider.session && isChainsStale)\n                    await provider.disconnect();\n                // If there isn't an active session or chains are stale, connect.\n                if (!provider.session || isChainsStale) {\n                    const optionalChains = config.chains\n                        .filter((chain) => chain.id !== targetChainId)\n                        .map((optionalChain) => optionalChain.id);\n                    await provider.connect({\n                        optionalChains: [targetChainId, ...optionalChains],\n                        ...('pairingTopic' in rest\n                            ? { pairingTopic: rest.pairingTopic }\n                            : {}),\n                    });\n                    this.setRequestedChainsIds(config.chains.map((x) => x.id));\n                }\n                // If session exists and chains are authorized, enable provider for required chain\n                const accounts = (await provider.enable()).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                const currentChainId = await this.getChainId();\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                if (!sessionDelete) {\n                    sessionDelete = this.onSessionDelete.bind(this);\n                    provider.on('session_delete', sessionDelete);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user rejected|connection request reset)/i.test(error?.message)) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            try {\n                await provider?.disconnect();\n            }\n            catch (error) {\n                if (!/No matching key/i.test(error.message))\n                    throw error;\n            }\n            finally {\n                if (chainChanged) {\n                    provider?.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider?.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider?.on('connect', connect);\n                }\n                if (accountsChanged) {\n                    provider?.removeListener('accountsChanged', accountsChanged);\n                    accountsChanged = undefined;\n                }\n                if (sessionDelete) {\n                    provider?.removeListener('session_delete', sessionDelete);\n                    sessionDelete = undefined;\n                }\n                this.setRequestedChainsIds([]);\n            }\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return provider.accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getProvider({ chainId } = {}) {\n            async function initProvider() {\n                const optionalChains = config.chains.map((x) => x.id);\n                if (!optionalChains.length)\n                    return;\n                const { EthereumProvider } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_walletconnect_ethereum-provider_dist_index_es_js\").then(__webpack_require__.bind(__webpack_require__, /*! @walletconnect/ethereum-provider */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/dist/index.es.js\"));\n                return await EthereumProvider.init({\n                    ...parameters,\n                    disableProviderPing: true,\n                    optionalChains,\n                    projectId: parameters.projectId,\n                    rpcMap: Object.fromEntries(config.chains.map((chain) => {\n                        const [url] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                            chain,\n                            transports: config.transports,\n                        });\n                        return [chain.id, url];\n                    })),\n                    showQrModal: parameters.showQrModal ?? true,\n                });\n            }\n            if (!provider_) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider_ = await providerPromise;\n                provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY);\n            }\n            if (chainId)\n                await this.switchChain?.({ chainId });\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            return provider.chainId;\n        },\n        async isAuthorized() {\n            try {\n                const [accounts, provider] = await Promise.all([\n                    this.getAccounts(),\n                    this.getProvider(),\n                ]);\n                // If an account does not exist on the session, then the connector is unauthorized.\n                if (!accounts.length)\n                    return false;\n                // If the chains are stale on the session, then the connector is unauthorized.\n                const isChainsStale = await this.isChainsStale();\n                if (isChainsStale && provider.session) {\n                    await provider.disconnect().catch(() => { });\n                    return false;\n                }\n                return true;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ChainNotConfiguredError());\n            try {\n                await Promise.all([\n                    new Promise((resolve) => {\n                        const listener = ({ chainId: currentChainId, }) => {\n                            if (currentChainId === chainId) {\n                                config.emitter.off('change', listener);\n                                resolve();\n                            }\n                        };\n                        config.emitter.on('change', listener);\n                    }),\n                    provider.request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId) }],\n                    }),\n                ]);\n                const requestedChains = await this.getRequestedChainsIds();\n                this.setRequestedChainsIds([...requestedChains, chainId]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (/(user rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                try {\n                    let blockExplorerUrls;\n                    if (addEthereumChainParameter?.blockExplorerUrls)\n                        blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                    else\n                        blockExplorerUrls = chain.blockExplorers?.default.url\n                            ? [chain.blockExplorers?.default.url]\n                            : [];\n                    let rpcUrls;\n                    if (addEthereumChainParameter?.rpcUrls?.length)\n                        rpcUrls = addEthereumChainParameter.rpcUrls;\n                    else\n                        rpcUrls = [...chain.rpcUrls.default.http];\n                    const addEthereumChain = {\n                        blockExplorerUrls,\n                        chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId),\n                        chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                        iconUrls: addEthereumChainParameter?.iconUrls,\n                        nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,\n                        rpcUrls,\n                    };\n                    await provider.request({\n                        method: 'wallet_addEthereumChain',\n                        params: [addEthereumChain],\n                    });\n                    const requestedChains = await this.getRequestedChainsIds();\n                    this.setRequestedChainsIds([...requestedChains, chainId]);\n                    return chain;\n                }\n                catch (error) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const chainId = Number(connectInfo.chainId);\n            const accounts = await this.getAccounts();\n            config.emitter.emit('connect', { accounts, chainId });\n        },\n        async onDisconnect(_error) {\n            this.setRequestedChainsIds([]);\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (sessionDelete) {\n                provider.removeListener('session_delete', sessionDelete);\n                sessionDelete = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n        onSessionDelete() {\n            this.onDisconnect();\n        },\n        getNamespaceChainsIds() {\n            if (!provider_)\n                return [];\n            const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map((account) => Number.parseInt(account.split(':')[1] || ''));\n            return chainIds ?? [];\n        },\n        async getRequestedChainsIds() {\n            return ((await config.storage?.getItem(this.requestedChainsStorageKey)) ?? []);\n        },\n        /**\n         * Checks if the target chains match the chains that were\n         * initially requested by the connector for the WalletConnect session.\n         * If there is a mismatch, this means that the chains on the connector\n         * are considered stale, and need to be revalidated at a later point (via\n         * connection).\n         *\n         * There may be a scenario where a dapp adds a chain to the\n         * connector later on, however, this chain will not have been approved or rejected\n         * by the wallet. In this case, the chain is considered stale.\n         */\n        async isChainsStale() {\n            if (!isNewChainsStale)\n                return false;\n            const connectorChains = config.chains.map((x) => x.id);\n            const namespaceChains = this.getNamespaceChainsIds();\n            if (namespaceChains.length &&\n                !namespaceChains.some((id) => connectorChains.includes(id)))\n                return false;\n            const requestedChains = await this.getRequestedChainsIds();\n            return !connectorChains.every((id) => requestedChains.includes(id));\n        },\n        async setRequestedChainsIds(chains) {\n            await config.storage?.setItem(this.requestedChainsStorageKey, chains);\n        },\n        get requestedChainsStorageKey() {\n            return `${this.id}.requestedChains`;\n        },\n    }));\n}\n//# sourceMappingURL=walletConnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/mock.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mock: function() { return /* binding */ mock; }\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem/utils */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/compat.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../errors/config.js */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\nmock.type = 'mock';\nfunction mock(parameters) {\n    const transactionCache = new Map();\n    const features = parameters.features ??\n        { defaultConnected: false };\n    let connected = features.defaultConnected;\n    let connectedChainId;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'mock',\n        name: 'Mock Connector',\n        type: mock.type,\n        async setup() {\n            connectedChainId = config.chains[0].id;\n        },\n        async connect({ chainId } = {}) {\n            if (features.connectError) {\n                if (typeof features.connectError === 'boolean')\n                    throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to connect.'));\n                throw features.connectError;\n            }\n            const provider = await this.getProvider();\n            const accounts = await provider.request({\n                method: 'eth_requestAccounts',\n            });\n            let currentChainId = await this.getChainId();\n            if (chainId && currentChainId !== chainId) {\n                const chain = await this.switchChain({ chainId });\n                currentChainId = chain.id;\n            }\n            connected = true;\n            return {\n                accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                chainId: currentChainId,\n            };\n        },\n        async disconnect() {\n            connected = false;\n        },\n        async getAccounts() {\n            if (!connected)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ConnectorNotConnectedError();\n            const provider = await this.getProvider();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(hexChainId, 'number');\n        },\n        async isAuthorized() {\n            if (!features.reconnect)\n                return false;\n            if (!connected)\n                return false;\n            const accounts = await this.getAccounts();\n            return !!accounts.length;\n        },\n        async switchChain({ chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_1__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            await provider.request({\n                method: 'wallet_switchEthereumChain',\n                params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(chainId) }],\n            });\n            return chain;\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            connected = false;\n        },\n        async getProvider({ chainId } = {}) {\n            const chain = config.chains.find((x) => x.id === chainId) ?? config.chains[0];\n            const url = chain.rpcUrls.default.http[0];\n            const request = async ({ method, params }) => {\n                // eth methods\n                if (method === 'eth_chainId')\n                    return (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(connectedChainId);\n                if (method === 'eth_requestAccounts')\n                    return parameters.accounts;\n                if (method === 'eth_signTypedData_v4')\n                    if (features.signTypedDataError) {\n                        if (typeof features.signTypedDataError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign typed data.'));\n                        throw features.signTypedDataError;\n                    }\n                // wallet methods\n                if (method === 'wallet_switchEthereumChain') {\n                    if (features.switchChainError) {\n                        if (typeof features.switchChainError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.switchChainError;\n                    }\n                    connectedChainId = (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(params[0].chainId, 'number');\n                    this.onChainChanged(connectedChainId.toString());\n                    return;\n                }\n                if (method === 'wallet_watchAsset') {\n                    if (features.watchAssetError) {\n                        if (typeof features.watchAssetError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.watchAssetError;\n                    }\n                    return connected;\n                }\n                if (method === 'wallet_getCapabilities')\n                    return {\n                        '0x2105': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                            sessionKeys: {\n                                supported: true,\n                            },\n                        },\n                        '0x14A34': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                        },\n                    };\n                if (method === 'wallet_sendCalls') {\n                    const hashes = [];\n                    const calls = params[0].calls;\n                    for (const call of calls) {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_sendTransaction',\n                                params: [call],\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        hashes.push(result);\n                    }\n                    const id = (0,viem__WEBPACK_IMPORTED_MODULE_8__.keccak256)((0,viem__WEBPACK_IMPORTED_MODULE_5__.stringToHex)(JSON.stringify(calls)));\n                    transactionCache.set(id, hashes);\n                    return { id };\n                }\n                if (method === 'wallet_getCallsStatus') {\n                    const hashes = transactionCache.get(params[0]);\n                    if (!hashes)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    const receipts = await Promise.all(hashes.map(async (hash) => {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_getTransactionReceipt',\n                                params: [hash],\n                                id: 0,\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        if (!result)\n                            return null;\n                        return {\n                            blockHash: result.blockHash,\n                            blockNumber: result.blockNumber,\n                            gasUsed: result.gasUsed,\n                            logs: result.logs,\n                            status: result.status,\n                            transactionHash: result.transactionHash,\n                        };\n                    }));\n                    const receipts_ = receipts.filter((x) => x !== null);\n                    if (receipts_.length === 0)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    return {\n                        atomic: false,\n                        chainId: '0x1',\n                        id: params[0],\n                        status: 200,\n                        receipts: receipts_,\n                        version: '2.0.0',\n                    };\n                }\n                if (method === 'wallet_showCallsStatus')\n                    return;\n                // other methods\n                if (method === 'personal_sign') {\n                    if (features.signMessageError) {\n                        if (typeof features.signMessageError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign message.'));\n                        throw features.signMessageError;\n                    }\n                    // Change `personal_sign` to `eth_sign` and swap params\n                    method = 'eth_sign';\n                    params = [params[1], params[0]];\n                }\n                const body = { method, params };\n                const { error, result } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, { body });\n                if (error)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({ body, error, url });\n                return result;\n            };\n            return (0,viem__WEBPACK_IMPORTED_MODULE_9__.custom)({ request })({ retryCount: 0 });\n        },\n    }));\n}\n//# sourceMappingURL=mock.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: function() { return /* binding */ extractRpcUrls; }\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy9leHRyYWN0UnBjVXJscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBO0FBQ0EsNERBQTRELE9BQU87QUFDbkU7QUFDQSw2QkFBNkIsT0FBTztBQUNwQztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy9leHRyYWN0UnBjVXJscy5qcz9kMTA5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBleHRyYWN0UnBjVXJscyhwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBjaGFpbiB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBmYWxsYmFja1VybCA9IGNoYWluLnJwY1VybHMuZGVmYXVsdC5odHRwWzBdO1xuICAgIGlmICghcGFyYW1ldGVycy50cmFuc3BvcnRzKVxuICAgICAgICByZXR1cm4gW2ZhbGxiYWNrVXJsXTtcbiAgICBjb25zdCB0cmFuc3BvcnQgPSBwYXJhbWV0ZXJzLnRyYW5zcG9ydHM/LltjaGFpbi5pZF0/Lih7IGNoYWluIH0pO1xuICAgIGNvbnN0IHRyYW5zcG9ydHMgPSB0cmFuc3BvcnQ/LnZhbHVlPy50cmFuc3BvcnRzIHx8IFt0cmFuc3BvcnRdO1xuICAgIHJldHVybiB0cmFuc3BvcnRzLm1hcCgoeyB2YWx1ZSB9KSA9PiB2YWx1ZT8udXJsIHx8IGZhbGxiYWNrVXJsKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV4dHJhY3RScGNVcmxzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/compat.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/compat.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSocket: function() { return /* binding */ getSocket; },\n/* harmony export */   rpc: function() { return /* binding */ rpc; }\n/* harmony export */ });\n/* harmony import */ var _http_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./http.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/http.js\");\n/* harmony import */ var _webSocket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./webSocket.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/webSocket.js\");\n// TODO(v3): This file is here for backwards compatibility, and to prevent breaking changes.\n// These APIs will be removed in v3.\n\n\nfunction webSocket(socketClient, { body, onError, onResponse }) {\n    socketClient.request({\n        body,\n        onError,\n        onResponse,\n    });\n    return socketClient;\n}\nasync function webSocketAsync(socketClient, { body, timeout = 10_000 }) {\n    return socketClient.requestAsync({\n        body,\n        timeout,\n    });\n}\n/**\n * @deprecated use `getSocketClient` instead.\n *\n * ```diff\n * -import { getSocket } from 'viem/utils'\n * +import { getSocketClient } from 'viem/utils'\n *\n * -const socket = await getSocket(url)\n * +const socketClient = await getSocketClient(url)\n * +const socket = socketClient.socket\n * ```\n */\nasync function getSocket(url) {\n    const client = await (0,_webSocket_js__WEBPACK_IMPORTED_MODULE_0__.getWebSocketRpcClient)(url);\n    return Object.assign(client.socket, {\n        requests: client.requests,\n        subscriptions: client.subscriptions,\n    });\n}\nconst rpc = {\n    /**\n     * @deprecated use `getHttpRpcClient` instead.\n     *\n     * ```diff\n     * -import { rpc } from 'viem/utils'\n     * +import { getHttpRpcClient } from 'viem/utils'\n     *\n     * -rpc.http(url, params)\n     * +const httpClient = getHttpRpcClient(url)\n     * +httpClient.request(params)\n     * ```\n     */\n    http(url, params) {\n        return (0,_http_js__WEBPACK_IMPORTED_MODULE_1__.getHttpRpcClient)(url).request(params);\n    },\n    /**\n     * @deprecated use `getWebSocketRpcClient` instead.\n     *\n     * ```diff\n     * -import { rpc } from 'viem/utils'\n     * +import { getWebSocketRpcClient } from 'viem/utils'\n     *\n     * -rpc.webSocket(url, params)\n     * +const webSocketClient = getWebSocketRpcClient(url)\n     * +webSocketClient.request(params)\n     * ```\n     */\n    webSocket,\n    /**\n     * @deprecated use `getWebSocketRpcClient` instead.\n     *\n     * ```diff\n     * -import { rpc } from 'viem/utils'\n     * +import { getWebSocketRpcClient } from 'viem/utils'\n     *\n     * -const response = await rpc.webSocketAsync(url, params)\n     * +const webSocketClient = getWebSocketRpcClient(url)\n     * +const response = await webSocketClient.requestAsync(params)\n     * ```\n     */\n    webSocketAsync,\n};\n/* c8 ignore end */\n//# sourceMappingURL=compat.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/compat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/socket.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/socket.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSocketRpcClient: function() { return /* binding */ getSocketRpcClient; },\n/* harmony export */   socketClientCache: function() { return /* binding */ socketClientCache; }\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../promise/createBatchScheduler.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js\");\n/* harmony import */ var _promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../promise/withTimeout.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./id.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/id.js\");\n\n\n\n\nconst socketClientCache = /*#__PURE__*/ new Map();\nasync function getSocketRpcClient(parameters) {\n    const { getSocket, keepAlive = true, key = 'socket', reconnect = true, url, } = parameters;\n    const { interval: keepAliveInterval = 30_000 } = typeof keepAlive === 'object' ? keepAlive : {};\n    const { attempts = 5, delay = 2_000 } = typeof reconnect === 'object' ? reconnect : {};\n    const id = JSON.stringify({ keepAlive, key, url, reconnect });\n    let socketClient = socketClientCache.get(id);\n    // If the socket already exists, return it.\n    if (socketClient)\n        return socketClient;\n    let reconnectCount = 0;\n    const { schedule } = (0,_promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_0__.createBatchScheduler)({\n        id,\n        fn: async () => {\n            // Set up a cache for incoming \"synchronous\" requests.\n            const requests = new Map();\n            // Set up a cache for subscriptions (eth_subscribe).\n            const subscriptions = new Map();\n            let error;\n            let socket;\n            let keepAliveTimer;\n            let reconnectInProgress = false;\n            function attemptReconnect() {\n                // Attempt to reconnect.\n                if (reconnect && reconnectCount < attempts) {\n                    if (reconnectInProgress)\n                        return;\n                    reconnectInProgress = true;\n                    reconnectCount++;\n                    // Make sure the previous socket is definitely closed.\n                    socket?.close();\n                    setTimeout(async () => {\n                        await setup().catch(console.error);\n                        reconnectInProgress = false;\n                    }, delay);\n                }\n                // Otherwise, clear all requests and subscriptions.\n                else {\n                    requests.clear();\n                    subscriptions.clear();\n                }\n            }\n            // Set up socket implementation.\n            async function setup() {\n                const result = await getSocket({\n                    onClose() {\n                        // Notify all requests and subscriptions of the closure error.\n                        for (const request of requests.values())\n                            request.onError?.(new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url }));\n                        for (const subscription of subscriptions.values())\n                            subscription.onError?.(new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url }));\n                        attemptReconnect();\n                    },\n                    onError(error_) {\n                        error = error_;\n                        // Notify all requests and subscriptions of the error.\n                        for (const request of requests.values())\n                            request.onError?.(error);\n                        for (const subscription of subscriptions.values())\n                            subscription.onError?.(error);\n                        attemptReconnect();\n                    },\n                    onOpen() {\n                        error = undefined;\n                        reconnectCount = 0;\n                    },\n                    onResponse(data) {\n                        const isSubscription = data.method === 'eth_subscription';\n                        const id = isSubscription ? data.params.subscription : data.id;\n                        const cache = isSubscription ? subscriptions : requests;\n                        const callback = cache.get(id);\n                        if (callback)\n                            callback.onResponse(data);\n                        if (!isSubscription)\n                            cache.delete(id);\n                    },\n                });\n                socket = result;\n                if (keepAlive) {\n                    if (keepAliveTimer)\n                        clearInterval(keepAliveTimer);\n                    keepAliveTimer = setInterval(() => socket.ping?.(), keepAliveInterval);\n                }\n                if (reconnect && subscriptions.size > 0) {\n                    const subscriptionEntries = subscriptions.entries();\n                    for (const [key, { onResponse, body, onError },] of subscriptionEntries) {\n                        if (!body)\n                            continue;\n                        subscriptions.delete(key);\n                        socketClient?.request({ body, onResponse, onError });\n                    }\n                }\n                return result;\n            }\n            await setup();\n            error = undefined;\n            // Create a new socket instance.\n            socketClient = {\n                close() {\n                    keepAliveTimer && clearInterval(keepAliveTimer);\n                    socket.close();\n                    socketClientCache.delete(id);\n                },\n                get socket() {\n                    return socket;\n                },\n                request({ body, onError, onResponse }) {\n                    if (error && onError)\n                        onError(error);\n                    const id = body.id ?? _id_js__WEBPACK_IMPORTED_MODULE_2__.idCache.take();\n                    const callback = (response) => {\n                        if (typeof response.id === 'number' && id !== response.id)\n                            return;\n                        // If we are subscribing to a topic, we want to set up a listener for incoming\n                        // messages.\n                        if (body.method === 'eth_subscribe' &&\n                            typeof response.result === 'string')\n                            subscriptions.set(response.result, {\n                                onResponse: callback,\n                                onError,\n                                body,\n                            });\n                        // If we are unsubscribing from a topic, we want to remove the listener.\n                        if (body.method === 'eth_unsubscribe')\n                            subscriptions.delete(body.params?.[0]);\n                        onResponse(response);\n                    };\n                    requests.set(id, { onResponse: callback, onError });\n                    try {\n                        socket.request({\n                            body: {\n                                jsonrpc: '2.0',\n                                id,\n                                ...body,\n                            },\n                        });\n                    }\n                    catch (error) {\n                        onError?.(error);\n                    }\n                },\n                requestAsync({ body, timeout = 10_000 }) {\n                    return (0,_promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => new Promise((onResponse, onError) => this.request({\n                        body,\n                        onError,\n                        onResponse,\n                    })), {\n                        errorInstance: new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.TimeoutError({ body, url }),\n                        timeout,\n                    });\n                },\n                requests,\n                subscriptions,\n                url,\n            };\n            socketClientCache.set(id, socketClient);\n            return [socketClient];\n        },\n    });\n    const [_, [socketClient_]] = await schedule();\n    return socketClient_;\n}\n//# sourceMappingURL=socket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/socket.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/webSocket.js":
/*!*******************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/webSocket.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getWebSocketRpcClient: function() { return /* binding */ getWebSocketRpcClient; }\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./socket.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/socket.js\");\n\n\nasync function getWebSocketRpcClient(url, options = {}) {\n    const { keepAlive, reconnect } = options;\n    return (0,_socket_js__WEBPACK_IMPORTED_MODULE_0__.getSocketRpcClient)({\n        async getSocket({ onClose, onError, onOpen, onResponse }) {\n            const WebSocket = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_isows__esm_native_js\").then(__webpack_require__.bind(__webpack_require__, /*! isows */ \"(app-pages-browser)/./node_modules/isows/_esm/native.js\")).then((module) => module.WebSocket);\n            const socket = new WebSocket(url);\n            function onClose_() {\n                socket.removeEventListener('close', onClose_);\n                socket.removeEventListener('message', onMessage);\n                socket.removeEventListener('error', onError);\n                socket.removeEventListener('open', onOpen);\n                onClose();\n            }\n            function onMessage({ data }) {\n                try {\n                    const _data = JSON.parse(data);\n                    onResponse(_data);\n                }\n                catch (error) {\n                    onError(error);\n                }\n            }\n            // Setup event listeners for RPC & subscription responses.\n            socket.addEventListener('close', onClose_);\n            socket.addEventListener('message', onMessage);\n            socket.addEventListener('error', onError);\n            socket.addEventListener('open', onOpen);\n            // Wait for the socket to open.\n            if (socket.readyState === WebSocket.CONNECTING) {\n                await new Promise((resolve, reject) => {\n                    if (!socket)\n                        return;\n                    socket.onopen = resolve;\n                    socket.onerror = reject;\n                });\n            }\n            const { close: close_ } = socket;\n            return Object.assign(socket, {\n                close() {\n                    close_.bind(socket)();\n                    onClose_();\n                },\n                ping() {\n                    try {\n                        if (socket.readyState === socket.CLOSED ||\n                            socket.readyState === socket.CLOSING)\n                            throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.WebSocketRequestError({\n                                url: socket.url,\n                                cause: new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url: socket.url }),\n                            });\n                        const body = {\n                            jsonrpc: '2.0',\n                            method: 'net_version',\n                            params: [],\n                        };\n                        socket.send(JSON.stringify(body));\n                    }\n                    catch (error) {\n                        onError(error);\n                    }\n                },\n                request({ body }) {\n                    if (socket.readyState === socket.CLOSED ||\n                        socket.readyState === socket.CLOSING)\n                        throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.WebSocketRequestError({\n                            body,\n                            url: socket.url,\n                            cause: new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url: socket.url }),\n                        });\n                    return socket.send(JSON.stringify(body));\n                },\n            });\n        },\n        keepAlive,\n        reconnect,\n        url,\n    });\n}\n//# sourceMappingURL=webSocket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/webSocket.js\n"));

/***/ })

}]);