"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-7a0575"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js ***!
  \*************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   telegramSvg: function() { return /* binding */ telegramSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst telegramSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"32\" height=\"32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\">\n  <g clip-path=\"url(#a)\">\n    <path fill=\"url(#b)\" d=\"M0 0h32v32H0z\"/>\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.034 15.252c4.975-2.167 8.293-3.596 9.953-4.287 4.74-1.971 5.725-2.314 6.366-2.325.142-.002.457.033.662.*************.33.243.463.022.132.05.435.028.671-.257 2.7-1.368 9.248-1.933 12.27-.24 1.28-.71 1.708-1.167 1.75-.99.091-1.743-.655-2.703-1.284-1.502-.985-2.351-1.598-3.81-2.558-1.684-1.11-.592-1.721.368-2.718.252-.261 4.619-4.233 4.703-4.594.01-.045.02-.213-.08-.301-.1-.09-.246-.059-.353-.035-.15.034-2.55 1.62-7.198 4.758-.682.468-1.298.696-1.851.684-.61-.013-1.782-.344-2.653-.628-1.069-.347-1.918-.53-1.845-1.12.039-.308.462-.623 1.27-.944Z\" fill=\"#fff\"/>\n  </g>\n  <path d=\"M.5 16C.5 7.44 7.44.5 16 .5 24.56.5 31.5 7.44 31.5 16c0 8.56-6.94 15.5-15.5 15.5C7.44 31.5.5 24.56.5 16Z\" stroke=\"#141414\" stroke-opacity=\".05\"/>\n  <defs>\n    <linearGradient id=\"b\" x1=\"1600\" y1=\"0\" x2=\"1600\" y2=\"3176.27\" gradientUnits=\"userSpaceOnUse\">\n      <stop stop-color=\"#2AABEE\"/>\n      <stop offset=\"1\" stop-color=\"#229ED9\"/>\n    </linearGradient>\n    <clipPath id=\"a\">\n      <path d=\"M0 16C0 7.163 7.163 0 16 0s16 7.163 16 16-7.163 16-16 16S0 24.837 0 16Z\" fill=\"#fff\"/>\n    </clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=telegram.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js\n"));

/***/ })

}]);