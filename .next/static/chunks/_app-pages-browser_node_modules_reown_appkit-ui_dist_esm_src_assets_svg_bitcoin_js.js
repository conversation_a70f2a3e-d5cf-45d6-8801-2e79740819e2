"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_bitcoin_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bitcoin.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bitcoin.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitcoinSvg: function() { return /* binding */ bitcoinSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst bitcoinSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"13\" height=\"12\" viewBox=\"0 0 13 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_24458_3947)\">\n<path d=\"M12.6542 7.45143C11.8528 10.6657 8.59691 12.6219 5.38191 11.8204C2.16827 11.019 0.211924 7.76324 1.01369 4.54918C1.81476 1.33451 5.07063 -0.621803 8.28463 0.17954C11.4994 0.980884 13.4556 4.23702 12.6541 7.45149L12.6542 7.45143H12.6542Z\" fill=\"var(--wui-color-gray-glass-005)\"/>\n<path d=\"M9.48002 5.14517C9.59946 4.3467 8.9915 3.91749 8.16017 3.63117L8.42986 2.54957L7.7714 2.38551L7.50886 3.43863C7.33576 3.39546 7.15799 3.35479 6.9813 3.31446L7.24574 2.25439L6.58769 2.09033L6.31786 3.17158C6.17461 3.13896 6.03391 3.10673 5.89741 3.07277L5.89817 3.06937L4.99014 2.84264L4.81498 3.54585C4.81498 3.54585 5.3035 3.65783 5.29321 3.66472C5.55985 3.73126 5.60807 3.90775 5.60006 4.04765L5.29286 5.27984C5.31122 5.2845 5.33503 5.29125 5.36131 5.3018C5.33935 5.29635 5.31597 5.2904 5.29171 5.28459L4.86112 7.01072C4.82853 7.09172 4.74582 7.21328 4.5594 7.16712C4.566 7.17668 4.08082 7.0477 4.08082 7.0477L3.75391 7.80135L4.61079 8.01495C4.77019 8.05492 4.92641 8.09674 5.08024 8.13607L4.80777 9.23007L5.46547 9.39413L5.7353 8.31174C5.91498 8.36051 6.08935 8.4055 6.26005 8.44791L5.99112 9.52519L6.64961 9.68925L6.92206 8.5973C8.04487 8.80978 8.88914 8.72411 9.2445 7.70862C9.53085 6.89103 9.23025 6.41944 8.63954 6.11192C9.06978 6.0127 9.39385 5.72975 9.48026 5.14525L9.48005 5.14511L9.48002 5.14517ZM7.9756 7.25457C7.7721 8.07216 6.39541 7.6302 5.94906 7.51937L6.31064 6.07001C6.75696 6.18142 8.18827 6.40191 7.97562 7.25457H7.9756ZM8.17923 5.13332C7.9936 5.877 6.84776 5.49918 6.47606 5.40653L6.80389 4.09205C7.17559 4.18469 8.37261 4.35761 8.17929 5.13332H8.17923Z\" fill=\"var(--wui-color-gray-glass-090)\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_24458_3947\">\n<rect width=\"11.9997\" height=\"12\" fill=\"white\" transform=\"translate(0.833984)\"/>\n</clipPath>\n</defs>\n</svg>\n`;\n//# sourceMappingURL=bitcoin.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bitcoin.js\n"));

/***/ })

}]);