"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_twitch_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   twitchSvg: function() { return /* binding */ twitchSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst twitchSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#5A3E85\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M18.22 25.7 20 23.91h3.34l2.1-2.1v-6.68H15.4v8.78h2.82v1.77Zm3.87-8.16h1.25v3.66H22.1v-3.66Zm-3.34 0H20v3.66h-1.25v-3.66ZM20 7.9a12 12 0 1 0 0 24 12 12 0 0 0 0-24Zm6.69 14.56-3.66 3.66h-2.72l-1.77 1.78h-1.88V26.1H13.3v-9.82l.94-2.4H26.7v8.56Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=twitch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3R3aXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixrQkFBa0Isd0NBQUc7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3R3aXRjaC5qcz80MmIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgdHdpdGNoU3ZnID0gc3ZnIGA8c3ZnIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCA0MCA0MFwiPlxuICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2EpXCI+XG4gICAgPGcgY2xpcC1wYXRoPVwidXJsKCNiKVwiPlxuICAgICAgPGNpcmNsZSBjeD1cIjIwXCIgY3k9XCIxOS44OVwiIHI9XCIyMFwiIGZpbGw9XCIjNUEzRTg1XCIgLz5cbiAgICAgIDxnIGNsaXAtcGF0aD1cInVybCgjYylcIj5cbiAgICAgICAgPHBhdGhcbiAgICAgICAgICBmaWxsPVwiI2ZmZlwiXG4gICAgICAgICAgZD1cIk0xOC4yMiAyNS43IDIwIDIzLjkxaDMuMzRsMi4xLTIuMXYtNi42OEgxNS40djguNzhoMi44MnYxLjc3Wm0zLjg3LTguMTZoMS4yNXYzLjY2SDIyLjF2LTMuNjZabS0zLjM0IDBIMjB2My42NmgtMS4yNXYtMy42NlpNMjAgNy45YTEyIDEyIDAgMSAwIDAgMjQgMTIgMTIgMCAwIDAgMC0yNFptNi42OSAxNC41Ni0zLjY2IDMuNjZoLTIuNzJsLTEuNzcgMS43OGgtMS44OFYyNi4xSDEzLjN2LTkuODJsLjk0LTIuNEgyNi43djguNTZaXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZz5cbiAgICA8L2c+XG4gIDwvZz5cbiAgPGRlZnM+XG4gICAgPGNsaXBQYXRoIGlkPVwiYVwiPjxyZWN0IHdpZHRoPVwiNDBcIiBoZWlnaHQ9XCI0MFwiIGZpbGw9XCIjZmZmXCIgcng9XCIyMFwiIC8+PC9jbGlwUGF0aD5cbiAgICA8Y2xpcFBhdGggaWQ9XCJiXCI+PHBhdGggZmlsbD1cIiNmZmZcIiBkPVwiTTAgMGg0MHY0MEgwelwiIC8+PC9jbGlwUGF0aD5cbiAgICA8Y2xpcFBhdGggaWQ9XCJjXCI+PHBhdGggZmlsbD1cIiNmZmZcIiBkPVwiTTggNy44OWgyNHYyNEg4elwiIC8+PC9jbGlwUGF0aD5cbiAgPC9kZWZzPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR3aXRjaC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js\n"));

/***/ })

}]);