"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_farcaster_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   farcasterSvg: function() { return /* binding */ farcasterSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst farcasterSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg style=\"border-radius: 9999px; overflow: hidden;\"  fill=\"none\" viewBox=\"0 0 1000 1000\">\n  <rect width=\"1000\" height=\"1000\" rx=\"9999\" ry=\"9999\" fill=\"#855DCD\"/>\n  <path fill=\"#855DCD\" d=\"M0 0h1000v1000H0V0Z\" />\n  <path\n    fill=\"#fff\"\n    d=\"M320 248h354v504h-51.96V521.13h-.5c-5.76-63.8-59.31-113.81-124.54-113.81s-118.78 50-124.53 113.81h-.5V752H320V248Z\"\n  />\n  <path\n    fill=\"#fff\"\n    d=\"m225 320 21.16 71.46h17.9v289.09a16.29 16.29 0 0 0-16.28 16.24v19.49h-3.25a16.3 16.3 0 0 0-16.28 16.24V752h182.26v-19.48a16.22 16.22 0 0 0-16.28-16.24h-3.25v-19.5a16.22 16.22 0 0 0-16.28-16.23h-19.52V320H225Zm400.3 360.55a16.3 16.3 0 0 0-15.04 10.02 16.2 16.2 0 0 0-1.24 6.22v19.49h-3.25a16.29 16.29 0 0 0-16.27 16.24V752h182.24v-19.48a16.23 16.23 0 0 0-16.27-16.24h-3.25v-19.5a16.2 16.2 0 0 0-10.04-15 16.3 16.3 0 0 0-6.23-1.23v-289.1h17.9L775 320H644.82v360.55H625.3Z\"\n  />\n</svg>`;\n//# sourceMappingURL=farcaster.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2ZhcmNhc3Rlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixxQkFBcUIsd0NBQUcscUNBQXFDLGlCQUFpQjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvZmFyY2FzdGVyLmpzPzViODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBmYXJjYXN0ZXJTdmcgPSBzdmcgYDxzdmcgc3R5bGU9XCJib3JkZXItcmFkaXVzOiA5OTk5cHg7IG92ZXJmbG93OiBoaWRkZW47XCIgIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAxMDAwIDEwMDBcIj5cbiAgPHJlY3Qgd2lkdGg9XCIxMDAwXCIgaGVpZ2h0PVwiMTAwMFwiIHJ4PVwiOTk5OVwiIHJ5PVwiOTk5OVwiIGZpbGw9XCIjODU1RENEXCIvPlxuICA8cGF0aCBmaWxsPVwiIzg1NURDRFwiIGQ9XCJNMCAwaDEwMDB2MTAwMEgwVjBaXCIgLz5cbiAgPHBhdGhcbiAgICBmaWxsPVwiI2ZmZlwiXG4gICAgZD1cIk0zMjAgMjQ4aDM1NHY1MDRoLTUxLjk2VjUyMS4xM2gtLjVjLTUuNzYtNjMuOC01OS4zMS0xMTMuODEtMTI0LjU0LTExMy44MXMtMTE4Ljc4IDUwLTEyNC41MyAxMTMuODFoLS41Vjc1MkgzMjBWMjQ4WlwiXG4gIC8+XG4gIDxwYXRoXG4gICAgZmlsbD1cIiNmZmZcIlxuICAgIGQ9XCJtMjI1IDMyMCAyMS4xNiA3MS40NmgxNy45djI4OS4wOWExNi4yOSAxNi4yOSAwIDAgMC0xNi4yOCAxNi4yNHYxOS40OWgtMy4yNWExNi4zIDE2LjMgMCAwIDAtMTYuMjggMTYuMjRWNzUyaDE4Mi4yNnYtMTkuNDhhMTYuMjIgMTYuMjIgMCAwIDAtMTYuMjgtMTYuMjRoLTMuMjV2LTE5LjVhMTYuMjIgMTYuMjIgMCAwIDAtMTYuMjgtMTYuMjNoLTE5LjUyVjMyMEgyMjVabTQwMC4zIDM2MC41NWExNi4zIDE2LjMgMCAwIDAtMTUuMDQgMTAuMDIgMTYuMiAxNi4yIDAgMCAwLTEuMjQgNi4yMnYxOS40OWgtMy4yNWExNi4yOSAxNi4yOSAwIDAgMC0xNi4yNyAxNi4yNFY3NTJoMTgyLjI0di0xOS40OGExNi4yMyAxNi4yMyAwIDAgMC0xNi4yNy0xNi4yNGgtMy4yNXYtMTkuNWExNi4yIDE2LjIgMCAwIDAtMTAuMDQtMTUgMTYuMyAxNi4zIDAgMCAwLTYuMjMtMS4yM3YtMjg5LjFoMTcuOUw3NzUgMzIwSDY0NC44MnYzNjAuNTVINjI1LjNaXCJcbiAgLz5cbjwvc3ZnPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mYXJjYXN0ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js\n"));

/***/ })

}]);