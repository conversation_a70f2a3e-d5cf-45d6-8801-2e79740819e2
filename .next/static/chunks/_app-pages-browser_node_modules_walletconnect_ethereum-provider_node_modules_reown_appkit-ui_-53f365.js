"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-53f365"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   etherscanSvg: function() { return /* binding */ etherscanSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst etherscanSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 16 16\">\n  <path\n    fill=\"currentColor\"\n    d=\"M4.25 7a.63.63 0 0 0-.63.63v3.97c0 .28-.2.51-.47.54l-.75.07a.93.93 0 0 1-.9-.47A7.51 7.51 0 0 1 5.54.92a7.5 7.5 0 0 1 9.54 4.62c.12.35.06.72-.16 1-.74.97-1.68 1.78-2.6 2.44V4.44a.64.64 0 0 0-.63-.64h-1.06c-.35 0-.63.3-.63.64v5.5c0 .23-.12.42-.32.5l-.52.23V6.05c0-.36-.3-.64-.64-.64H7.45c-.35 0-.64.3-.64.64v4.97c0 .25-.17.46-.4.52a5.8 5.8 0 0 0-.45.11v-4c0-.36-.3-.65-.64-.65H4.25ZM14.07 12.4A7.49 7.49 0 0 1 3.6 14.08c4.09-.58 9.14-2.5 11.87-6.6v.03a7.56 7.56 0 0 1-1.41 4.91Z\"\n  />\n</svg>`;\n//# sourceMappingURL=etherscan.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9ldGhlcnNjYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIscUJBQXFCLHdDQUFHO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHdhbGxldGNvbm5lY3QvZXRoZXJldW0tcHJvdmlkZXIvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvZXRoZXJzY2FuLmpzPzljZWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBldGhlcnNjYW5TdmcgPSBzdmcgYDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCI+XG4gIDxwYXRoXG4gICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgZD1cIk00LjI1IDdhLjYzLjYzIDAgMCAwLS42My42M3YzLjk3YzAgLjI4LS4yLjUxLS40Ny41NGwtLjc1LjA3YS45My45MyAwIDAgMS0uOS0uNDdBNy41MSA3LjUxIDAgMCAxIDUuNTQuOTJhNy41IDcuNSAwIDAgMSA5LjU0IDQuNjJjLjEyLjM1LjA2LjcyLS4xNiAxLS43NC45Ny0xLjY4IDEuNzgtMi42IDIuNDRWNC40NGEuNjQuNjQgMCAwIDAtLjYzLS42NGgtMS4wNmMtLjM1IDAtLjYzLjMtLjYzLjY0djUuNWMwIC4yMy0uMTIuNDItLjMyLjVsLS41Mi4yM1Y2LjA1YzAtLjM2LS4zLS42NC0uNjQtLjY0SDcuNDVjLS4zNSAwLS42NC4zLS42NC42NHY0Ljk3YzAgLjI1LS4xNy40Ni0uNC41MmE1LjggNS44IDAgMCAwLS40NS4xMXYtNGMwLS4zNi0uMy0uNjUtLjY0LS42NUg0LjI1Wk0xNC4wNyAxMi40QTcuNDkgNy40OSAwIDAgMSAzLjYgMTQuMDhjNC4wOS0uNTggOS4xNC0yLjUgMTEuODctNi42di4wM2E3LjU2IDcuNTYgMCAwIDEtMS40MSA0LjkxWlwiXG4gIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXRoZXJzY2FuLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js\n"));

/***/ })

}]);