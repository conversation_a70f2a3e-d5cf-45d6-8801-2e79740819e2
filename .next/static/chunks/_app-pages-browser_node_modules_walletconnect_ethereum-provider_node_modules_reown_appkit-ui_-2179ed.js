"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-2179ed"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js ***!
  \***********************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cursorTransparentSvg: function() { return /* binding */ cursorTransparentSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst cursorTransparentSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 14 6\">\n  <path style=\"fill: var(--wui-color-bg-150);\" d=\"M0 1h14L9.21 5.12a3.31 3.31 0 0 1-4.49 0L0 1Z\" />\n  <path\n    style=\"stroke: var(--wui-color-inverse-100);\"\n    stroke-opacity=\".05\"\n    d=\"M1.33 1.5h11.32L8.88 4.75l-.01.01a2.81 2.81 0 0 1-3.8 0l-.02-.01L1.33 1.5Z\"\n  />\n  <path\n    style=\"fill: var(--wui-color-bg-150);\"\n    d=\"M1.25.71h11.5L9.21 3.88a3.31 3.31 0 0 1-4.49 0L1.25.71Z\"\n  />\n</svg> `;\n//# sourceMappingURL=cursor-transparent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9jdXJzb3ItdHJhbnNwYXJlbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsNkJBQTZCLHdDQUFHO0FBQ3ZDLDZDQUE2QztBQUM3QztBQUNBLGdEQUFnRDtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHdhbGxldGNvbm5lY3QvZXRoZXJldW0tcHJvdmlkZXIvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvY3Vyc29yLXRyYW5zcGFyZW50LmpzPzBiOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBjdXJzb3JUcmFuc3BhcmVudFN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMTQgNlwiPlxuICA8cGF0aCBzdHlsZT1cImZpbGw6IHZhcigtLXd1aS1jb2xvci1iZy0xNTApO1wiIGQ9XCJNMCAxaDE0TDkuMjEgNS4xMmEzLjMxIDMuMzEgMCAwIDEtNC40OSAwTDAgMVpcIiAvPlxuICA8cGF0aFxuICAgIHN0eWxlPVwic3Ryb2tlOiB2YXIoLS13dWktY29sb3ItaW52ZXJzZS0xMDApO1wiXG4gICAgc3Ryb2tlLW9wYWNpdHk9XCIuMDVcIlxuICAgIGQ9XCJNMS4zMyAxLjVoMTEuMzJMOC44OCA0Ljc1bC0uMDEuMDFhMi44MSAyLjgxIDAgMCAxLTMuOCAwbC0uMDItLjAxTDEuMzMgMS41WlwiXG4gIC8+XG4gIDxwYXRoXG4gICAgc3R5bGU9XCJmaWxsOiB2YXIoLS13dWktY29sb3ItYmctMTUwKTtcIlxuICAgIGQ9XCJNMS4yNS43MWgxMS41TDkuMjEgMy44OGEzLjMxIDMuMzEgMCAwIDEtNC40OSAwTDEuMjUuNzFaXCJcbiAgLz5cbjwvc3ZnPiBgO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3Vyc29yLXRyYW5zcGFyZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js\n"));

/***/ })

}]);