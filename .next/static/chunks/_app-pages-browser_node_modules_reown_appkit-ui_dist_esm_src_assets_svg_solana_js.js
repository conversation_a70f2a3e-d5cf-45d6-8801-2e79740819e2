"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_solana_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/solana.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/solana.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   solanaSvg: function() { return /* binding */ solanaSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst solanaSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"13\" height=\"12\" viewBox=\"0 0 13 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_24458_3940)\">\n<path d=\"M6.49987 12C9.81351 12 12.4997 9.31371 12.4997 6C12.4997 2.68629 9.81351 0 6.49987 0C3.18623 0 0.5 2.68629 0.5 6C0.5 9.31371 3.18623 12 6.49987 12Z\" fill=\"var(--wui-color-gray-glass-005)\"/>\n<path d=\"M9.89298 7.73057L8.76102 8.91828C8.73642 8.94408 8.70664 8.96465 8.67356 8.97871C8.64047 8.99277 8.60478 9.00002 8.56872 9H3.2027C3.1771 9 3.15205 8.99268 3.13064 8.97895C3.10923 8.96521 3.09239 8.94565 3.08218 8.92268C3.07197 8.8997 3.06885 8.87431 3.07319 8.84963C3.07753 8.82494 3.08915 8.80203 3.10662 8.78371L4.23943 7.596C4.26397 7.57027 4.29365 7.54974 4.32662 7.53569C4.3596 7.52163 4.39518 7.51435 4.43115 7.51428H9.79688C9.82248 7.51428 9.84752 7.52161 9.86895 7.53534C9.89033 7.54908 9.90717 7.56864 9.91742 7.59161C9.92761 7.61459 9.93073 7.63997 9.92638 7.66466C9.92204 7.68935 9.91043 7.71226 9.89298 7.73057ZM8.76102 5.33885C8.73642 5.31305 8.70664 5.29248 8.67356 5.27843C8.64047 5.26437 8.60478 5.25713 8.56872 5.25715H3.2027C3.1771 5.25715 3.15205 5.26446 3.13064 5.2782C3.10923 5.29194 3.09239 5.31149 3.08218 5.33446C3.07197 5.35744 3.06885 5.38283 3.07319 5.40752C3.07753 5.4322 3.08915 5.45511 3.10662 5.47343L4.23943 6.66115C4.26397 6.68688 4.29365 6.7074 4.32662 6.72146C4.3596 6.73551 4.39518 6.7428 4.43115 6.74285H9.79688C9.82248 6.74285 9.84752 6.73554 9.86895 6.7218C9.89033 6.70806 9.90717 6.68851 9.91742 6.66554C9.92761 6.64256 9.93073 6.61717 9.92638 6.59248C9.92204 6.5678 9.91043 6.54489 9.89298 6.52657L8.76102 5.33885ZM3.2027 4.48572H8.56872C8.60478 4.48573 8.64047 4.47849 8.67356 4.46443C8.70664 4.45037 8.73642 4.4298 8.76102 4.404L9.89298 3.21629C9.91043 3.19797 9.92204 3.17506 9.92638 3.15037C9.93073 3.12569 9.92761 3.1003 9.91742 3.07732C9.90717 3.05435 9.89033 3.03479 9.86895 3.02106C9.84752 3.00732 9.82248 3 9.79688 3H4.43115C4.39518 3.00006 4.3596 3.00734 4.32662 3.0214C4.29365 3.03545 4.26397 3.05598 4.23943 3.08171L3.10691 4.26943C3.08946 4.28772 3.07785 4.31061 3.0735 4.33527C3.06914 4.35993 3.07224 4.3853 3.08241 4.40826C3.09258 4.43122 3.10938 4.45078 3.13075 4.46454C3.15212 4.4783 3.17712 4.48565 3.2027 4.48572Z\" fill=\"var(--wui-color-gray-glass-090)\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_24458_3940\">\n<rect width=\"11.9997\" height=\"12\" fill=\"white\" transform=\"translate(0.5)\"/>\n</clipPath>\n</defs>\n</svg>\n`;\n//# sourceMappingURL=solana.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/solana.js\n"));

/***/ })

}]);