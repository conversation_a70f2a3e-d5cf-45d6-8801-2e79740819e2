"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_x-mark_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x-mark.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x-mark.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xMarkSvg: function() { return /* binding */ xMarkSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst xMarkSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 12 12\" fill=\"none\">\n  <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.9659 1.96541C2.30063 1.63067 2.84334 1.63067 3.17808 1.96541L6.00056 4.78789L8.82304 1.96541C9.15777 1.63067 9.70049 1.63067 10.0352 1.96541C10.37 2.30014 10.37 2.84285 10.0352 3.17759L7.21274 6.00007L10.0352 8.82255C10.37 9.15729 10.37 9.7 10.0352 10.0347C9.70049 10.3695 9.15777 10.3695 8.82304 10.0347L6.00056 7.21225L3.17808 10.0347C2.84334 10.3695 2.30063 10.3695 1.9659 10.0347C1.63116 9.7 1.63116 9.15729 1.9659 8.82255L4.78837 6.00007L1.9659 3.17759C1.63116 2.84285 1.63116 2.30014 1.9659 1.96541Z\" fill=\"currentColor\"/>\n</svg>`;\n//# sourceMappingURL=x-mark.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3gtbWFyay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixpQkFBaUIsd0NBQUc7QUFDM0I7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3gtbWFyay5qcz8xZjk3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgeE1hcmtTdmcgPSBzdmcgYDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIHZpZXdCb3g9XCIwIDAgMTIgMTJcIiBmaWxsPVwibm9uZVwiPlxuICA8cGF0aCBmaWxsLXJ1bGU9XCJldmVub2RkXCIgY2xpcC1ydWxlPVwiZXZlbm9kZFwiIGQ9XCJNMS45NjU5IDEuOTY1NDFDMi4zMDA2MyAxLjYzMDY3IDIuODQzMzQgMS42MzA2NyAzLjE3ODA4IDEuOTY1NDFMNi4wMDA1NiA0Ljc4Nzg5TDguODIzMDQgMS45NjU0MUM5LjE1Nzc3IDEuNjMwNjcgOS43MDA0OSAxLjYzMDY3IDEwLjAzNTIgMS45NjU0MUMxMC4zNyAyLjMwMDE0IDEwLjM3IDIuODQyODUgMTAuMDM1MiAzLjE3NzU5TDcuMjEyNzQgNi4wMDAwN0wxMC4wMzUyIDguODIyNTVDMTAuMzcgOS4xNTcyOSAxMC4zNyA5LjcgMTAuMDM1MiAxMC4wMzQ3QzkuNzAwNDkgMTAuMzY5NSA5LjE1Nzc3IDEwLjM2OTUgOC44MjMwNCAxMC4wMzQ3TDYuMDAwNTYgNy4yMTIyNUwzLjE3ODA4IDEwLjAzNDdDMi44NDMzNCAxMC4zNjk1IDIuMzAwNjMgMTAuMzY5NSAxLjk2NTkgMTAuMDM0N0MxLjYzMTE2IDkuNyAxLjYzMTE2IDkuMTU3MjkgMS45NjU5IDguODIyNTVMNC43ODgzNyA2LjAwMDA3TDEuOTY1OSAzLjE3NzU5QzEuNjMxMTYgMi44NDI4NSAxLjYzMTE2IDIuMzAwMTQgMS45NjU5IDEuOTY1NDFaXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiLz5cbjwvc3ZnPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD14LW1hcmsuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x-mark.js\n"));

/***/ })

}]);