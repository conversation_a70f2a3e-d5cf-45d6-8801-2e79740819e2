"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-13ea1e"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js ***!
  \************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   browserSvg: function() { return /* binding */ browserSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst browserSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M4 6.4a1 1 0 0 1-.46.89 6.98 6.98 0 0 0 .38 6.18A7 7 0 0 0 16.46 7.3a1 1 0 0 1-.47-.92 7 7 0 0 0-12 .03Zm-2.02-.5a9 9 0 1 1 16.03 8.2A9 9 0 0 1 1.98 5.9Z\"\n    clip-rule=\"evenodd\"\n  />\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M6.03 8.63c-1.46-.3-2.72-.75-3.6-1.35l-.02-.01-.14-.11a1 1 0 0 1 1.2-1.6l.1.08c.6.4 1.52.74 2.69 1 .16-.99.39-1.88.67-2.65.3-.79.68-1.5 1.15-2.02A2.58 2.58 0 0 1 9.99 1c.8 0 1.45.44 **********.52.84 1.23 1.14 **********.52 1.66.68 2.64a8 8 0 0 0 2.7-1l.26-.18h.48a1 1 0 0 1 .12 2c-.86.51-2.01.91-3.34 1.18a22.24 22.24 0 0 1-.03 3.19c1.45.29 2.7.73 3.58 1.31a1 1 0 0 1-1.1 1.68c-.6-.4-1.56-.76-2.75-1-.15.8-.36 1.55-.6 2.2-.3.79-.67 1.5-1.14 2.02-.47.53-1.12.97-1.92.97-.8 0-1.45-.44-1.91-.97a6.51 6.51 0 0 1-1.15-2.02c-.24-.65-.44-1.4-.6-2.2-1.18.24-2.13.6-2.73.99a1 1 0 1 1-1.1-1.67c.88-.58 2.12-1.03 3.57-1.31a22.03 22.03 0 0 1-.04-3.2Zm2.2-1.7c.15-.86.34-1.61.58-2.24.24-.65.51-1.12.76-1.4.25-.28.4-.29.42-.29.03 0 .**********.***********.77 *********.43 1.37.57 2.22a19.96 19.96 0 0 1-3.52 0Zm-.18 4.6a20.1 20.1 0 0 1-.03-2.62 21.95 21.95 0 0 0 3.94 0 20.4 20.4 0 0 1-.03 2.63 21.97 21.97 0 0 0-3.88 0Zm.27 2c.13.66.3 1.26.49 **********.51 1.12.76 *********.**********.03 0 .17-.01.42-.3.25-.27.52-.74.77-1.4.19-.5.36-1.1.49-1.78a20.03 20.03 0 0 0-3.35 0Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=browser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js\n"));

/***/ })

}]);