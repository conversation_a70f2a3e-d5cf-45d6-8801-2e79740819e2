"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-e62a0a"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js ***!
  \******************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnectBrownSvg: function() { return /* binding */ walletConnectBrownSvg; },\n/* harmony export */   walletConnectLightBrownSvg: function() { return /* binding */ walletConnectLightBrownSvg; },\n/* harmony export */   walletConnectSvg: function() { return /* binding */ walletConnectSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst walletConnectSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 96 67\">\n  <path\n    fill=\"currentColor\"\n    d=\"M25.32 18.8a32.56 32.56 0 0 1 45.36 0l1.5 1.47c.63.62.63 1.61 0 2.22l-5.15 5.05c-.31.3-.82.3-1.14 0l-2.07-2.03a22.71 22.71 0 0 0-31.64 0l-2.22 2.18c-.31.3-.82.3-1.14 0l-5.15-5.05a1.55 1.55 0 0 1 0-2.22l1.65-1.62Zm56.02 10.44 4.59 4.5c.63.6.63 1.6 0 2.21l-20.7 20.26c-.62.61-1.63.61-2.26 0L48.28 41.83a.4.4 0 0 0-.56 0L33.03 56.21c-.63.61-1.64.61-2.27 0L10.07 35.95a1.55 1.55 0 0 1 0-2.22l4.59-4.5a1.63 1.63 0 0 1 2.27 0L31.6 43.63a.4.4 0 0 0 .57 0l14.69-14.38a1.63 1.63 0 0 1 2.26 0l14.69 14.38a.4.4 0 0 0 .57 0l14.68-14.38a1.63 1.63 0 0 1 2.27 0Z\"\n  />\n  <path\n    stroke=\"#000\"\n    stroke-opacity=\".1\"\n    d=\"M25.67 19.15a32.06 32.06 0 0 1 44.66 0l1.5 1.48c.43.42.43 1.09 0 1.5l-5.15 5.05a.31.31 0 0 1-.44 0l-2.07-2.03a23.21 23.21 0 0 0-32.34 0l-2.22 2.18a.31.31 0 0 1-.44 0l-5.15-5.05a1.05 1.05 0 0 1 0-1.5l1.65-1.63ZM81 29.6l4.6 4.5c.42.41.42 1.09 0 1.5l-20.7 20.26c-.43.43-1.14.43-1.57 0L48.63 41.47a.9.9 0 0 0-1.26 0L32.68 55.85c-.43.43-1.14.43-1.57 0L10.42 35.6a1.05 1.05 0 0 1 0-1.5l4.59-4.5a1.13 1.13 0 0 1 1.57 0l14.68 14.38a.9.9 0 0 0 1.27 0l-.35-.35.35.35L47.22 29.6a1.13 1.13 0 0 1 1.56 0l14.7 14.38a.9.9 0 0 0 1.26 0L79.42 29.6a1.13 1.13 0 0 1 1.57 0Z\"\n  />\n</svg>`;\nconst walletConnectLightBrownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_22274_4692)\">\n<path d=\"M0 6.64C0 4.17295 0 2.93942 0.525474 2.01817C0.880399 1.39592 1.39592 0.880399 2.01817 0.525474C2.93942 0 4.17295 0 6.64 0H9.36C11.8271 0 13.0606 0 13.9818 0.525474C14.6041 0.880399 15.1196 1.39592 15.4745 2.01817C16 2.93942 16 4.17295 16 6.64V9.36C16 11.8271 16 13.0606 15.4745 13.9818C15.1196 14.6041 14.6041 15.1196 13.9818 15.4745C13.0606 16 11.8271 16 9.36 16H6.64C4.17295 16 2.93942 16 2.01817 15.4745C1.39592 15.1196 0.880399 14.6041 0.525474 13.9818C0 13.0606 0 11.8271 0 9.36V6.64Z\" fill=\"#C7B994\"/>\n<path d=\"M4.49038 5.76609C6.42869 3.86833 9.5713 3.86833 11.5096 5.76609L11.7429 5.99449C11.8398 6.08938 11.8398 6.24323 11.7429 6.33811L10.9449 7.11942C10.8964 7.16686 10.8179 7.16686 10.7694 7.11942L10.4484 6.80512C9.09617 5.48119 6.90381 5.48119 5.5516 6.80512L5.20782 7.14171C5.15936 7.18915 5.08079 7.18915 5.03234 7.14171L4.23434 6.3604C4.13742 6.26552 4.13742 6.11167 4.23434 6.01678L4.49038 5.76609ZM13.1599 7.38192L13.8702 8.07729C13.9671 8.17217 13.9671 8.32602 13.8702 8.4209L10.6677 11.5564C10.5708 11.6513 10.4137 11.6513 10.3168 11.5564L8.04388 9.33105C8.01965 9.30733 7.98037 9.30733 7.95614 9.33105L5.6833 11.5564C5.58638 11.6513 5.42925 11.6513 5.33234 11.5564L2.12982 8.42087C2.0329 8.32598 2.0329 8.17213 2.12982 8.07724L2.84004 7.38188C2.93695 7.28699 3.09408 7.28699 3.191 7.38188L5.46392 9.60726C5.48815 9.63098 5.52743 9.63098 5.55166 9.60726L7.82447 7.38188C7.92138 7.28699 8.07851 7.28699 8.17543 7.38187L10.4484 9.60726C10.4726 9.63098 10.5119 9.63098 10.5361 9.60726L12.809 7.38192C12.9059 7.28703 13.063 7.28703 13.1599 7.38192Z\" fill=\"#202020\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_22274_4692\">\n<path d=\"M0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8Z\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n`;\nconst walletConnectBrownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<circle cx=\"11\" cy=\"11\" r=\"11\" transform=\"matrix(-1 0 0 1 23 1)\" fill=\"#202020\"/>\n<circle cx=\"11\" cy=\"11\" r=\"11.5\" transform=\"matrix(-1 0 0 1 23 1)\" stroke=\"#C7B994\" stroke-opacity=\"0.7\"/>\n<path d=\"M15.4523 11.0686L16.7472 9.78167C13.8205 6.87297 10.1838 6.87297 7.25708 9.78167L8.55201 11.0686C10.7779 8.85645 13.2279 8.85645 15.4538 11.0686H15.4523Z\" fill=\"#C7B994\"/>\n<path d=\"M15.0199 14.067L12 11.0656L8.98 14.067L5.96004 11.0656L4.66663 12.3511L8.98 16.6393L12 13.638L15.0199 16.6393L19.3333 12.3511L18.0399 11.0656L15.0199 14.067Z\" fill=\"#C7B994\"/>\n</svg>\n`;\n//# sourceMappingURL=walletconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js\n"));

/***/ })

}]);