"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_etherscan_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   etherscanSvg: function() { return /* binding */ etherscanSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst etherscanSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 16 16\">\n  <path\n    fill=\"currentColor\"\n    d=\"M4.25 7a.63.63 0 0 0-.63.63v3.97c0 .28-.2.51-.47.54l-.75.07a.93.93 0 0 1-.9-.47A7.51 7.51 0 0 1 5.54.92a7.5 7.5 0 0 1 9.54 4.62c.12.35.06.72-.16 1-.74.97-1.68 1.78-2.6 2.44V4.44a.64.64 0 0 0-.63-.64h-1.06c-.35 0-.63.3-.63.64v5.5c0 .23-.12.42-.32.5l-.52.23V6.05c0-.36-.3-.64-.64-.64H7.45c-.35 0-.64.3-.64.64v4.97c0 .25-.17.46-.4.52a5.8 5.8 0 0 0-.45.11v-4c0-.36-.3-.65-.64-.65H4.25ZM14.07 12.4A7.49 7.49 0 0 1 3.6 14.08c4.09-.58 9.14-2.5 11.87-6.6v.03a7.56 7.56 0 0 1-1.41 4.91Z\"\n  />\n</svg>`;\n//# sourceMappingURL=etherscan.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2V0aGVyc2Nhbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixxQkFBcUIsd0NBQUc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2V0aGVyc2Nhbi5qcz82ZGNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgZXRoZXJzY2FuU3ZnID0gc3ZnIGA8c3ZnIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAxNiAxNlwiPlxuICA8cGF0aFxuICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgIGQ9XCJNNC4yNSA3YS42My42MyAwIDAgMC0uNjMuNjN2My45N2MwIC4yOC0uMi41MS0uNDcuNTRsLS43NS4wN2EuOTMuOTMgMCAwIDEtLjktLjQ3QTcuNTEgNy41MSAwIDAgMSA1LjU0LjkyYTcuNSA3LjUgMCAwIDEgOS41NCA0LjYyYy4xMi4zNS4wNi43Mi0uMTYgMS0uNzQuOTctMS42OCAxLjc4LTIuNiAyLjQ0VjQuNDRhLjY0LjY0IDAgMCAwLS42My0uNjRoLTEuMDZjLS4zNSAwLS42My4zLS42My42NHY1LjVjMCAuMjMtLjEyLjQyLS4zMi41bC0uNTIuMjNWNi4wNWMwLS4zNi0uMy0uNjQtLjY0LS42NEg3LjQ1Yy0uMzUgMC0uNjQuMy0uNjQuNjR2NC45N2MwIC4yNS0uMTcuNDYtLjQuNTJhNS44IDUuOCAwIDAgMC0uNDUuMTF2LTRjMC0uMzYtLjMtLjY1LS42NC0uNjVINC4yNVpNMTQuMDcgMTIuNEE3LjQ5IDcuNDkgMCAwIDEgMy42IDE0LjA4YzQuMDktLjU4IDkuMTQtMi41IDExLjg3LTYuNnYuMDNhNy41NiA3LjU2IDAgMCAxLTEuNDEgNC45MVpcIlxuICAvPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV0aGVyc2Nhbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js\n"));

/***/ })

}]);