"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-57af44"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js ***!
  \*************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exclamationTriangleSvg: function() { return /* binding */ exclamationTriangleSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst exclamationTriangleSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M15.0162 11.6312L9.55059 2.13937C9.39228 1.86862 9.16584 1.64405 8.8938 1.48798C8.62176 1.33192 8.3136 1.2498 7.99997 1.2498C7.68634 1.2498 7.37817 1.33192 7.10613 1.48798C6.83409 1.64405 6.60765 1.86862 6.44934 2.13937L0.983716 11.6312C0.830104 11.894 0.749146 12.1928 0.749146 12.4972C0.749146 12.8015 0.830104 13.1004 0.983716 13.3631C1.14027 13.6352 1.3664 13.8608 1.63889 14.0166C1.91139 14.1725 2.22044 14.253 2.53434 14.25H13.4656C13.7793 14.2528 14.0881 14.1721 14.3603 14.0163C14.6326 13.8604 14.8585 13.635 15.015 13.3631C15.1688 13.1005 15.2499 12.8017 15.2502 12.4973C15.2504 12.193 15.1696 11.8941 15.0162 11.6312ZM13.7162 12.6125C13.6908 12.6558 13.6541 12.6914 13.6101 12.7157C13.5661 12.7399 13.5164 12.7517 13.4662 12.75H2.53434C2.48415 12.7517 2.43442 12.7399 2.39042 12.7157C2.34641 12.6914 2.30976 12.6558 2.28434 12.6125C2.26278 12.5774 2.25137 12.5371 2.25137 12.4959C2.25137 12.4548 2.26278 12.4144 2.28434 12.3794L7.74997 2.88749C7.77703 2.84583 7.81408 2.8116 7.85774 2.7879C7.9014 2.7642 7.95029 2.75178 7.99997 2.75178C8.04964 2.75178 8.09854 2.7642 8.1422 2.7879C8.18586 2.8116 8.2229 2.84583 8.24997 2.88749L13.715 12.3794C13.7367 12.4143 13.7483 12.4546 13.7486 12.4958C13.7488 12.5369 13.7376 12.5773 13.7162 12.6125ZM7.24997 8.49999V6.49999C7.24997 6.30108 7.32898 6.11031 7.46964 5.96966C7.61029 5.82901 7.80105 5.74999 7.99997 5.74999C8.19888 5.74999 8.38964 5.82901 8.5303 5.96966C8.67095 6.11031 8.74997 6.30108 8.74997 6.49999V8.49999C8.74997 8.6989 8.67095 8.88967 8.5303 9.03032C8.38964 9.17097 8.19888 9.24999 7.99997 9.24999C7.80105 9.24999 7.61029 9.17097 7.46964 9.03032C7.32898 8.88967 7.24997 8.6989 7.24997 8.49999ZM8.99997 11C8.99997 11.1978 8.94132 11.3911 8.83144 11.5556C8.72155 11.72 8.56538 11.8482 8.38265 11.9239C8.19992 11.9996 7.99886 12.0194 7.80488 11.9808C7.6109 11.9422 7.43271 11.847 7.29286 11.7071C7.15301 11.5672 7.05777 11.3891 7.01918 11.1951C6.9806 11.0011 7.0004 10.8 7.07609 10.6173C7.15177 10.4346 7.27995 10.2784 7.4444 10.1685C7.60885 10.0586 7.80219 9.99999 7.99997 9.99999C8.26518 9.99999 8.51954 10.1053 8.70707 10.2929C8.89461 10.4804 8.99997 10.7348 8.99997 11Z\" fill=\"currentColor\"/>\n</svg>\n`;\n//# sourceMappingURL=exclamation-triangle.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js\n"));

/***/ })

}]);