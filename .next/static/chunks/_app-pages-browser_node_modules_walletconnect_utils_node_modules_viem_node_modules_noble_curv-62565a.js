"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_utils_node_modules_viem_node_modules_noble_curv-62565a"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: function() { return /* binding */ Chi; },\n/* harmony export */   HashMD: function() { return /* binding */ HashMD; },\n/* harmony export */   Maj: function() { return /* binding */ Maj; },\n/* harmony export */   SHA224_IV: function() { return /* binding */ SHA224_IV; },\n/* harmony export */   SHA256_IV: function() { return /* binding */ SHA256_IV; },\n/* harmony export */   SHA384_IV: function() { return /* binding */ SHA384_IV; },\n/* harmony export */   SHA512_IV: function() { return /* binding */ SHA512_IV; },\n/* harmony export */   setBigUint64: function() { return /* binding */ setBigUint64; }\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(data);\n        const { view, buffer, blockLen } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(this.buffer.subarray(pos));\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nconst SHA256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nconst SHA224_IV = /* @__PURE__ */ Uint32Array.from([\n    0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nconst SHA384_IV = /* @__PURE__ */ Uint32Array.from([\n    0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n    0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nconst SHA512_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n    0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: function() { return /* binding */ HMAC; },\n/* harmony export */   hmac: function() { return /* binding */ hmac; }\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(pad);\n    }\n    update(buf) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha2.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha2.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA224: function() { return /* binding */ SHA224; },\n/* harmony export */   SHA256: function() { return /* binding */ SHA256; },\n/* harmony export */   SHA384: function() { return /* binding */ SHA384; },\n/* harmony export */   SHA512: function() { return /* binding */ SHA512; },\n/* harmony export */   SHA512_224: function() { return /* binding */ SHA512_224; },\n/* harmony export */   SHA512_256: function() { return /* binding */ SHA512_256; },\n/* harmony export */   sha224: function() { return /* binding */ sha224; },\n/* harmony export */   sha256: function() { return /* binding */ sha256; },\n/* harmony export */   sha384: function() { return /* binding */ sha384; },\n/* harmony export */   sha512: function() { return /* binding */ sha512; },\n/* harmony export */   sha512_224: function() { return /* binding */ sha512_224; },\n/* harmony export */   sha512_256: function() { return /* binding */ sha512_256; }\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_u64.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\n\n\n\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor(outputLen = 32) {\n        super(64, outputLen, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[0] | 0;\n        this.B = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[1] | 0;\n        this.C = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[2] | 0;\n        this.D = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[3] | 0;\n        this.E = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[4] | 0;\n        this.F = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[5] | 0;\n        this.G = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[6] | 0;\n        this.H = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA256_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n    }\n}\nclass SHA224 extends SHA256 {\n    constructor() {\n        super(28);\n        this.A = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[0] | 0;\n        this.B = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[1] | 0;\n        this.C = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[2] | 0;\n        this.D = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[3] | 0;\n        this.E = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[4] | 0;\n        this.F = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[5] | 0;\n        this.G = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[6] | 0;\n        this.H = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[7] | 0;\n    }\n}\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => _u64_js__WEBPACK_IMPORTED_MODULE_2__.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nclass SHA512 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor(outputLen = 64) {\n        super(128, outputLen, 16, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[0] | 0;\n        this.Al = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[1] | 0;\n        this.Bh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[2] | 0;\n        this.Bl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[3] | 0;\n        this.Ch = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[4] | 0;\n        this.Cl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[5] | 0;\n        this.Dh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[6] | 0;\n        this.Dl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[7] | 0;\n        this.Eh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[8] | 0;\n        this.El = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[9] | 0;\n        this.Fh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[10] | 0;\n        this.Fl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[11] | 0;\n        this.Gh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[12] | 0;\n        this.Gl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[13] | 0;\n        this.Hh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[14] | 0;\n        this.Hl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[15] | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W15h, W15l, 1) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W15h, W15l, 8) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSH(W15h, W15l, 7);\n            const s0l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W15h, W15l, 1) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W15h, W15l, 8) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W2h, W2l, 19) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(W2h, W2l, 61) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSH(W2h, W2l, 6);\n            const s1l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W2h, W2l, 19) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(W2h, W2l, 61) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Eh, El, 14) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Eh, El, 18) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Eh, El, 41);\n            const sigma1l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Eh, El, 14) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Eh, El, 18) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Ah, Al, 28) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Ah, Al, 34) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Ah, Al, 39);\n            const sigma0l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Ah, Al, 28) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Ah, Al, 34) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add3L(T1l, sigma0l, MAJl);\n            Ah = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA512_W_H, SHA512_W_L);\n    }\n    destroy() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nclass SHA384 extends SHA512 {\n    constructor() {\n        super(48);\n        this.Ah = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[0] | 0;\n        this.Al = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[1] | 0;\n        this.Bh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[2] | 0;\n        this.Bl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[3] | 0;\n        this.Ch = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[4] | 0;\n        this.Cl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[5] | 0;\n        this.Dh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[6] | 0;\n        this.Dl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[7] | 0;\n        this.Eh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[8] | 0;\n        this.El = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[9] | 0;\n        this.Fh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[10] | 0;\n        this.Fl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[11] | 0;\n        this.Gh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[12] | 0;\n        this.Gl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[13] | 0;\n        this.Hh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[14] | 0;\n        this.Hl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[15] | 0;\n    }\n}\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n    0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n    0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n    0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super(28);\n        this.Ah = T224_IV[0] | 0;\n        this.Al = T224_IV[1] | 0;\n        this.Bh = T224_IV[2] | 0;\n        this.Bl = T224_IV[3] | 0;\n        this.Ch = T224_IV[4] | 0;\n        this.Cl = T224_IV[5] | 0;\n        this.Dh = T224_IV[6] | 0;\n        this.Dl = T224_IV[7] | 0;\n        this.Eh = T224_IV[8] | 0;\n        this.El = T224_IV[9] | 0;\n        this.Fh = T224_IV[10] | 0;\n        this.Fl = T224_IV[11] | 0;\n        this.Gh = T224_IV[12] | 0;\n        this.Gl = T224_IV[13] | 0;\n        this.Hh = T224_IV[14] | 0;\n        this.Hl = T224_IV[15] | 0;\n    }\n}\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super(32);\n        this.Ah = T256_IV[0] | 0;\n        this.Al = T256_IV[1] | 0;\n        this.Bh = T256_IV[2] | 0;\n        this.Bl = T256_IV[3] | 0;\n        this.Ch = T256_IV[4] | 0;\n        this.Cl = T256_IV[5] | 0;\n        this.Dh = T256_IV[6] | 0;\n        this.Dl = T256_IV[7] | 0;\n        this.Eh = T256_IV[8] | 0;\n        this.El = T256_IV[9] | 0;\n        this.Fh = T256_IV[10] | 0;\n        this.Fl = T256_IV[11] | 0;\n        this.Gh = T256_IV[12] | 0;\n        this.Gl = T256_IV[13] | 0;\n        this.Hh = T256_IV[14] | 0;\n        this.Hl = T256_IV[15] | 0;\n    }\n}\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA224());\n/** SHA2-512 hash function from RFC 4634. */\nconst sha512 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nconst sha384 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA384());\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nconst sha512_256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nconst sha512_224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512_224());\n//# sourceMappingURL=sha2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \*************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: function() { return /* binding */ createCurve; },\n/* harmony export */   getHash: function() { return /* binding */ getHash; }\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n/** connects noble-curves to noble-hashes */\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC91dGlscy9ub2RlX21vZHVsZXMvdmllbS9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vX3Nob3J0d191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMEM7QUFDcUI7QUFDUDtBQUN4RDtBQUNPO0FBQ1A7QUFDQTtBQUNBLGdDQUFnQyx3REFBSSxZQUFZLGdFQUFXO0FBQzNELG1CQUFtQjtBQUNuQjtBQUNBO0FBQ087QUFDUCw2QkFBNkIscUVBQVcsR0FBRywrQkFBK0I7QUFDMUUsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0B3YWxsZXRjb25uZWN0L3V0aWxzL25vZGVfbW9kdWxlcy92aWVtL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9fc2hvcnR3X3V0aWxzLmpzPzU5MTciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVdGlsaXRpZXMgZm9yIHNob3J0IHdlaWVyc3RyYXNzIGN1cnZlcywgY29tYmluZWQgd2l0aCBub2JsZS1oYXNoZXMuXG4gKiBAbW9kdWxlXG4gKi9cbi8qISBub2JsZS1jdXJ2ZXMgLSBNSVQgTGljZW5zZSAoYykgMjAyMiBQYXVsIE1pbGxlciAocGF1bG1pbGxyLmNvbSkgKi9cbmltcG9ydCB7IGhtYWMgfSBmcm9tICdAbm9ibGUvaGFzaGVzL2htYWMnO1xuaW1wb3J0IHsgY29uY2F0Qnl0ZXMsIHJhbmRvbUJ5dGVzIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy91dGlscyc7XG5pbXBvcnQgeyB3ZWllcnN0cmFzcyB9IGZyb20gXCIuL2Fic3RyYWN0L3dlaWVyc3RyYXNzLmpzXCI7XG4vKiogY29ubmVjdHMgbm9ibGUtY3VydmVzIHRvIG5vYmxlLWhhc2hlcyAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEhhc2goaGFzaCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGhhc2gsXG4gICAgICAgIGhtYWM6IChrZXksIC4uLm1zZ3MpID0+IGhtYWMoaGFzaCwga2V5LCBjb25jYXRCeXRlcyguLi5tc2dzKSksXG4gICAgICAgIHJhbmRvbUJ5dGVzLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ3VydmUoY3VydmVEZWYsIGRlZkhhc2gpIHtcbiAgICBjb25zdCBjcmVhdGUgPSAoaGFzaCkgPT4gd2VpZXJzdHJhc3MoeyAuLi5jdXJ2ZURlZiwgLi4uZ2V0SGFzaChoYXNoKSB9KTtcbiAgICByZXR1cm4geyAuLi5jcmVhdGUoZGVmSGFzaCksIGNyZWF0ZSB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9X3Nob3J0d191dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js ***!
  \**************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pippenger: function() { return /* binding */ pippenger; },\n/* harmony export */   precomputeMSMUnsafe: function() { return /* binding */ precomputeMSMUnsafe; },\n/* harmony export */   validateBasic: function() { return /* binding */ validateBasic; },\n/* harmony export */   wNAF: function() { return /* binding */ wNAF; }\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, scalarBits) {\n    validateW(W, scalarBits);\n    const windows = Math.ceil(scalarBits / W) + 1; // W=8 33. Not 32, because we skip zero\n    const windowSize = 2 ** (W - 1); // W=8 128. Not 256, because we skip zero\n    const maxNumber = 2 ** W; // W=8 256\n    const mask = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(W); // W=8 255 == mask 0b11111111\n    const shiftBy = BigInt(W); // W=8 8\n    return { windows, windowSize, mask, maxNumber, shiftBy };\n}\nfunction calcOffsets(n, window, wOpts) {\n    const { windowSize, mask, maxNumber, shiftBy } = wOpts;\n    let wbits = Number(n & mask); // extract W bits.\n    let nextN = n >> shiftBy; // shift number by W bits.\n    // What actually happens here:\n    // const highestBit = Number(mask ^ (mask >> 1n));\n    // let wbits2 = wbits - 1; // skip zero\n    // if (wbits2 & highestBit) { wbits2 ^= Number(mask); // (~);\n    // split if bits > max: +224 => 256-32\n    if (wbits > windowSize) {\n        // we skip zero, which means instead of `>= size-1`, we do `> size`\n        wbits -= maxNumber; // -32, can be maxNumber - wbits, but then we need to set isNeg here.\n        nextN += _1n; // +256 (carry)\n    }\n    const offsetStart = window * windowSize;\n    const offset = offsetStart + Math.abs(wbits) - 1; // -1 because we skip zero\n    const isZero = wbits === 0; // is current window slice a 0?\n    const isNeg = wbits < 0; // is current window slice negative?\n    const isNegF = window % 2 !== 0; // fake random statement for noise\n    const offsetF = offsetStart; // fake offset for noise\n    return { nextN, offset, isZero, isNeg, isNegF, offsetF };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes.\n// Allows to make points frozen / immutable.\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap();\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nfunction wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // i=1, bc we skip 0\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // Smaller version:\n            // https://github.com/paulmillr/noble-secp256k1/blob/47cb1669b6e506ad66b35fe7d76132ae97465da2/index.ts#L502-L541\n            // TODO: check the scalar is less than group order?\n            // wNAF behavior is undefined otherwise. But have to carefully remove\n            // other checks before wNAF. ORDER == bits here.\n            // Accumulators\n            let p = c.ZERO;\n            let f = c.BASE;\n            // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n            // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n            // there is negate now: it is possible that negated element from low value\n            // would be the same as high element, which will create carry into next window.\n            // It's not obvious how this can fail, but still worth investigating later.\n            const wo = calcWOpts(W, bits);\n            for (let window = 0; window < wo.windows; window++) {\n                // (n === _0n) is handled and not early-exited. isEven and offsetF are used for noise\n                const { nextN, offset, isZero, isNeg, isNegF, offsetF } = calcOffsets(n, window, wo);\n                n = nextN;\n                if (isZero) {\n                    // bits are 0: add garbage to fake point\n                    // Important part for const-time getPublicKey: add random \"noise\" point to f.\n                    f = f.add(constTimeNegate(isNegF, precomputes[offsetF]));\n                }\n                else {\n                    // bits are 1: add to result point\n                    p = p.add(constTimeNegate(isNeg, precomputes[offset]));\n                }\n            }\n            // Return both real and fake points: JIT won't eliminate f.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const wo = calcWOpts(W, bits);\n            for (let window = 0; window < wo.windows; window++) {\n                if (n === _0n)\n                    break; // Early-exit, skip 0 value\n                const { nextN, offset, isZero, isNeg } = calcOffsets(n, window, wo);\n                n = nextN;\n                if (isZero) {\n                    // Window bits are 0: skip processing.\n                    // Move to next window.\n                    continue;\n                }\n                else {\n                    const item = precomputes[offset];\n                    acc = acc.add(isNeg ? item.negate() : item); // Re-using acc allows to save adds in MSM\n                }\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster than precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nfunction pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    const plength = points.length;\n    const slength = scalars.length;\n    if (plength !== slength)\n        throw new Error('arrays of points and scalars must have equal length');\n    // if (plength === 0) throw new Error('array must be of length >= 2');\n    const zero = c.ZERO;\n    const wbits = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitLen)(BigInt(plength));\n    let windowSize = 1; // bits\n    if (wbits > 12)\n        windowSize = wbits - 3;\n    else if (wbits > 4)\n        windowSize = wbits - 2;\n    else if (wbits > 0)\n        windowSize = 2;\n    const MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(windowSize);\n    const buckets = new Array(Number(MASK) + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < slength; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & MASK);\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nfunction precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(windowSize);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \**********************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: function() { return /* binding */ createHasher; },\n/* harmony export */   expand_message_xmd: function() { return /* binding */ expand_message_xmd; },\n/* harmony export */   expand_message_xof: function() { return /* binding */ expand_message_xof; },\n/* harmony export */   hash_to_field: function() { return /* binding */ hash_to_field; },\n/* harmony export */   isogenyMap: function() { return /* binding */ isogenyMap; }\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    anum(value);\n    anum(length);\n    if (value < 0 || value >= 1 << (8 * length))\n        throw new Error('invalid I2OSP input: ' + value);\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction anum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (lenInBytes > 65535 || ell > 255)\n        throw new Error('expand_message_xmd: invalid lenInBytes');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    anum(count);\n    const DST = typeof _DST === 'string' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(_DST) : _DST;\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const coeff = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xn, xd, yn, yd] = coeff.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        // 6.6.3\n        // Exceptional cases of iso_map are inputs that cause the denominator of\n        // either rational function to evaluate to zero; such cases MUST return\n        // the identity point on E.\n        const [xd_inv, yd_inv] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.FpInvertBatch)(field, [xd, yd], true);\n        x = field.mul(xn, xd_inv); // xNum / xDen\n        y = field.mul(y, field.mul(yn, yd_inv)); // y * (yNum / yDev)\n        return { x, y };\n    };\n}\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nfunction createHasher(Point, mapToCurve, defaults) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    function map(num) {\n        return Point.fromAffine(mapToCurve(num));\n    }\n    function clear(initial) {\n        const P = initial.clearCofactor();\n        if (P.equals(Point.ZERO))\n            return Point.ZERO; // zero will throw in assert\n        P.assertValidity();\n        return P;\n    }\n    return {\n        defaults,\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...defaults, DST: defaults.DST, ...options });\n            const u0 = map(u[0]);\n            const u1 = map(u[1]);\n            return clear(u0.add(u1));\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...defaults, DST: defaults.encodeDST, ...options });\n            return clear(map(u[0]));\n        },\n        // Same as encodeToCurve, but without hash\n        mapToCurve(scalars) {\n            if (!Array.isArray(scalars))\n                throw new Error('expected array of bigints');\n            for (const i of scalars)\n                if (typeof i !== 'bigint')\n                    throw new Error('expected array of bigints');\n            return clear(map(scalars));\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: function() { return /* binding */ Field; },\n/* harmony export */   FpDiv: function() { return /* binding */ FpDiv; },\n/* harmony export */   FpInvertBatch: function() { return /* binding */ FpInvertBatch; },\n/* harmony export */   FpIsSquare: function() { return /* binding */ FpIsSquare; },\n/* harmony export */   FpLegendre: function() { return /* binding */ FpLegendre; },\n/* harmony export */   FpPow: function() { return /* binding */ FpPow; },\n/* harmony export */   FpSqrt: function() { return /* binding */ FpSqrt; },\n/* harmony export */   FpSqrtEven: function() { return /* binding */ FpSqrtEven; },\n/* harmony export */   FpSqrtOdd: function() { return /* binding */ FpSqrtOdd; },\n/* harmony export */   getFieldBytesLength: function() { return /* binding */ getFieldBytesLength; },\n/* harmony export */   getMinHashLength: function() { return /* binding */ getMinHashLength; },\n/* harmony export */   hashToPrivateScalar: function() { return /* binding */ hashToPrivateScalar; },\n/* harmony export */   invert: function() { return /* binding */ invert; },\n/* harmony export */   isNegativeLE: function() { return /* binding */ isNegativeLE; },\n/* harmony export */   mapHashToField: function() { return /* binding */ mapHashToField; },\n/* harmony export */   mod: function() { return /* binding */ mod; },\n/* harmony export */   nLength: function() { return /* binding */ nLength; },\n/* harmony export */   pow: function() { return /* binding */ pow; },\n/* harmony export */   pow2: function() { return /* binding */ pow2; },\n/* harmony export */   tonelliShanks: function() { return /* binding */ tonelliShanks; },\n/* harmony export */   validateField: function() { return /* binding */ validateField; }\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * TODO: remove.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nfunction pow(num, power, modulo) {\n    return FpPow(Field(modulo), num, power);\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nfunction invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n// Not all roots are possible! Example which will throw:\n// const NUM =\n// n = 72057594037927816n;\n// Fp = Field(BigInt('0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaab'));\nfunction sqrt3mod4(Fp, n) {\n    const p1div4 = (Fp.ORDER + _1n) / _4n;\n    const root = Fp.pow(n, p1div4);\n    // Throw if root^2 != n\n    if (!Fp.eql(Fp.sqr(root), n))\n        throw new Error('Cannot find square root');\n    return root;\n}\nfunction sqrt5mod8(Fp, n) {\n    const p5div8 = (Fp.ORDER - _5n) / _8n;\n    const n2 = Fp.mul(n, _2n);\n    const v = Fp.pow(n2, p5div8);\n    const nv = Fp.mul(n, v);\n    const i = Fp.mul(Fp.mul(nv, _2n), v);\n    const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n    if (!Fp.eql(Fp.sqr(root), n))\n        throw new Error('Cannot find square root');\n    return root;\n}\n// TODO: Commented-out for now. Provide test vectors.\n// Tonelli is too slow for extension fields Fp2.\n// That means we can't use sqrt (c1, c2...) even for initialization constants.\n// if (P % _16n === _9n) return sqrt9mod16;\n// // prettier-ignore\n// function sqrt9mod16<T>(Fp: IField<T>, n: T, p7div16?: bigint) {\n//   if (p7div16 === undefined) p7div16 = (Fp.ORDER + BigInt(7)) / _16n;\n//   const c1 = Fp.sqrt(Fp.neg(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n//   const c2 = Fp.sqrt(c1);             //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n//   const c3 = Fp.sqrt(Fp.neg(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n//   const c4 = p7div16;                 //  4. c4 = (q + 7) / 16        # Integer arithmetic\n//   let tv1 = Fp.pow(n, c4);            //  1. tv1 = x^c4\n//   let tv2 = Fp.mul(c1, tv1);          //  2. tv2 = c1 * tv1\n//   const tv3 = Fp.mul(c2, tv1);        //  3. tv3 = c2 * tv1\n//   let tv4 = Fp.mul(c3, tv1);          //  4. tv4 = c3 * tv1\n//   const e1 = Fp.eql(Fp.sqr(tv2), n);  //  5.  e1 = (tv2^2) == x\n//   const e2 = Fp.eql(Fp.sqr(tv3), n);  //  6.  e2 = (tv3^2) == x\n//   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n//   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n//   const e3 = Fp.eql(Fp.sqr(tv2), n);  //  9.  e3 = (tv2^2) == x\n//   return Fp.cmov(tv1, tv2, e3); // 10.  z = CMOV(tv1, tv2, e3) # Select the sqrt from tv1 and tv2\n// }\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Initialization (precomputation).\n    if (P < BigInt(3))\n        throw new Error('sqrt is not defined for small field');\n    // Factor P - 1 = Q * 2^S, where Q is odd\n    let Q = P - _1n;\n    let S = 0;\n    while (Q % _2n === _0n) {\n        Q /= _2n;\n        S++;\n    }\n    // Find the first quadratic non-residue Z >= 2\n    let Z = _2n;\n    const _Fp = Field(P);\n    while (FpLegendre(_Fp, Z) === 1) {\n        // Basic primality test for P. After x iterations, chance of\n        // not finding quadratic non-residue is 2^x, so 2^1000.\n        if (Z++ > 1000)\n            throw new Error('Cannot find square root: probably non-prime P');\n    }\n    // Fast-path; usually done before Z, but we do \"primality test\".\n    if (S === 1)\n        return sqrt3mod4;\n    // Slow-path\n    // TODO: test on Fp2 and others\n    let cc = _Fp.pow(Z, Q); // c = z^Q\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        if (Fp.is0(n))\n            return n;\n        // Check if n is a quadratic residue using Legendre symbol\n        if (FpLegendre(Fp, n) !== 1)\n            throw new Error('Cannot find square root');\n        // Initialize variables for the main loop\n        let M = S;\n        let c = Fp.mul(Fp.ONE, cc); // c = z^Q, move cc from field _Fp into field Fp\n        let t = Fp.pow(n, Q); // t = n^Q, first guess at the fudge factor\n        let R = Fp.pow(n, Q1div2); // R = n^((Q+1)/2), first guess at the square root\n        // Main loop\n        // while t != 1\n        while (!Fp.eql(t, Fp.ONE)) {\n            if (Fp.is0(t))\n                return Fp.ZERO; // if t=0 return R=0\n            let i = 1;\n            // Find the smallest i >= 1 such that t^(2^i) ≡ 1 (mod P)\n            let t_tmp = Fp.sqr(t); // t^(2^1)\n            while (!Fp.eql(t_tmp, Fp.ONE)) {\n                i++;\n                t_tmp = Fp.sqr(t_tmp); // t^(2^2)...\n                if (i === M)\n                    throw new Error('Cannot find square root');\n            }\n            // Calculate the exponent for b: 2^(M - i - 1)\n            const exponent = _1n << BigInt(M - i - 1); // bigint is important\n            const b = Fp.pow(c, exponent); // b = 2^(M - i - 1)\n            // Update variables\n            M = i;\n            c = Fp.sqr(b); // c = b^2\n            t = Fp.mul(t, c); // t = (t * b^2)\n            R = Fp.mul(R, b); // R = R*b\n        }\n        return R;\n    };\n}\n/**\n * Square root for a finite field. Will try optimized versions first:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nfunction FpSqrt(P) {\n    // P ≡ 3 (mod 4) => √n = n^((P+1)/4)\n    if (P % _4n === _3n)\n        return sqrt3mod4;\n    // P ≡ 5 (mod 8) => Atkin algorithm, page 10 of https://eprint.iacr.org/2012/685.pdf\n    if (P % _8n === _5n)\n        return sqrt5mod8;\n    // P ≡ 9 (mod 16) not implemented, see above\n    // Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(Fp, num, power) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return Fp.ONE;\n    if (power === _1n)\n        return num;\n    let p = Fp.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = Fp.mul(p, d);\n        d = Fp.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * Exception-free. Will return `undefined` for 0 elements.\n * @param passZero map 0 to 0 (instead of undefined)\n */\nfunction FpInvertBatch(Fp, nums, passZero = false) {\n    const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : undefined);\n    // Walk from first to last, multiply them by each other MOD p\n    const multipliedAcc = nums.reduce((acc, num, i) => {\n        if (Fp.is0(num))\n            return acc;\n        inverted[i] = acc;\n        return Fp.mul(acc, num);\n    }, Fp.ONE);\n    // Invert last element\n    const invertedAcc = Fp.inv(multipliedAcc);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (Fp.is0(num))\n            return acc;\n        inverted[i] = Fp.mul(acc, inverted[i]);\n        return Fp.mul(acc, num);\n    }, invertedAcc);\n    return inverted;\n}\n// TODO: remove\nfunction FpDiv(Fp, lhs, rhs) {\n    return Fp.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));\n}\n/**\n * Legendre symbol.\n * Legendre constant is used to calculate Legendre symbol (a | p)\n * which denotes the value of a^((p-1)/2) (mod p).\n *\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nfunction FpLegendre(Fp, n) {\n    // We can use 3rd argument as optional cache of this value\n    // but seems unneeded for now. The operation is very fast.\n    const p1mod2 = (Fp.ORDER - _1n) / _2n;\n    const powered = Fp.pow(n, p1mod2);\n    const yes = Fp.eql(powered, Fp.ONE);\n    const zero = Fp.eql(powered, Fp.ZERO);\n    const no = Fp.eql(powered, Fp.neg(Fp.ONE));\n    if (!yes && !zero && !no)\n        throw new Error('invalid Legendre symbol result');\n    return yes ? 1 : zero ? 0 : -1;\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(Fp, n) {\n    const l = FpLegendre(Fp, n);\n    return l === 1;\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    if (nBitLength !== undefined)\n        (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.anumber)(nBitLength);\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n        // TODO: we don't need it here, move out to separate fn\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // We can't move this out because Fp6, Fp12 implement it\n        // and it's unclear what to return in there.\n        cmov: (a, b, c) => (c ? b : a),\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js ***!
  \**************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aInRange: function() { return /* binding */ aInRange; },\n/* harmony export */   abool: function() { return /* binding */ abool; },\n/* harmony export */   abytes: function() { return /* binding */ abytes; },\n/* harmony export */   bitGet: function() { return /* binding */ bitGet; },\n/* harmony export */   bitLen: function() { return /* binding */ bitLen; },\n/* harmony export */   bitMask: function() { return /* binding */ bitMask; },\n/* harmony export */   bitSet: function() { return /* binding */ bitSet; },\n/* harmony export */   bytesToHex: function() { return /* binding */ bytesToHex; },\n/* harmony export */   bytesToNumberBE: function() { return /* binding */ bytesToNumberBE; },\n/* harmony export */   bytesToNumberLE: function() { return /* binding */ bytesToNumberLE; },\n/* harmony export */   concatBytes: function() { return /* binding */ concatBytes; },\n/* harmony export */   createHmacDrbg: function() { return /* binding */ createHmacDrbg; },\n/* harmony export */   ensureBytes: function() { return /* binding */ ensureBytes; },\n/* harmony export */   equalBytes: function() { return /* binding */ equalBytes; },\n/* harmony export */   hexToBytes: function() { return /* binding */ hexToBytes; },\n/* harmony export */   hexToNumber: function() { return /* binding */ hexToNumber; },\n/* harmony export */   inRange: function() { return /* binding */ inRange; },\n/* harmony export */   isBytes: function() { return /* binding */ isBytes; },\n/* harmony export */   memoized: function() { return /* binding */ memoized; },\n/* harmony export */   notImplemented: function() { return /* binding */ notImplemented; },\n/* harmony export */   numberToBytesBE: function() { return /* binding */ numberToBytesBE; },\n/* harmony export */   numberToBytesLE: function() { return /* binding */ numberToBytesLE; },\n/* harmony export */   numberToHexUnpadded: function() { return /* binding */ numberToHexUnpadded; },\n/* harmony export */   numberToVarBytesBE: function() { return /* binding */ numberToVarBytesBE; },\n/* harmony export */   utf8ToBytes: function() { return /* binding */ utf8ToBytes; },\n/* harmony export */   validateObject: function() { return /* binding */ validateObject; }\n/* harmony export */ });\n/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nfunction abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Used in weierstrass, der\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function';\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nfunction inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nfunction aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n * TODO: merge with nLength in modular\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nfunction bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_1n << BigInt(n)) - _1n;\n// DRBG\nconst u8n = (len) => new Uint8Array(len); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n(0)) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nconst notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nfunction memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: function() { return /* binding */ DER; },\n/* harmony export */   DERErr: function() { return /* binding */ DERErr; },\n/* harmony export */   SWUFpSqrtRatio: function() { return /* binding */ SWUFpSqrtRatio; },\n/* harmony export */   mapToCurveSimpleSWU: function() { return /* binding */ mapToCurveSimpleSWU; },\n/* harmony export */   weierstrass: function() { return /* binding */ weierstrass; },\n/* harmony export */   weierstrassPoints: function() { return /* binding */ weierstrassPoints; }\n/* harmony export */ });\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curve.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js\");\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Short Weierstrass curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Parameters\n *\n * To initialize a weierstrass curve, one needs to pass following params:\n *\n * * a: formula param\n * * b: formula param\n * * Fp: finite field of prime characteristic P; may be complex (Fp2). Arithmetics is done in field\n * * n: order of prime subgroup a.k.a total amount of valid curve points\n * * Gx: Base point (x, y) aka generator point. Gx = x coordinate\n * * Gy: ...y coordinate\n * * h: cofactor, usually 1. h*n = curve group order (n is only subgroup order)\n * * lowS: whether to enable (default) or disable \"low-s\" non-malleable signatures\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// prettier-ignore\n\n// prettier-ignore\n\n// prettier-ignore\n\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowInfinityPoint: 'boolean',\n        allowedPrivateKeyLengths: 'array',\n        clearCofactor: 'function',\n        fromBytes: 'function',\n        isTorsionFree: 'function',\n        toBytes: 'function',\n        wrapPrivateKey: 'boolean',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endo: CURVE.a must be 0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endo: expected \"beta\": bigint and \"splitScalar\": function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nclass DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)((len.length / 2) | 128) : '';\n            const t = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('signature', hex);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\nfunction numToSizedHex(num, size) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, size));\n}\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.Field)(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula. Takes x, returns y².\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x² * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x³ + a * x + b\n    }\n    function isValidXY(x, y) {\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        return Fp.eql(left, right);\n    }\n    // Validate whether the passed curve params are valid.\n    // Test 1: equation y² = x³ + ax + b should work for generator point.\n    if (!isValidXY(CURVE.Gx, CURVE.Gy))\n        throw new Error('bad curve params: generator point');\n    // Test 2: discriminant Δ part should be non-zero: 4a³ + 27b² != 0.\n    // Guarantees curve is genus-1, smooth (non-singular).\n    const _4a3 = Fp.mul(Fp.pow(CURVE.a, _3n), _4n);\n    const _27b2 = Fp.mul(Fp.sqr(CURVE.b), BigInt(27));\n    if (Fp.is0(Fp.add(_4a3, _27b2)))\n        throw new Error('bad curve params: a or b');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange)(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(key))\n                key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(num, N); // disabled by default, enabled for BLS\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function aprjpoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n    const toAffineMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        if (!isValidXY(x, y))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py) || Fp.is0(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.FpInvertBatch)(Fp, points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.pippenger)(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            aprjpoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            aprjpoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            /** See docs for {@link EndomorphismOpts} */\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            /** See docs for {@link EndomorphismOpts} */\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(this.toRawBytes(isCompressed));\n        }\n    }\n    // base / generator point\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    // zero / infinity / identity point\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO); // 0, 1, 0\n    const { endo, nBitLength } = CURVE;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.wNAF)(Point, endo ? Math.ceil(nBitLength / 2) : nBitLength);\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER, nByteLength, nBitLength } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.invert)(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(tail);\n                if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange)(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('r', r, _1n, CURVE_ORDER); // r in [1..N]\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('s', s, _1n, CURVE_ORDER); // s in [1..N]\n            this.r = r;\n            this.s = s;\n            if (recovery != null)\n                this.recovery = recovery;\n            Object.freeze(this);\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        /**\n         * @todo remove\n         * @deprecated\n         */\n        assertValidity() { }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToSizedHex(radj, Fp.BYTES));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig(this);\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(this.toCompactHex());\n        }\n        toCompactHex() {\n            const l = nByteLength;\n            return numToSizedHex(this.r, l) + numToSizedHex(this.s, l);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength)(CURVE.n);\n            return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField)(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        if (typeof item === 'bigint')\n            return false;\n        if (item instanceof Point)\n            return true;\n        const arr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('key', item);\n        const len = arr.length;\n        const fpl = Fp.BYTES;\n        const compLen = fpl + 1; // e.g. 33 for 32\n        const uncompLen = 2 * fpl + 1; // e.g. 65 for 32\n        if (CURVE.allowedPrivateKeyLengths || nByteLength === compLen) {\n            return undefined;\n        }\n        else {\n            return len === compLen || len === uncompLen;\n        }\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA) === true)\n            throw new Error('first arg must be private key');\n        if (isProbPub(publicB) === false)\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\", for protection against DoS\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('num < 2^' + nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createHmacDrbg)(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField)(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        const tv4_inv = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.FpInvertBatch)(Fp, [tv4], true)[0];\n        x = Fp.mul(x, tv4_inv); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/secp256k1.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/secp256k1.js ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: function() { return /* binding */ encodeToCurve; },\n/* harmony export */   hashToCurve: function() { return /* binding */ hashToCurve; },\n/* harmony export */   schnorr: function() { return /* binding */ schnorr; },\n/* harmony export */   secp256k1: function() { return /* binding */ secp256k1; },\n/* harmony export */   secp256k1_hasher: function() { return /* binding */ secp256k1_hasher; }\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha2 */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha2.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 curve, ECDSA and ECDH methods.\n *\n * Field: `2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n`\n *\n * @example\n * ```js\n * import { secp256k1 } from '@noble/curves/secp256k1';\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n * ```\n */\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: _0n,\n    b: BigInt(7),\n    Fp: Fpk1,\n    n: secp256k1N,\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1),\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = /* @__PURE__ */ (() => secp256k1.ProjectivePoint)();\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.aInRange)('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * ```js\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n * ```\n */\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\n/** Hashing / encoding to secp256k1 points / field. RFC 9380 methods. */\nconst secp256k1_hasher = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\nconst hashToCurve = /* @__PURE__ */ (() => secp256k1_hasher.hashToCurve)();\nconst encodeToCurve = /* @__PURE__ */ (() => secp256k1_hasher.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/viem/node_modules/@noble/curves/esm/secp256k1.js\n"));

/***/ })

}]);