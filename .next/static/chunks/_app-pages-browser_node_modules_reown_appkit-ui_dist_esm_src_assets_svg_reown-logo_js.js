"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reownSvg: function() { return /* binding */ reownSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst reownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"60\" height=\"16\" viewBox=\"0 0 60 16\" fill=\"none\"\">\n  <path d=\"M9.3335 4.66667C9.3335 2.08934 11.4229 0 14.0002 0H20.6669C23.2442 0 25.3335 2.08934 25.3335 4.66667V11.3333C25.3335 13.9106 23.2442 16 20.6669 16H14.0002C11.4229 16 9.3335 13.9106 9.3335 11.3333V4.66667Z\" fill=\"#363636\"/>\n  <path d=\"M15.6055 11.0003L17.9448 4.66699H18.6316L16.2923 11.0003H15.6055Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M0 4.33333C0 1.9401 1.9401 0 4.33333 0C6.72657 0 8.66669 1.9401 8.66669 4.33333V11.6667C8.66669 14.0599 6.72657 16 4.33333 16C1.9401 16 0 14.0599 0 11.6667V4.33333Z\" fill=\"#363636\"/>\n  <path d=\"M3.9165 9.99934V9.16602H4.74983V9.99934H3.9165Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M26 8C26 3.58172 29.3517 0 33.4863 0H52.5137C56.6483 0 60 3.58172 60 8C60 12.4183 56.6483 16 52.5137 16H33.4863C29.3517 16 26 12.4183 26 8Z\" fill=\"#363636\"/>\n  <path d=\"M49.3687 9.95834V6.26232H50.0213V6.81966C50.256 6.40899 50.7326 6.16699 51.2606 6.16699C52.0599 6.16699 52.6173 6.67299 52.6173 7.65566V9.95834H51.972V7.69234C51.972 7.04696 51.6053 6.70966 51.07 6.70966C50.4906 6.70966 50.0213 7.17168 50.0213 7.82433V9.95834H49.3687Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M45.2538 9.95773L44.5718 6.26172H45.1877L45.6717 9.31242L46.3098 7.30306H46.9184L47.5491 9.29041L48.0404 6.26172H48.6564L47.9744 9.95773H47.2411L46.6178 8.03641L45.9871 9.95773H45.2538Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M42.3709 10.0536C41.2489 10.0536 40.5889 9.21765 40.5889 8.1103C40.5889 7.01035 41.2489 6.16699 42.3709 6.16699C43.4929 6.16699 44.1529 7.01035 44.1529 8.1103C44.1529 9.21765 43.4929 10.0536 42.3709 10.0536ZM42.3709 9.51096C43.1775 9.51096 43.4856 8.82164 43.4856 8.10296C43.4856 7.39163 43.1775 6.70966 42.3709 6.70966C41.5642 6.70966 41.2562 7.39163 41.2562 8.10296C41.2562 8.82164 41.5642 9.51096 42.3709 9.51096Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M38.2805 10.0536C37.1952 10.0536 36.5132 9.22499 36.5132 8.1103C36.5132 7.00302 37.1952 6.16699 38.2805 6.16699C39.1972 6.16699 40.0038 6.68766 39.9159 8.27896H37.1805C37.2319 8.96103 37.5472 9.5183 38.2805 9.5183C38.7718 9.5183 39.0945 9.21765 39.2045 8.87299H39.8499C39.7472 9.48903 39.1679 10.0536 38.2805 10.0536ZM37.1952 7.78765H39.2852C39.2338 7.04696 38.8892 6.70232 38.2805 6.70232C37.6132 6.70232 37.2832 7.18635 37.1952 7.78765Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M33.3828 9.95773V6.26172H34.0501V6.88506C34.2848 6.47439 34.6882 6.26172 35.1061 6.26172H35.9935V6.88506H35.0548C34.4682 6.88506 34.0501 7.26638 34.0501 8.00706V9.95773H33.3828Z\" fill=\"#F6F6F6\"/>\n</svg>`;\n//# sourceMappingURL=reown-logo.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js\n"));

/***/ })

}]);