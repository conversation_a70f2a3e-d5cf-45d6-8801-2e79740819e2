"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-b4b32b"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js ***!
  \*******************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   warningCircleSvg: function() { return /* binding */ warningCircleSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst warningCircleSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    d=\"M11 6.67a1 1 0 1 0-2 0v2.66a1 1 0 0 0 2 0V6.67ZM10 14.5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z\"\n  />\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M10 1a9 9 0 1 0 0 18 9 9 0 0 0 0-18Zm-7 9a7 7 0 1 1 14 0 7 7 0 0 1-14 0Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=warning-circle.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy93YXJuaW5nLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQix5QkFBeUIsd0NBQUc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy93YXJuaW5nLWNpcmNsZS5qcz9lOGYyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3Qgd2FybmluZ0NpcmNsZVN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBkPVwiTTExIDYuNjdhMSAxIDAgMSAwLTIgMHYyLjY2YTEgMSAwIDAgMCAyIDBWNi42N1pNMTAgMTQuNWExLjI1IDEuMjUgMCAxIDAgMC0yLjUgMS4yNSAxLjI1IDAgMCAwIDAgMi41WlwiXG4gIC8+XG4gIDxwYXRoXG4gICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgZmlsbC1ydWxlPVwiZXZlbm9kZFwiXG4gICAgZD1cIk0xMCAxYTkgOSAwIDEgMCAwIDE4IDkgOSAwIDAgMCAwLTE4Wm0tNyA5YTcgNyAwIDEgMSAxNCAwIDcgNyAwIDAgMS0xNCAwWlwiXG4gICAgY2xpcC1ydWxlPVwiZXZlbm9kZFwiXG4gIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2FybmluZy1jaXJjbGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js\n"));

/***/ })

}]);