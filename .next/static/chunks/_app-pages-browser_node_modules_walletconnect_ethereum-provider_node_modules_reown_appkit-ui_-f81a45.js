"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-f81a45"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js ***!
  \***********************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   recycleHorizontalSvg: function() { return /* binding */ recycleHorizontalSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst recycleHorizontalSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  fill=\"none\"\n  viewBox=\"0 0 21 20\"\n>\n  <path\n    fill=\"currentColor\"\n    d=\"M8.8071 0.292893C9.19763 0.683417 9.19763 1.31658 8.8071 1.70711L6.91421 3.6H11.8404C14.3368 3.6 16.5533 5.1975 17.3427 7.56588L17.4487 7.88377C17.6233 8.40772 17.3402 8.97404 16.8162 9.14868C16.2923 9.32333 15.726 9.04017 15.5513 8.51623L15.4453 8.19834C14.9281 6.64664 13.476 5.6 11.8404 5.6H6.91421L8.8071 7.49289C9.19763 7.88342 9.19763 8.51658 8.8071 8.90711C8.41658 9.29763 7.78341 9.29763 7.39289 8.90711L3.79289 5.30711C3.40236 4.91658 3.40236 4.28342 3.79289 3.89289L7.39289 0.292893C7.78341 -0.0976311 8.41658 -0.0976311 8.8071 0.292893ZM4.18377 10.8513C4.70771 10.6767 5.27403 10.9598 5.44868 11.4838L5.55464 11.8017C6.07188 13.3534 7.52401 14.4 9.15964 14.4L14.0858 14.4L12.1929 12.5071C11.8024 12.1166 11.8024 11.4834 12.1929 11.0929C12.5834 10.7024 13.2166 10.7024 13.6071 11.0929L17.2071 14.6929C17.5976 15.0834 17.5976 15.7166 17.2071 16.1071L13.6071 19.7071C13.2166 20.0976 12.5834 20.0976 12.1929 19.7071C11.8024 19.3166 11.8024 18.6834 12.1929 18.2929L14.0858 16.4L9.15964 16.4C6.66314 16.4 4.44674 14.8025 3.65728 12.4341L3.55131 12.1162C3.37667 11.5923 3.65983 11.026 4.18377 10.8513Z\"\n  /></svg\n>`;\n//# sourceMappingURL=recycle-horizontal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js\n"));

/***/ })

}]);