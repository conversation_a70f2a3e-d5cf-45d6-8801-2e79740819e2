"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-1cc6b0"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js ***!
  \***************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   playStoreSvg: function() { return /* binding */ playStoreSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst playStoreSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) ` <svg\n  width=\"36\"\n  height=\"36\"\n  fill=\"none\"\n>\n  <path\n    d=\"M0 8a8 8 0 0 1 8-8h20a8 8 0 0 1 8 8v20a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z\"\n    fill=\"#fff\"\n    fill-opacity=\".05\"\n  />\n  <path\n    d=\"m18.262 17.513-8.944 9.49v.01a2.417 2.417 0 0 0 3.56 1.452l.026-.017 10.061-5.803-4.703-5.132Z\"\n    fill=\"#EA4335\"\n  />\n  <path\n    d=\"m27.307 15.9-.008-.008-4.342-2.52-4.896 4.36 4.913 4.912 4.325-2.494a2.42 2.42 0 0 0 .008-4.25Z\"\n    fill=\"#FBBC04\"\n  />\n  <path\n    d=\"M9.318 8.997c-.05.202-.084.403-.084.622V26.39c0 .218.025.42.084.621l9.246-9.247-9.246-8.768Z\"\n    fill=\"#4285F4\"\n  />\n  <path\n    d=\"m18.33 18 4.627-4.628-10.053-5.828a2.427 2.427 0 0 0-3.586 1.444L18.329 18Z\"\n    fill=\"#34A853\"\n  />\n  <path\n    d=\"M8 .5h20A7.5 7.5 0 0 1 35.5 8v20a7.5 7.5 0 0 1-7.5 7.5H8A7.5 7.5 0 0 1 .5 28V8A7.5 7.5 0 0 1 8 .5Z\"\n    stroke=\"#fff\"\n    stroke-opacity=\".05\"\n  />\n</svg>`;\n//# sourceMappingURL=play-store.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js\n"));

/***/ })

}]);