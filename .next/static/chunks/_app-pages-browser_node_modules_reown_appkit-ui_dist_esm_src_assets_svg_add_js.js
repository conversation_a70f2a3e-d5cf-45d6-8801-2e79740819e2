"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_add_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addSvg: function() { return /* binding */ addSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst addSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"14\"\n  height=\"14\"\n  viewBox=\"0 0 14 14\"\n  fill=\"none\"\n  xmlns=\"http://www.w3.org/2000/svg\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M7.0023 0.875C7.48571 0.875 7.8776 1.26675 7.8776 1.75V6.125H12.2541C12.7375 6.125 13.1294 6.51675 13.1294 7C13.1294 7.48325 12.7375 7.875 12.2541 7.875H7.8776V12.25C7.8776 12.7332 7.48571 13.125 7.0023 13.125C6.51889 13.125 6.12701 12.7332 6.12701 12.25V7.875H1.75054C1.26713 7.875 0.875244 7.48325 0.875244 7C0.875244 6.51675 1.26713 6.125 1.75054 6.125H6.12701V1.75C6.12701 1.26675 6.51889 0.875 7.0023 0.875Z\"\n    fill=\"#667dff\"\n  /></svg\n>`;\n//# sourceMappingURL=add.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2FkZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixlQUFlLHdDQUFHO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9hZGQuanM/MzRiNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IGFkZFN2ZyA9IHN2ZyBgPHN2Z1xuICB3aWR0aD1cIjE0XCJcbiAgaGVpZ2h0PVwiMTRcIlxuICB2aWV3Qm94PVwiMCAwIDE0IDE0XCJcbiAgZmlsbD1cIm5vbmVcIlxuICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbj5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBmaWxsLXJ1bGU9XCJldmVub2RkXCJcbiAgICBjbGlwLXJ1bGU9XCJldmVub2RkXCJcbiAgICBkPVwiTTcuMDAyMyAwLjg3NUM3LjQ4NTcxIDAuODc1IDcuODc3NiAxLjI2Njc1IDcuODc3NiAxLjc1VjYuMTI1SDEyLjI1NDFDMTIuNzM3NSA2LjEyNSAxMy4xMjk0IDYuNTE2NzUgMTMuMTI5NCA3QzEzLjEyOTQgNy40ODMyNSAxMi43Mzc1IDcuODc1IDEyLjI1NDEgNy44NzVINy44Nzc2VjEyLjI1QzcuODc3NiAxMi43MzMyIDcuNDg1NzEgMTMuMTI1IDcuMDAyMyAxMy4xMjVDNi41MTg4OSAxMy4xMjUgNi4xMjcwMSAxMi43MzMyIDYuMTI3MDEgMTIuMjVWNy44NzVIMS43NTA1NEMxLjI2NzEzIDcuODc1IDAuODc1MjQ0IDcuNDgzMjUgMC44NzUyNDQgN0MwLjg3NTI0NCA2LjUxNjc1IDEuMjY3MTMgNi4xMjUgMS43NTA1NCA2LjEyNUg2LjEyNzAxVjEuNzVDNi4xMjcwMSAxLjI2Njc1IDYuNTE4ODkgMC44NzUgNy4wMDIzIDAuODc1WlwiXG4gICAgZmlsbD1cIiM2NjdkZmZcIlxuICAvPjwvc3ZnXG4+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFkZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js\n"));

/***/ })

}]);