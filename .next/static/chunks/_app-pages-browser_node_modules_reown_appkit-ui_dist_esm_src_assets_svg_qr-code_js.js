"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_qr-code_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   qrCodeIcon: function() { return /* binding */ qrCodeIcon; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst qrCodeIcon = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    d=\"M3 6a3 3 0 0 1 3-3h1a1 1 0 1 0 0-2H6a5 5 0 0 0-5 5v1a1 1 0 0 0 2 0V6ZM13 1a1 1 0 1 0 0 2h1a3 3 0 0 1 3 3v1a1 1 0 1 0 2 0V6a5 5 0 0 0-5-5h-1ZM3 13a1 1 0 1 0-2 0v1a5 5 0 0 0 5 5h1a1 1 0 1 0 0-2H6a3 3 0 0 1-3-3v-1ZM19 13a1 1 0 1 0-2 0v1a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1.01a5 5 0 0 0 5-5v-1ZM5.3 6.36c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05A1.5 1.5 0 0 0 9.2 8.14c.06-.2.06-.43.06-.89s0-.7-.06-.89A1.5 1.5 0 0 0 8.14 5.3c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06ZM10.8 6.36c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05a1.5 1.5 0 0 0 1.06-1.06c.06-.2.06-.43.06-.89s0-.7-.06-.89a1.5 1.5 0 0 0-1.06-1.06c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06ZM5.26 12.75c0-.46 0-.7.05-.89a1.5 1.5 0 0 1 1.06-1.06c.19-.05.42-.05.89-.05.46 0 .7 0 .88.05.52.14.93.54 1.06 1.06.06.2.06.43.06.89s0 .7-.06.89a1.5 1.5 0 0 1-1.06 1.06c-.19.05-.42.05-.88.05-.47 0-.7 0-.9-.05a1.5 1.5 0 0 1-1.05-1.06c-.05-.2-.05-.43-.05-.89ZM10.8 11.86c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05a1.5 1.5 0 0 0 1.06-1.06c.06-.2.06-.43.06-.89s0-.7-.06-.89a1.5 1.5 0 0 0-1.06-1.06c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06Z\"\n  />\n</svg>`;\n//# sourceMappingURL=qr-code.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js\n"));

/***/ })

}]);