"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_ethereum_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/ethereum.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/ethereum.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ethereumSvg: function() { return /* binding */ ethereumSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst ethereumSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"13\" height=\"12\" viewBox=\"0 0 13 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M6.66686 12C9.9805 12 12.6667 9.31371 12.6667 6C12.6667 2.68629 9.9805 0 6.66686 0C3.35323 0 0.666992 2.68629 0.666992 6C0.666992 9.31371 3.35323 12 6.66686 12Z\" fill=\"var(--wui-color-gray-glass-005)\"/>\n<path d=\"M6.6658 1.50098V4.82739L9.47712 6.08381L6.6658 1.50098Z\" fill=\"var(--wui-color-gray-glass-060)\"/>\n<path d=\"M6.6658 1.50098L3.85449 6.08381L6.6658 4.82739V1.50098Z\" fill=\"var(--wui-color-gray-glass-090)\"/>\n<path d=\"M6.6658 8.23909V10.4993L9.47876 6.60705L6.6658 8.23909Z\" fill=\"var(--wui-color-gray-glass-060)\"/>\n<path d=\"M6.6658 10.4993V8.23909L3.85449 6.60705L6.6658 10.4993Z\" fill=\"var(--wui-color-gray-glass-090)\"/>\n<path d=\"M6.6658 7.71585L9.47712 6.08381L6.6658 4.82739V7.71585Z\" fill=\"var(--wui-color-gray-glass-020)\"/>\n<path d=\"M3.85449 6.08381L6.6658 7.71585V4.82739L3.85449 6.08381Z\" fill=\"var(--wui-color-gray-glass-060)\"/>\n</svg>\n`;\n//# sourceMappingURL=ethereum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2V0aGVyZXVtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBQ25CLG9CQUFvQix3Q0FBRztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9ldGhlcmV1bS5qcz80NWVkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgZXRoZXJldW1TdmcgPSBzdmcgYDxzdmcgd2lkdGg9XCIxM1wiIGhlaWdodD1cIjEyXCIgdmlld0JveD1cIjAgMCAxMyAxMlwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxuPHBhdGggZD1cIk02LjY2Njg2IDEyQzkuOTgwNSAxMiAxMi42NjY3IDkuMzEzNzEgMTIuNjY2NyA2QzEyLjY2NjcgMi42ODYyOSA5Ljk4MDUgMCA2LjY2Njg2IDBDMy4zNTMyMyAwIDAuNjY2OTkyIDIuNjg2MjkgMC42NjY5OTIgNkMwLjY2Njk5MiA5LjMxMzcxIDMuMzUzMjMgMTIgNi42NjY4NiAxMlpcIiBmaWxsPVwidmFyKC0td3VpLWNvbG9yLWdyYXktZ2xhc3MtMDA1KVwiLz5cbjxwYXRoIGQ9XCJNNi42NjU4IDEuNTAwOThWNC44MjczOUw5LjQ3NzEyIDYuMDgzODFMNi42NjU4IDEuNTAwOThaXCIgZmlsbD1cInZhcigtLXd1aS1jb2xvci1ncmF5LWdsYXNzLTA2MClcIi8+XG48cGF0aCBkPVwiTTYuNjY1OCAxLjUwMDk4TDMuODU0NDkgNi4wODM4MUw2LjY2NTggNC44MjczOVYxLjUwMDk4WlwiIGZpbGw9XCJ2YXIoLS13dWktY29sb3ItZ3JheS1nbGFzcy0wOTApXCIvPlxuPHBhdGggZD1cIk02LjY2NTggOC4yMzkwOVYxMC40OTkzTDkuNDc4NzYgNi42MDcwNUw2LjY2NTggOC4yMzkwOVpcIiBmaWxsPVwidmFyKC0td3VpLWNvbG9yLWdyYXktZ2xhc3MtMDYwKVwiLz5cbjxwYXRoIGQ9XCJNNi42NjU4IDEwLjQ5OTNWOC4yMzkwOUwzLjg1NDQ5IDYuNjA3MDVMNi42NjU4IDEwLjQ5OTNaXCIgZmlsbD1cInZhcigtLXd1aS1jb2xvci1ncmF5LWdsYXNzLTA5MClcIi8+XG48cGF0aCBkPVwiTTYuNjY1OCA3LjcxNTg1TDkuNDc3MTIgNi4wODM4MUw2LjY2NTggNC44MjczOVY3LjcxNTg1WlwiIGZpbGw9XCJ2YXIoLS13dWktY29sb3ItZ3JheS1nbGFzcy0wMjApXCIvPlxuPHBhdGggZD1cIk0zLjg1NDQ5IDYuMDgzODFMNi42NjU4IDcuNzE1ODVWNC44MjczOUwzLjg1NDQ5IDYuMDgzODFaXCIgZmlsbD1cInZhcigtLXd1aS1jb2xvci1ncmF5LWdsYXNzLTA2MClcIi8+XG48L3N2Zz5cbmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ldGhlcmV1bS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/ethereum.js\n"));

/***/ })

}]);