"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnectBrownSvg: function() { return /* binding */ walletConnectBrownSvg; },\n/* harmony export */   walletConnectLightBrownSvg: function() { return /* binding */ walletConnectLightBrownSvg; },\n/* harmony export */   walletConnectSvg: function() { return /* binding */ walletConnectSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst walletConnectSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 96 67\">\n  <path\n    fill=\"currentColor\"\n    d=\"M25.32 18.8a32.56 32.56 0 0 1 45.36 0l1.5 1.47c.63.62.63 1.61 0 2.22l-5.15 5.05c-.31.3-.82.3-1.14 0l-2.07-2.03a22.71 22.71 0 0 0-31.64 0l-2.22 2.18c-.31.3-.82.3-1.14 0l-5.15-5.05a1.55 1.55 0 0 1 0-2.22l1.65-1.62Zm56.02 10.44 4.59 4.5c.63.6.63 1.6 0 2.21l-20.7 20.26c-.62.61-1.63.61-2.26 0L48.28 41.83a.4.4 0 0 0-.56 0L33.03 56.21c-.63.61-1.64.61-2.27 0L10.07 35.95a1.55 1.55 0 0 1 0-2.22l4.59-4.5a1.63 1.63 0 0 1 2.27 0L31.6 43.63a.4.4 0 0 0 .57 0l14.69-14.38a1.63 1.63 0 0 1 2.26 0l14.69 14.38a.4.4 0 0 0 .57 0l14.68-14.38a1.63 1.63 0 0 1 2.27 0Z\"\n  />\n  <path\n    stroke=\"#000\"\n    stroke-opacity=\".1\"\n    d=\"M25.67 19.15a32.06 32.06 0 0 1 44.66 0l1.5 1.48c.43.42.43 1.09 0 1.5l-5.15 5.05a.31.31 0 0 1-.44 0l-2.07-2.03a23.21 23.21 0 0 0-32.34 0l-2.22 2.18a.31.31 0 0 1-.44 0l-5.15-5.05a1.05 1.05 0 0 1 0-1.5l1.65-1.63ZM81 29.6l4.6 4.5c.42.41.42 1.09 0 1.5l-20.7 20.26c-.43.43-1.14.43-1.57 0L48.63 41.47a.9.9 0 0 0-1.26 0L32.68 55.85c-.43.43-1.14.43-1.57 0L10.42 35.6a1.05 1.05 0 0 1 0-1.5l4.59-4.5a1.13 1.13 0 0 1 1.57 0l14.68 14.38a.9.9 0 0 0 1.27 0l-.35-.35.35.35L47.22 29.6a1.13 1.13 0 0 1 1.56 0l14.7 14.38a.9.9 0 0 0 1.26 0L79.42 29.6a1.13 1.13 0 0 1 1.57 0Z\"\n  />\n</svg>`;\nconst walletConnectLightBrownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_22274_4692)\">\n<path d=\"M0 6.64C0 4.17295 0 2.93942 0.525474 2.01817C0.880399 1.39592 1.39592 0.880399 2.01817 0.525474C2.93942 0 4.17295 0 6.64 0H9.36C11.8271 0 13.0606 0 13.9818 0.525474C14.6041 0.880399 15.1196 1.39592 15.4745 2.01817C16 2.93942 16 4.17295 16 6.64V9.36C16 11.8271 16 13.0606 15.4745 13.9818C15.1196 14.6041 14.6041 15.1196 13.9818 15.4745C13.0606 16 11.8271 16 9.36 16H6.64C4.17295 16 2.93942 16 2.01817 15.4745C1.39592 15.1196 0.880399 14.6041 0.525474 13.9818C0 13.0606 0 11.8271 0 9.36V6.64Z\" fill=\"#C7B994\"/>\n<path d=\"M4.49038 5.76609C6.42869 3.86833 9.5713 3.86833 11.5096 5.76609L11.7429 5.99449C11.8398 6.08938 11.8398 6.24323 11.7429 6.33811L10.9449 7.11942C10.8964 7.16686 10.8179 7.16686 10.7694 7.11942L10.4484 6.80512C9.09617 5.48119 6.90381 5.48119 5.5516 6.80512L5.20782 7.14171C5.15936 7.18915 5.08079 7.18915 5.03234 7.14171L4.23434 6.3604C4.13742 6.26552 4.13742 6.11167 4.23434 6.01678L4.49038 5.76609ZM13.1599 7.38192L13.8702 8.07729C13.9671 8.17217 13.9671 8.32602 13.8702 8.4209L10.6677 11.5564C10.5708 11.6513 10.4137 11.6513 10.3168 11.5564L8.04388 9.33105C8.01965 9.30733 7.98037 9.30733 7.95614 9.33105L5.6833 11.5564C5.58638 11.6513 5.42925 11.6513 5.33234 11.5564L2.12982 8.42087C2.0329 8.32598 2.0329 8.17213 2.12982 8.07724L2.84004 7.38188C2.93695 7.28699 3.09408 7.28699 3.191 7.38188L5.46392 9.60726C5.48815 9.63098 5.52743 9.63098 5.55166 9.60726L7.82447 7.38188C7.92138 7.28699 8.07851 7.28699 8.17543 7.38187L10.4484 9.60726C10.4726 9.63098 10.5119 9.63098 10.5361 9.60726L12.809 7.38192C12.9059 7.28703 13.063 7.28703 13.1599 7.38192Z\" fill=\"#202020\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_22274_4692\">\n<path d=\"M0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8Z\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n`;\nconst walletConnectBrownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<circle cx=\"11\" cy=\"11\" r=\"11\" transform=\"matrix(-1 0 0 1 23 1)\" fill=\"#202020\"/>\n<circle cx=\"11\" cy=\"11\" r=\"11.5\" transform=\"matrix(-1 0 0 1 23 1)\" stroke=\"#C7B994\" stroke-opacity=\"0.7\"/>\n<path d=\"M15.4523 11.0686L16.7472 9.78167C13.8205 6.87297 10.1838 6.87297 7.25708 9.78167L8.55201 11.0686C10.7779 8.85645 13.2279 8.85645 15.4538 11.0686H15.4523Z\" fill=\"#C7B994\"/>\n<path d=\"M15.0199 14.067L12 11.0656L8.98 14.067L5.96004 11.0656L4.66663 12.3511L8.98 16.6393L12 13.638L15.0199 16.6393L19.3333 12.3511L18.0399 11.0656L15.0199 14.067Z\" fill=\"#C7B994\"/>\n</svg>\n`;\n//# sourceMappingURL=walletconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3dhbGxldGNvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwQjtBQUNuQix5QkFBeUIsd0NBQUc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxtQ0FBbUMsd0NBQUc7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sOEJBQThCLHdDQUFHO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvd2FsbGV0Y29ubmVjdC5qcz9hZTdiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3Qgd2FsbGV0Q29ubmVjdFN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgOTYgNjdcIj5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBkPVwiTTI1LjMyIDE4LjhhMzIuNTYgMzIuNTYgMCAwIDEgNDUuMzYgMGwxLjUgMS40N2MuNjMuNjIuNjMgMS42MSAwIDIuMjJsLTUuMTUgNS4wNWMtLjMxLjMtLjgyLjMtMS4xNCAwbC0yLjA3LTIuMDNhMjIuNzEgMjIuNzEgMCAwIDAtMzEuNjQgMGwtMi4yMiAyLjE4Yy0uMzEuMy0uODIuMy0xLjE0IDBsLTUuMTUtNS4wNWExLjU1IDEuNTUgMCAwIDEgMC0yLjIybDEuNjUtMS42MlptNTYuMDIgMTAuNDQgNC41OSA0LjVjLjYzLjYuNjMgMS42IDAgMi4yMWwtMjAuNyAyMC4yNmMtLjYyLjYxLTEuNjMuNjEtMi4yNiAwTDQ4LjI4IDQxLjgzYS40LjQgMCAwIDAtLjU2IDBMMzMuMDMgNTYuMjFjLS42My42MS0xLjY0LjYxLTIuMjcgMEwxMC4wNyAzNS45NWExLjU1IDEuNTUgMCAwIDEgMC0yLjIybDQuNTktNC41YTEuNjMgMS42MyAwIDAgMSAyLjI3IDBMMzEuNiA0My42M2EuNC40IDAgMCAwIC41NyAwbDE0LjY5LTE0LjM4YTEuNjMgMS42MyAwIDAgMSAyLjI2IDBsMTQuNjkgMTQuMzhhLjQuNCAwIDAgMCAuNTcgMGwxNC42OC0xNC4zOGExLjYzIDEuNjMgMCAwIDEgMi4yNyAwWlwiXG4gIC8+XG4gIDxwYXRoXG4gICAgc3Ryb2tlPVwiIzAwMFwiXG4gICAgc3Ryb2tlLW9wYWNpdHk9XCIuMVwiXG4gICAgZD1cIk0yNS42NyAxOS4xNWEzMi4wNiAzMi4wNiAwIDAgMSA0NC42NiAwbDEuNSAxLjQ4Yy40My40Mi40MyAxLjA5IDAgMS41bC01LjE1IDUuMDVhLjMxLjMxIDAgMCAxLS40NCAwbC0yLjA3LTIuMDNhMjMuMjEgMjMuMjEgMCAwIDAtMzIuMzQgMGwtMi4yMiAyLjE4YS4zMS4zMSAwIDAgMS0uNDQgMGwtNS4xNS01LjA1YTEuMDUgMS4wNSAwIDAgMSAwLTEuNWwxLjY1LTEuNjNaTTgxIDI5LjZsNC42IDQuNWMuNDIuNDEuNDIgMS4wOSAwIDEuNWwtMjAuNyAyMC4yNmMtLjQzLjQzLTEuMTQuNDMtMS41NyAwTDQ4LjYzIDQxLjQ3YS45LjkgMCAwIDAtMS4yNiAwTDMyLjY4IDU1Ljg1Yy0uNDMuNDMtMS4xNC40My0xLjU3IDBMMTAuNDIgMzUuNmExLjA1IDEuMDUgMCAwIDEgMC0xLjVsNC41OS00LjVhMS4xMyAxLjEzIDAgMCAxIDEuNTcgMGwxNC42OCAxNC4zOGEuOS45IDAgMCAwIDEuMjcgMGwtLjM1LS4zNS4zNS4zNUw0Ny4yMiAyOS42YTEuMTMgMS4xMyAwIDAgMSAxLjU2IDBsMTQuNyAxNC4zOGEuOS45IDAgMCAwIDEuMjYgMEw3OS40MiAyOS42YTEuMTMgMS4xMyAwIDAgMSAxLjU3IDBaXCJcbiAgLz5cbjwvc3ZnPmA7XG5leHBvcnQgY29uc3Qgd2FsbGV0Q29ubmVjdExpZ2h0QnJvd25TdmcgPSBzdmcgYFxuPHN2ZyB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTZcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG48ZyBjbGlwLXBhdGg9XCJ1cmwoI2NsaXAwXzIyMjc0XzQ2OTIpXCI+XG48cGF0aCBkPVwiTTAgNi42NEMwIDQuMTcyOTUgMCAyLjkzOTQyIDAuNTI1NDc0IDIuMDE4MTdDMC44ODAzOTkgMS4zOTU5MiAxLjM5NTkyIDAuODgwMzk5IDIuMDE4MTcgMC41MjU0NzRDMi45Mzk0MiAwIDQuMTcyOTUgMCA2LjY0IDBIOS4zNkMxMS44MjcxIDAgMTMuMDYwNiAwIDEzLjk4MTggMC41MjU0NzRDMTQuNjA0MSAwLjg4MDM5OSAxNS4xMTk2IDEuMzk1OTIgMTUuNDc0NSAyLjAxODE3QzE2IDIuOTM5NDIgMTYgNC4xNzI5NSAxNiA2LjY0VjkuMzZDMTYgMTEuODI3MSAxNiAxMy4wNjA2IDE1LjQ3NDUgMTMuOTgxOEMxNS4xMTk2IDE0LjYwNDEgMTQuNjA0MSAxNS4xMTk2IDEzLjk4MTggMTUuNDc0NUMxMy4wNjA2IDE2IDExLjgyNzEgMTYgOS4zNiAxNkg2LjY0QzQuMTcyOTUgMTYgMi45Mzk0MiAxNiAyLjAxODE3IDE1LjQ3NDVDMS4zOTU5MiAxNS4xMTk2IDAuODgwMzk5IDE0LjYwNDEgMC41MjU0NzQgMTMuOTgxOEMwIDEzLjA2MDYgMCAxMS44MjcxIDAgOS4zNlY2LjY0WlwiIGZpbGw9XCIjQzdCOTk0XCIvPlxuPHBhdGggZD1cIk00LjQ5MDM4IDUuNzY2MDlDNi40Mjg2OSAzLjg2ODMzIDkuNTcxMyAzLjg2ODMzIDExLjUwOTYgNS43NjYwOUwxMS43NDI5IDUuOTk0NDlDMTEuODM5OCA2LjA4OTM4IDExLjgzOTggNi4yNDMyMyAxMS43NDI5IDYuMzM4MTFMMTAuOTQ0OSA3LjExOTQyQzEwLjg5NjQgNy4xNjY4NiAxMC44MTc5IDcuMTY2ODYgMTAuNzY5NCA3LjExOTQyTDEwLjQ0ODQgNi44MDUxMkM5LjA5NjE3IDUuNDgxMTkgNi45MDM4MSA1LjQ4MTE5IDUuNTUxNiA2LjgwNTEyTDUuMjA3ODIgNy4xNDE3MUM1LjE1OTM2IDcuMTg5MTUgNS4wODA3OSA3LjE4OTE1IDUuMDMyMzQgNy4xNDE3MUw0LjIzNDM0IDYuMzYwNEM0LjEzNzQyIDYuMjY1NTIgNC4xMzc0MiA2LjExMTY3IDQuMjM0MzQgNi4wMTY3OEw0LjQ5MDM4IDUuNzY2MDlaTTEzLjE1OTkgNy4zODE5MkwxMy44NzAyIDguMDc3MjlDMTMuOTY3MSA4LjE3MjE3IDEzLjk2NzEgOC4zMjYwMiAxMy44NzAyIDguNDIwOUwxMC42Njc3IDExLjU1NjRDMTAuNTcwOCAxMS42NTEzIDEwLjQxMzcgMTEuNjUxMyAxMC4zMTY4IDExLjU1NjRMOC4wNDM4OCA5LjMzMTA1QzguMDE5NjUgOS4zMDczMyA3Ljk4MDM3IDkuMzA3MzMgNy45NTYxNCA5LjMzMTA1TDUuNjgzMyAxMS41NTY0QzUuNTg2MzggMTEuNjUxMyA1LjQyOTI1IDExLjY1MTMgNS4zMzIzNCAxMS41NTY0TDIuMTI5ODIgOC40MjA4N0MyLjAzMjkgOC4zMjU5OCAyLjAzMjkgOC4xNzIxMyAyLjEyOTgyIDguMDc3MjRMMi44NDAwNCA3LjM4MTg4QzIuOTM2OTUgNy4yODY5OSAzLjA5NDA4IDcuMjg2OTkgMy4xOTEgNy4zODE4OEw1LjQ2MzkyIDkuNjA3MjZDNS40ODgxNSA5LjYzMDk4IDUuNTI3NDMgOS42MzA5OCA1LjU1MTY2IDkuNjA3MjZMNy44MjQ0NyA3LjM4MTg4QzcuOTIxMzggNy4yODY5OSA4LjA3ODUxIDcuMjg2OTkgOC4xNzU0MyA3LjM4MTg3TDEwLjQ0ODQgOS42MDcyNkMxMC40NzI2IDkuNjMwOTggMTAuNTExOSA5LjYzMDk4IDEwLjUzNjEgOS42MDcyNkwxMi44MDkgNy4zODE5MkMxMi45MDU5IDcuMjg3MDMgMTMuMDYzIDcuMjg3MDMgMTMuMTU5OSA3LjM4MTkyWlwiIGZpbGw9XCIjMjAyMDIwXCIvPlxuPC9nPlxuPGRlZnM+XG48Y2xpcFBhdGggaWQ9XCJjbGlwMF8yMjI3NF80NjkyXCI+XG48cGF0aCBkPVwiTTAgOEMwIDMuNTgxNzIgMy41ODE3MiAwIDggMEMxMi40MTgzIDAgMTYgMy41ODE3MiAxNiA4QzE2IDEyLjQxODMgMTIuNDE4MyAxNiA4IDE2QzMuNTgxNzIgMTYgMCAxMi40MTgzIDAgOFpcIiBmaWxsPVwid2hpdGVcIi8+XG48L2NsaXBQYXRoPlxuPC9kZWZzPlxuPC9zdmc+XG5gO1xuZXhwb3J0IGNvbnN0IHdhbGxldENvbm5lY3RCcm93blN2ZyA9IHN2ZyBgXG48c3ZnIHdpZHRoPVwiMjRcIiBoZWlnaHQ9XCIyNFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cbjxjaXJjbGUgY3g9XCIxMVwiIGN5PVwiMTFcIiByPVwiMTFcIiB0cmFuc2Zvcm09XCJtYXRyaXgoLTEgMCAwIDEgMjMgMSlcIiBmaWxsPVwiIzIwMjAyMFwiLz5cbjxjaXJjbGUgY3g9XCIxMVwiIGN5PVwiMTFcIiByPVwiMTEuNVwiIHRyYW5zZm9ybT1cIm1hdHJpeCgtMSAwIDAgMSAyMyAxKVwiIHN0cm9rZT1cIiNDN0I5OTRcIiBzdHJva2Utb3BhY2l0eT1cIjAuN1wiLz5cbjxwYXRoIGQ9XCJNMTUuNDUyMyAxMS4wNjg2TDE2Ljc0NzIgOS43ODE2N0MxMy44MjA1IDYuODcyOTcgMTAuMTgzOCA2Ljg3Mjk3IDcuMjU3MDggOS43ODE2N0w4LjU1MjAxIDExLjA2ODZDMTAuNzc3OSA4Ljg1NjQ1IDEzLjIyNzkgOC44NTY0NSAxNS40NTM4IDExLjA2ODZIMTUuNDUyM1pcIiBmaWxsPVwiI0M3Qjk5NFwiLz5cbjxwYXRoIGQ9XCJNMTUuMDE5OSAxNC4wNjdMMTIgMTEuMDY1Nkw4Ljk4IDE0LjA2N0w1Ljk2MDA0IDExLjA2NTZMNC42NjY2MyAxMi4zNTExTDguOTggMTYuNjM5M0wxMiAxMy42MzhMMTUuMDE5OSAxNi42MzkzTDE5LjMzMzMgMTIuMzUxMUwxOC4wMzk5IDExLjA2NTZMMTUuMDE5OSAxNC4wNjdaXCIgZmlsbD1cIiNDN0I5OTRcIi8+XG48L3N2Zz5cbmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YWxsZXRjb25uZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js\n"));

/***/ })

}]);