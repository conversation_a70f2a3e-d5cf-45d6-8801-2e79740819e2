"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-ef60d2"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   farcasterSvg: function() { return /* binding */ farcasterSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst farcasterSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg style=\"border-radius: 9999px; overflow: hidden;\"  fill=\"none\" viewBox=\"0 0 1000 1000\">\n  <rect width=\"1000\" height=\"1000\" rx=\"9999\" ry=\"9999\" fill=\"#855DCD\"/>\n  <path fill=\"#855DCD\" d=\"M0 0h1000v1000H0V0Z\" />\n  <path\n    fill=\"#fff\"\n    d=\"M320 248h354v504h-51.96V521.13h-.5c-5.76-63.8-59.31-113.81-124.54-113.81s-118.78 50-124.53 113.81h-.5V752H320V248Z\"\n  />\n  <path\n    fill=\"#fff\"\n    d=\"m225 320 21.16 71.46h17.9v289.09a16.29 16.29 0 0 0-16.28 16.24v19.49h-3.25a16.3 16.3 0 0 0-16.28 16.24V752h182.26v-19.48a16.22 16.22 0 0 0-16.28-16.24h-3.25v-19.5a16.22 16.22 0 0 0-16.28-16.23h-19.52V320H225Zm400.3 360.55a16.3 16.3 0 0 0-15.04 10.02 16.2 16.2 0 0 0-1.24 6.22v19.49h-3.25a16.29 16.29 0 0 0-16.27 16.24V752h182.24v-19.48a16.23 16.23 0 0 0-16.27-16.24h-3.25v-19.5a16.2 16.2 0 0 0-10.04-15 16.3 16.3 0 0 0-6.23-1.23v-289.1h17.9L775 320H644.82v360.55H625.3Z\"\n  />\n</svg>`;\n//# sourceMappingURL=farcaster.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9mYXJjYXN0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIscUJBQXFCLHdDQUFHLHFDQUFxQyxpQkFBaUI7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9mYXJjYXN0ZXIuanM/ZTYyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IGZhcmNhc3RlclN2ZyA9IHN2ZyBgPHN2ZyBzdHlsZT1cImJvcmRlci1yYWRpdXM6IDk5OTlweDsgb3ZlcmZsb3c6IGhpZGRlbjtcIiAgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDEwMDAgMTAwMFwiPlxuICA8cmVjdCB3aWR0aD1cIjEwMDBcIiBoZWlnaHQ9XCIxMDAwXCIgcng9XCI5OTk5XCIgcnk9XCI5OTk5XCIgZmlsbD1cIiM4NTVEQ0RcIi8+XG4gIDxwYXRoIGZpbGw9XCIjODU1RENEXCIgZD1cIk0wIDBoMTAwMHYxMDAwSDBWMFpcIiAvPlxuICA8cGF0aFxuICAgIGZpbGw9XCIjZmZmXCJcbiAgICBkPVwiTTMyMCAyNDhoMzU0djUwNGgtNTEuOTZWNTIxLjEzaC0uNWMtNS43Ni02My44LTU5LjMxLTExMy44MS0xMjQuNTQtMTEzLjgxcy0xMTguNzggNTAtMTI0LjUzIDExMy44MWgtLjVWNzUySDMyMFYyNDhaXCJcbiAgLz5cbiAgPHBhdGhcbiAgICBmaWxsPVwiI2ZmZlwiXG4gICAgZD1cIm0yMjUgMzIwIDIxLjE2IDcxLjQ2aDE3Ljl2Mjg5LjA5YTE2LjI5IDE2LjI5IDAgMCAwLTE2LjI4IDE2LjI0djE5LjQ5aC0zLjI1YTE2LjMgMTYuMyAwIDAgMC0xNi4yOCAxNi4yNFY3NTJoMTgyLjI2di0xOS40OGExNi4yMiAxNi4yMiAwIDAgMC0xNi4yOC0xNi4yNGgtMy4yNXYtMTkuNWExNi4yMiAxNi4yMiAwIDAgMC0xNi4yOC0xNi4yM2gtMTkuNTJWMzIwSDIyNVptNDAwLjMgMzYwLjU1YTE2LjMgMTYuMyAwIDAgMC0xNS4wNCAxMC4wMiAxNi4yIDE2LjIgMCAwIDAtMS4yNCA2LjIydjE5LjQ5aC0zLjI1YTE2LjI5IDE2LjI5IDAgMCAwLTE2LjI3IDE2LjI0Vjc1MmgxODIuMjR2LTE5LjQ4YTE2LjIzIDE2LjIzIDAgMCAwLTE2LjI3LTE2LjI0aC0zLjI1di0xOS41YTE2LjIgMTYuMiAwIDAgMC0xMC4wNC0xNSAxNi4zIDE2LjMgMCAwIDAtNi4yMy0xLjIzdi0yODkuMWgxNy45TDc3NSAzMjBINjQ0LjgydjM2MC41NUg2MjUuM1pcIlxuICAvPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZhcmNhc3Rlci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js\n"));

/***/ })

}]);