"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_off_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   offSvg: function() { return /* binding */ offSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst offSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.99792 0C8.6291 0 9.14077 0.511675 9.14077 1.14286V6.41758C9.14077 7.04877 8.6291 7.56044 7.99792 7.56044C7.36673 7.56044 6.85506 7.04877 6.85506 6.41758V1.14286C6.85506 0.511675 7.36673 0 7.99792 0ZM5.44952 2.61998C5.79899 3.14558 5.6562 3.85497 5.1306 4.20444C3.73012 5.1356 2.8111 6.72446 2.8111 8.52747C2.8111 11.3921 5.13332 13.7143 7.99792 13.7143C10.8625 13.7143 13.1847 11.3921 13.1847 8.52747C13.1847 6.72446 12.2657 5.1356 10.8652 4.20444C10.3396 3.85497 10.1968 3.14558 10.5463 2.61998C10.8958 2.09437 11.6052 1.95158 12.1308 2.30105C14.1414 3.63786 15.4704 5.92721 15.4704 8.52747C15.4704 12.6544 12.1249 16 7.99792 16C3.87095 16 0.525391 12.6544 0.525391 8.52747C0.525391 5.92721 1.85448 3.63786 3.86506 2.30105C4.39066 1.95158 5.10005 2.09437 5.44952 2.61998Z\" fill=\"currentColor\"/>\n</svg>\n`;\n//# sourceMappingURL=off.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL29mZi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixlQUFlLHdDQUFHO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL29mZi5qcz82NDk0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3Qgb2ZmU3ZnID0gc3ZnIGA8c3ZnIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cbjxwYXRoIGZpbGwtcnVsZT1cImV2ZW5vZGRcIiBjbGlwLXJ1bGU9XCJldmVub2RkXCIgZD1cIk03Ljk5NzkyIDBDOC42MjkxIDAgOS4xNDA3NyAwLjUxMTY3NSA5LjE0MDc3IDEuMTQyODZWNi40MTc1OEM5LjE0MDc3IDcuMDQ4NzcgOC42MjkxIDcuNTYwNDQgNy45OTc5MiA3LjU2MDQ0QzcuMzY2NzMgNy41NjA0NCA2Ljg1NTA2IDcuMDQ4NzcgNi44NTUwNiA2LjQxNzU4VjEuMTQyODZDNi44NTUwNiAwLjUxMTY3NSA3LjM2NjczIDAgNy45OTc5MiAwWk01LjQ0OTUyIDIuNjE5OThDNS43OTg5OSAzLjE0NTU4IDUuNjU2MiAzLjg1NDk3IDUuMTMwNiA0LjIwNDQ0QzMuNzMwMTIgNS4xMzU2IDIuODExMSA2LjcyNDQ2IDIuODExMSA4LjUyNzQ3QzIuODExMSAxMS4zOTIxIDUuMTMzMzIgMTMuNzE0MyA3Ljk5NzkyIDEzLjcxNDNDMTAuODYyNSAxMy43MTQzIDEzLjE4NDcgMTEuMzkyMSAxMy4xODQ3IDguNTI3NDdDMTMuMTg0NyA2LjcyNDQ2IDEyLjI2NTcgNS4xMzU2IDEwLjg2NTIgNC4yMDQ0NEMxMC4zMzk2IDMuODU0OTcgMTAuMTk2OCAzLjE0NTU4IDEwLjU0NjMgMi42MTk5OEMxMC44OTU4IDIuMDk0MzcgMTEuNjA1MiAxLjk1MTU4IDEyLjEzMDggMi4zMDEwNUMxNC4xNDE0IDMuNjM3ODYgMTUuNDcwNCA1LjkyNzIxIDE1LjQ3MDQgOC41Mjc0N0MxNS40NzA0IDEyLjY1NDQgMTIuMTI0OSAxNiA3Ljk5NzkyIDE2QzMuODcwOTUgMTYgMC41MjUzOTEgMTIuNjU0NCAwLjUyNTM5MSA4LjUyNzQ3QzAuNTI1MzkxIDUuOTI3MjEgMS44NTQ0OCAzLjYzNzg2IDMuODY1MDYgMi4zMDEwNUM0LjM5MDY2IDEuOTUxNTggNS4xMDAwNSAyLjA5NDM3IDUuNDQ5NTIgMi42MTk5OFpcIiBmaWxsPVwiY3VycmVudENvbG9yXCIvPlxuPC9zdmc+XG5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b2ZmLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js\n"));

/***/ })

}]);