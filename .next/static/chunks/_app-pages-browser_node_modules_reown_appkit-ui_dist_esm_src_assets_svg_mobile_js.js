"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_mobile_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mobileSvg: function() { return /* binding */ mobileSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst mobileSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path fill=\"currentColor\" d=\"M10.81 5.81a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z\" />\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M3 4.75A4.75 4.75 0 0 1 7.75 0h4.5A4.75 4.75 0 0 1 17 4.75v10.5A4.75 4.75 0 0 1 12.25 20h-4.5A4.75 4.75 0 0 1 3 15.25V4.75ZM7.75 2A2.75 2.75 0 0 0 5 4.75v10.5A2.75 2.75 0 0 0 7.75 18h4.5A2.75 2.75 0 0 0 15 15.25V4.75A2.75 2.75 0 0 0 12.25 2h-4.5Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=mobile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL21vYmlsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixrQkFBa0Isd0NBQUc7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL21vYmlsZS5qcz8xMGMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgbW9iaWxlU3ZnID0gc3ZnIGA8c3ZnIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICA8cGF0aCBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk0xMC44MSA1LjgxYTIgMiAwIDEgMS00IDAgMiAyIDAgMCAxIDQgMFpcIiAvPlxuICA8cGF0aFxuICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgIGZpbGwtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGQ9XCJNMyA0Ljc1QTQuNzUgNC43NSAwIDAgMSA3Ljc1IDBoNC41QTQuNzUgNC43NSAwIDAgMSAxNyA0Ljc1djEwLjVBNC43NSA0Ljc1IDAgMCAxIDEyLjI1IDIwaC00LjVBNC43NSA0Ljc1IDAgMCAxIDMgMTUuMjVWNC43NVpNNy43NSAyQTIuNzUgMi43NSAwIDAgMCA1IDQuNzV2MTAuNUEyLjc1IDIuNzUgMCAwIDAgNy43NSAxOGg0LjVBMi43NSAyLjc1IDAgMCAwIDE1IDE1LjI1VjQuNzVBMi43NSAyLjc1IDAgMCAwIDEyLjI1IDJoLTQuNVpcIlxuICAgIGNsaXAtcnVsZT1cImV2ZW5vZGRcIlxuICAvPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1vYmlsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js\n"));

/***/ })

}]);