"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-9d40a9"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js ***!
  \**********************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clockSvg: function() { return /* binding */ clockSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst clockSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\">\n  <path \n    fill-rule=\"evenodd\" \n    clip-rule=\"evenodd\" \n    d=\"M7.00235 2C4.24 2 2.00067 4.23858 2.00067 7C2.00067 9.76142 4.24 12 7.00235 12C9.7647 12 12.004 9.76142 12.004 7C12.004 4.23858 9.7647 2 7.00235 2ZM0 7C0 3.13401 3.13506 0 7.00235 0C10.8696 0 14.0047 3.13401 14.0047 7C14.0047 10.866 10.8696 14 7.00235 14C3.13506 14 0 10.866 0 7ZM7.00235 3C7.55482 3 8.00269 3.44771 8.00269 4V6.58579L9.85327 8.43575C10.2439 8.82627 10.2439 9.45944 9.85327 9.84996C9.46262 10.2405 8.82924 10.2405 8.43858 9.84996L6.29501 7.70711C6.10741 7.51957 6.00201 7.26522 6.00201 7V4C6.00201 3.44771 6.44988 3 7.00235 3Z\" \n    fill=\"currentColor\"\n  />\n</svg>`;\n//# sourceMappingURL=clock.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9jbG9jay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixpQkFBaUIsd0NBQUc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHdhbGxldGNvbm5lY3QvZXRoZXJldW0tcHJvdmlkZXIvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvY2xvY2suanM/NmVjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IGNsb2NrU3ZnID0gc3ZnIGA8c3ZnIHdpZHRoPVwiMTRcIiBoZWlnaHQ9XCIxNFwiIHZpZXdCb3g9XCIwIDAgMTQgMTRcIiBmaWxsPVwibm9uZVwiPlxuICA8cGF0aCBcbiAgICBmaWxsLXJ1bGU9XCJldmVub2RkXCIgXG4gICAgY2xpcC1ydWxlPVwiZXZlbm9kZFwiIFxuICAgIGQ9XCJNNy4wMDIzNSAyQzQuMjQgMiAyLjAwMDY3IDQuMjM4NTggMi4wMDA2NyA3QzIuMDAwNjcgOS43NjE0MiA0LjI0IDEyIDcuMDAyMzUgMTJDOS43NjQ3IDEyIDEyLjAwNCA5Ljc2MTQyIDEyLjAwNCA3QzEyLjAwNCA0LjIzODU4IDkuNzY0NyAyIDcuMDAyMzUgMlpNMCA3QzAgMy4xMzQwMSAzLjEzNTA2IDAgNy4wMDIzNSAwQzEwLjg2OTYgMCAxNC4wMDQ3IDMuMTM0MDEgMTQuMDA0NyA3QzE0LjAwNDcgMTAuODY2IDEwLjg2OTYgMTQgNy4wMDIzNSAxNEMzLjEzNTA2IDE0IDAgMTAuODY2IDAgN1pNNy4wMDIzNSAzQzcuNTU0ODIgMyA4LjAwMjY5IDMuNDQ3NzEgOC4wMDI2OSA0VjYuNTg1NzlMOS44NTMyNyA4LjQzNTc1QzEwLjI0MzkgOC44MjYyNyAxMC4yNDM5IDkuNDU5NDQgOS44NTMyNyA5Ljg0OTk2QzkuNDYyNjIgMTAuMjQwNSA4LjgyOTI0IDEwLjI0MDUgOC40Mzg1OCA5Ljg0OTk2TDYuMjk1MDEgNy43MDcxMUM2LjEwNzQxIDcuNTE5NTcgNi4wMDIwMSA3LjI2NTIyIDYuMDAyMDEgN1Y0QzYuMDAyMDEgMy40NDc3MSA2LjQ0OTg4IDMgNy4wMDIzNSAzWlwiIFxuICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAvPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNsb2NrLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js\n"));

/***/ })

}]);