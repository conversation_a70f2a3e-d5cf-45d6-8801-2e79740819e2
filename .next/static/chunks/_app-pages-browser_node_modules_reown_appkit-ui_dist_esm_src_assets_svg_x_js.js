"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_x_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js":
/*!********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xSvg: function() { return /* binding */ xSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@reown/appkit-ui/node_modules/lit/index.js\");\n\nconst xSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 41 40\">\n  <g clip-path=\"url(#a)\">\n    <path fill=\"#000\" d=\"M.8 0h40v40H.8z\" />\n    <path\n      fill=\"#fff\"\n      d=\"m22.63 18.46 7.14-8.3h-1.69l-6.2 7.2-4.96-7.2H11.2l7.5 10.9-7.5 8.71h1.7l6.55-7.61 5.23 7.61h5.72l-7.77-11.31Zm-9.13-7.03h2.6l11.98 17.13h-2.6L13.5 11.43Z\"\n    />\n  </g>\n  <defs>\n    <clipPath id=\"a\"><path fill=\"#fff\" d=\"M.8 20a20 20 0 1 1 40 0 20 20 0 0 1-40 0Z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=x.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsYUFBYSx3Q0FBRztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcveC5qcz8xYTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgeFN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgNDEgNDBcIj5cbiAgPGcgY2xpcC1wYXRoPVwidXJsKCNhKVwiPlxuICAgIDxwYXRoIGZpbGw9XCIjMDAwXCIgZD1cIk0uOCAwaDQwdjQwSC44elwiIC8+XG4gICAgPHBhdGhcbiAgICAgIGZpbGw9XCIjZmZmXCJcbiAgICAgIGQ9XCJtMjIuNjMgMTguNDYgNy4xNC04LjNoLTEuNjlsLTYuMiA3LjItNC45Ni03LjJIMTEuMmw3LjUgMTAuOS03LjUgOC43MWgxLjdsNi41NS03LjYxIDUuMjMgNy42MWg1LjcybC03Ljc3LTExLjMxWm0tOS4xMy03LjAzaDIuNmwxMS45OCAxNy4xM2gtMi42TDEzLjUgMTEuNDNaXCJcbiAgICAvPlxuICA8L2c+XG4gIDxkZWZzPlxuICAgIDxjbGlwUGF0aCBpZD1cImFcIj48cGF0aCBmaWxsPVwiI2ZmZlwiIGQ9XCJNLjggMjBhMjAgMjAgMCAxIDEgNDAgMCAyMCAyMCAwIDAgMS00MCAwWlwiIC8+PC9jbGlwUGF0aD5cbiAgPC9kZWZzPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js\n"));

/***/ })

}]);