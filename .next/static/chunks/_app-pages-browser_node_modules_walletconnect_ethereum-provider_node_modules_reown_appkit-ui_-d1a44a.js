"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_ethereum-provider_node_modules_reown_appkit-ui_-d1a44a"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js ***!
  \*********************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plusSvg: function() { return /* binding */ plusSvg; }\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/lit/index.js\");\n\nconst plusSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"13\"\n  height=\"12\"\n  viewBox=\"0 0 13 12\"\n  fill=\"none\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M0.794373 5.99982C0.794373 5.52643 1.17812 5.14268 1.6515 5.14268H5.643V1.15109C5.643 0.677701 6.02675 0.293946 6.50012 0.293945C6.9735 0.293946 7.35725 0.677701 7.35725 1.15109V5.14268H11.3488C11.8221 5.14268 12.2059 5.52643 12.2059 5.99982C12.2059 6.47321 11.8221 6.85696 11.3488 6.85696H7.35725V10.8486C7.35725 11.3219 6.9735 11.7057 6.50012 11.7057C6.02675 11.7057 5.643 11.3219 5.643 10.8486V6.85696H1.6515C1.17812 6.85696 0.794373 6.47321 0.794373 5.99982Z\"\n  /></svg\n>`;\n//# sourceMappingURL=plus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC9ldGhlcmV1bS1wcm92aWRlci9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9wbHVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBQ25CLGdCQUFnQix3Q0FBRztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHdhbGxldGNvbm5lY3QvZXRoZXJldW0tcHJvdmlkZXIvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvcGx1cy5qcz8zMTgxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgcGx1c1N2ZyA9IHN2ZyBgPHN2Z1xuICB3aWR0aD1cIjEzXCJcbiAgaGVpZ2h0PVwiMTJcIlxuICB2aWV3Qm94PVwiMCAwIDEzIDEyXCJcbiAgZmlsbD1cIm5vbmVcIlxuPlxuICA8cGF0aFxuICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgIGZpbGwtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGNsaXAtcnVsZT1cImV2ZW5vZGRcIlxuICAgIGQ9XCJNMC43OTQzNzMgNS45OTk4MkMwLjc5NDM3MyA1LjUyNjQzIDEuMTc4MTIgNS4xNDI2OCAxLjY1MTUgNS4xNDI2OEg1LjY0M1YxLjE1MTA5QzUuNjQzIDAuNjc3NzAxIDYuMDI2NzUgMC4yOTM5NDYgNi41MDAxMiAwLjI5Mzk0NUM2Ljk3MzUgMC4yOTM5NDYgNy4zNTcyNSAwLjY3NzcwMSA3LjM1NzI1IDEuMTUxMDlWNS4xNDI2OEgxMS4zNDg4QzExLjgyMjEgNS4xNDI2OCAxMi4yMDU5IDUuNTI2NDMgMTIuMjA1OSA1Ljk5OTgyQzEyLjIwNTkgNi40NzMyMSAxMS44MjIxIDYuODU2OTYgMTEuMzQ4OCA2Ljg1Njk2SDcuMzU3MjVWMTAuODQ4NkM3LjM1NzI1IDExLjMyMTkgNi45NzM1IDExLjcwNTcgNi41MDAxMiAxMS43MDU3QzYuMDI2NzUgMTEuNzA1NyA1LjY0MyAxMS4zMjE5IDUuNjQzIDEwLjg0ODZWNi44NTY5NkgxLjY1MTVDMS4xNzgxMiA2Ljg1Njk2IDAuNzk0MzczIDYuNDczMjEgMC43OTQzNzMgNS45OTk4MlpcIlxuICAvPjwvc3ZnXG4+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBsdXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js\n"));

/***/ })

}]);