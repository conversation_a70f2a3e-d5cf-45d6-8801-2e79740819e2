/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/CustomWalletModal.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* Modern Dribbble-Style Wallet Modal - Clean White Design */
.CustomWalletModal_walletModalOverlay__agwZZ {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  animation: CustomWalletModal_fadeIn__HzI8j 0.3s ease-out;
}

.CustomWalletModal_walletModal__vgw9b {
  background: #ffffff;
  border-radius: 24px;
  width: 100%;
  max-width: 420px;
  margin: 20px;
  box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.08);
  animation: CustomWalletModal_slideUp__ydgXn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;
}

.CustomWalletModal_walletModalHeader__VsNQF {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid #f1f5f9;
}

.CustomWalletModal_walletModalHeader__VsNQF h2 {
  color: #0f172a;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.CustomWalletModal_closeButton__21x5M {
  background: #f8fafc;
  border: none;
  color: #64748b;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CustomWalletModal_closeButton__21x5M:hover {
  background: #e2e8f0;
  color: #334155;
}

.CustomWalletModal_walletOptions__gb91B {
  padding: 0 32px 32px 32px;
}

.CustomWalletModal_walletOption__9IUPG {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  border: 2px solid #f1f5f9;
  position: relative;
}

.CustomWalletModal_walletOption__9IUPG:hover:not(.CustomWalletModal_disabled__F47x5) {
  background: #f8fafc;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.CustomWalletModal_walletOption__9IUPG.CustomWalletModal_disabled__F47x5 {
  opacity: 0.4;
  cursor: not-allowed;
  background: #f8fafc;
}

.CustomWalletModal_walletInfo___TPCm {
  display: flex;
  align-items: center;
  gap: 16px;
}

.CustomWalletModal_walletIcon___c5rz {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #e2e8f0;
  font-size: 24px;
  position: relative;
}

.CustomWalletModal_walletName__Prh_u {
  color: #0f172a;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.CustomWalletModal_walletDescription__0sV2y {
  color: #64748b;
  font-size: 14px;
  font-weight: 400;
  margin-top: 2px;
}

.CustomWalletModal_walletTag__DSYgI {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: absolute;
  top: 16px;
  right: 16px;
}

.CustomWalletModal_connectedState___8MtW {
  padding: 32px;
  text-align: center;
}

.CustomWalletModal_connectedInfo__oOPza {
  background: #f8fafc;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.CustomWalletModal_connectionStatus__HfPdh {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #10b981;
  font-weight: 600;
  font-size: 14px;
}

.CustomWalletModal_statusDot__ZKs36 {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: CustomWalletModal_pulse__kTrQH 2s infinite;
}

.CustomWalletModal_walletAddress__x_ogC {
  color: #0f172a;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 600;
  background: #ffffff;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.CustomWalletModal_disconnectButton__x1LVy {
  background: #ef4444;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.CustomWalletModal_disconnectButton__x1LVy:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.CustomWalletModal_loadingState__1YznE {
  padding: 32px;
  text-align: center;
  color: #64748b;
}

.CustomWalletModal_spinner__ZTvAQ {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: CustomWalletModal_spin__dnSUH 1s linear infinite;
  margin: 0 auto 16px auto;
}

.CustomWalletModal_modalFooter__4sKc1 {
  padding: 0 32px 32px 32px;
  border-top: 1px solid #f1f5f9;
  margin-top: 8px;
  padding-top: 24px;
}

.CustomWalletModal_termsText__FPUXb {
  color: #64748b;
  font-size: 13px;
  text-align: center;
  margin: 0;
  line-height: 1.5;
}

.CustomWalletModal_termsLink__Ham7H {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
  font-weight: 500;
}

.CustomWalletModal_termsLink__Ham7H:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* Animations */
@keyframes CustomWalletModal_fadeIn__HzI8j {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes CustomWalletModal_slideUp__ydgXn {
  from {
    opacity: 0;
    transform: translateY(32px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes CustomWalletModal_spin__dnSUH {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes CustomWalletModal_pulse__kTrQH {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .CustomWalletModal_walletModal__vgw9b {
    margin: 10px;
    max-width: none;
  }
  
  .CustomWalletModal_walletModalHeader__VsNQF {
    padding: 20px 20px 12px 20px;
  }
  
  .CustomWalletModal_walletOptions__gb91B {
    padding: 12px 20px 20px 20px;
  }
  
  .CustomWalletModal_walletOption__9IUPG {
    padding: 14px;
  }
  
  .CustomWalletModal_walletName__Prh_u {
    font-size: 15px;
  }
  
  .CustomWalletModal_walletTag__DSYgI {
    font-size: 10px;
    padding: 3px 6px;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/PaymentModal.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/* Payment Modal Styles */
.PaymentModal_overlay__hYE9X {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: PaymentModal_fadeIn__Erzv1 0.3s ease-out;
}

.PaymentModal_modal__tiAWD {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 24px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: PaymentModal_slideUp__ZFKP7 0.4s ease-out;
}

.PaymentModal_header__A_P5v {
  text-align: center;
  margin-bottom: 32px;
}

.PaymentModal_header__A_P5v h2 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.PaymentModal_header__A_P5v p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.PaymentModal_content__u3win {
  text-align: center;
}

.PaymentModal_paymentInfo__GuuUM {
  margin-bottom: 24px;
}

.PaymentModal_amountDisplay__QKt95 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  color: white;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.PaymentModal_amount__vDaMn {
  display: block;
  font-size: 48px;
  font-weight: 900;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.PaymentModal_currency__0hNXT {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 600;
}

.PaymentModal_benefits__EOwzO {
  text-align: left;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.PaymentModal_benefits__EOwzO h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.PaymentModal_benefits__EOwzO ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.PaymentModal_benefits__EOwzO li {
  padding: 8px 0;
  font-size: 15px;
  color: #475569;
  font-weight: 500;
}

.PaymentModal_actions__PC_fo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.PaymentModal_payBtn__IB1fD {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.PaymentModal_payBtn__IB1fD.PaymentModal_primary__egBwr {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.PaymentModal_payBtn__IB1fD.PaymentModal_primary__egBwr:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.PaymentModal_payBtn__IB1fD.PaymentModal_primary__egBwr:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.PaymentModal_payBtn__IB1fD.PaymentModal_secondary__muG9E {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
  border: 2px solid rgba(100, 116, 139, 0.2);
}

.PaymentModal_payBtn__IB1fD.PaymentModal_secondary__muG9E:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: translateY(-1px);
}

.PaymentModal_icon__Cdb9R {
  font-size: 20px;
}

.PaymentModal_statusDisplay__9CSsj {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
}

.PaymentModal_spinner__67rhm {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.2);
  border-left: 4px solid #3b82f6;
  border-radius: 50%;
  animation: PaymentModal_spin__qXEXW 1s linear infinite;
}

.PaymentModal_successIcon__oEOn_ {
  font-size: 48px;
  color: #10b981;
}

.PaymentModal_errorIcon__0dl9t {
  font-size: 48px;
  color: #ef4444;
}

.PaymentModal_statusDisplay__9CSsj p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.PaymentModal_closeBtn__w1HlK {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(100, 116, 139, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.PaymentModal_closeBtn__w1HlK:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(1.1);
}

/* Animations */
@keyframes PaymentModal_fadeIn__Erzv1 {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes PaymentModal_slideUp__ZFKP7 {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes PaymentModal_spin__qXEXW {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .PaymentModal_modal__tiAWD {
    padding: 24px;
    margin: 20px;
  }
  
  .PaymentModal_amount__vDaMn {
    font-size: 36px;
  }
  
  .PaymentModal_payBtn__IB1fD {
    padding: 16px 24px;
    font-size: 16px;
  }
  
  .PaymentModal_benefits__EOwzO {
    padding: 20px;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/EthereumVerificationModal.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/* Ethereum Verification Modal Styles */
.EthereumVerificationModal_overlay__1SCfN {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: EthereumVerificationModal_fadeIn__krKp_ 0.3s ease-out;
}

.EthereumVerificationModal_modal__vPfzY {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 24px;
  padding: 40px;
  max-width: 550px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: EthereumVerificationModal_slideUp__plUWY 0.4s ease-out;
}

.EthereumVerificationModal_header__g9ues {
  text-align: center;
  margin-bottom: 32px;
}

.EthereumVerificationModal_header__g9ues h2 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.EthereumVerificationModal_header__g9ues p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.EthereumVerificationModal_content__J1_wn {
  text-align: center;
}

.EthereumVerificationModal_requirementInfo__j073g {
  margin-bottom: 32px;
}

.EthereumVerificationModal_requirementCard__8GUvj {
  display: flex;
  align-items: center;
  gap: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20px;
  padding: 24px;
  color: white;
  text-align: left;
}

.EthereumVerificationModal_requirementIcon__4WQvq {
  font-size: 32px;
  flex-shrink: 0;
}

.EthereumVerificationModal_requirementText__nxKl0 h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.EthereumVerificationModal_requirementText__nxKl0 p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

.EthereumVerificationModal_walletInfo__MhHh8 {
  margin-bottom: 24px;
}

.EthereumVerificationModal_walletDisplay__r8zla {
  background: rgba(99, 102, 241, 0.1);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.EthereumVerificationModal_walletLabel__wx75a {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
}

.EthereumVerificationModal_walletAddress___jRN9 {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 700;
  color: #6366f1;
}

.EthereumVerificationModal_statusDisplay__A_m0t {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  margin-bottom: 24px;
}

.EthereumVerificationModal_spinner__CWdYj {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(99, 102, 241, 0.2);
  border-left: 4px solid #6366f1;
  border-radius: 50%;
  animation: EthereumVerificationModal_spin__wzGYI 1s linear infinite;
}

.EthereumVerificationModal_successIcon__ytU3G {
  font-size: 48px;
  color: #10b981;
}

.EthereumVerificationModal_warningIcon__YvlAQ {
  font-size: 48px;
  color: #f59e0b;
}

.EthereumVerificationModal_errorIcon__tkzol {
  font-size: 48px;
  color: #ef4444;
}

.EthereumVerificationModal_statusDisplay__A_m0t p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.EthereumVerificationModal_subText__t_u_t {
  font-size: 14px !important;
  color: #64748b !important;
  font-weight: 400 !important;
}

.EthereumVerificationModal_errorText__a4knO {
  font-size: 14px !important;
  color: #ef4444 !important;
  font-weight: 400 !important;
}

.EthereumVerificationModal_transactionInfo___aGxW {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin: 24px 0;
}

.EthereumVerificationModal_txCount__Y_v2D,
.EthereumVerificationModal_txRequirement__MpGtG {
  text-align: center;
}

.EthereumVerificationModal_txNumber__zkA4t,
.EthereumVerificationModal_reqNumber__g_za1 {
  display: block;
  font-size: 32px;
  font-weight: 900;
  color: #6366f1;
}

.EthereumVerificationModal_txLabel__UTTUx,
.EthereumVerificationModal_reqLabel__kjapQ {
  display: block;
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.EthereumVerificationModal_detailsBtn__AiIcJ {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.EthereumVerificationModal_detailsBtn__AiIcJ:hover {
  background: rgba(99, 102, 241, 0.2);
}

.EthereumVerificationModal_details__McF3X {
  background: rgba(99, 102, 241, 0.05);
  border-radius: 16px;
  padding: 20px;
  margin-top: 16px;
  text-align: left;
}

.EthereumVerificationModal_details__McF3X h4 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.EthereumVerificationModal_detailRow__ZIFhb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
  font-size: 14px;
}

.EthereumVerificationModal_detailRow__ZIFhb span:first-child {
  font-weight: 600;
  color: #64748b;
}

.EthereumVerificationModal_detailRow__ZIFhb span:last-child {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1e293b;
}

.EthereumVerificationModal_eligible__zwObi {
  color: #10b981 !important;
  font-weight: 700 !important;
}

.EthereumVerificationModal_notEligible__tbZuh {
  color: #ef4444 !important;
  font-weight: 700 !important;
}

.EthereumVerificationModal_actions___Ub72 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.EthereumVerificationModal_notEligibleActions__1SZOG {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.EthereumVerificationModal_helpText__Nb0So {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  padding: 16px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.EthereumVerificationModal_actionBtn__A7IK_ {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 16px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  width: 100%;
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_primary___sHSu {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_primary___sHSu:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_secondary__Og2lQ {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 2px solid rgba(99, 102, 241, 0.2);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_secondary__Og2lQ:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_demo__fXXFe {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 2px solid rgba(245, 158, 11, 0.2);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_demo__fXXFe:hover {
  background: rgba(245, 158, 11, 0.2);
  transform: translateY(-1px);
}

.EthereumVerificationModal_icon__s43s5 {
  font-size: 18px;
}

.EthereumVerificationModal_closeBtn__8N_4V {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(100, 116, 139, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.EthereumVerificationModal_closeBtn__8N_4V:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(1.1);
}

/* Animations */
@keyframes EthereumVerificationModal_fadeIn__krKp_ {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes EthereumVerificationModal_slideUp__plUWY {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes EthereumVerificationModal_spin__wzGYI {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .EthereumVerificationModal_modal__vPfzY {
    padding: 24px;
    margin: 20px;
  }
  
  .EthereumVerificationModal_requirementCard__8GUvj {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .EthereumVerificationModal_transactionInfo___aGxW {
    gap: 24px;
  }
  
  .EthereumVerificationModal_actionBtn__A7IK_ {
    padding: 16px 24px;
    font-size: 15px;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/ReownWalletButton.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* Reown AppKit Wallet Button Styles */
.ReownWalletButton_connectBtn__ByaZF {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 36px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 280px;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_primary__J1Wy_ {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.3);
  border: 2px solid transparent;
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_primary__J1Wy_:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_primary__J1Wy_:active {
  transform: translateY(-1px);
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_secondary__Wsbop {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_secondary__Wsbop:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.5);
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_warning__zopnO {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: 2px solid rgba(245, 158, 11, 0.3);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_warning__zopnO:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  border-color: rgba(245, 158, 11, 0.5);
}

.ReownWalletButton_icon___NKs9 {
  font-size: 22px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.ReownWalletButton_connectedState__Gtvhc {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24px;
  padding: 32px;
  max-width: 450px;
  margin: 0 auto;
  border: 2px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.ReownWalletButton_walletInfo__Y6Wnb {
  margin-bottom: 24px;
}

.ReownWalletButton_connectionIndicator__xvP1U {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 16px;
  font-weight: 700;
  color: #10b981;
  font-size: 16px;
}

.ReownWalletButton_connectionIndicator__xvP1U.ReownWalletButton_wrongNetwork___rXW1 {
  color: #f59e0b;
}

.ReownWalletButton_statusDot__6ll_u {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  animation: ReownWalletButton_pulse__Khy0d 2s infinite;
  box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
}

.ReownWalletButton_statusDot__6ll_u.ReownWalletButton_warning__zopnO {
  background: #f59e0b;
  animation: ReownWalletButton_pulseWarning__sKLhA 2s infinite;
  box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
}

.ReownWalletButton_walletAddress__IPqt_ {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 12px;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ReownWalletButton_walletNetwork__ubOyY {
  font-size: 14px;
  opacity: 0.9;
  color: #e2e8f0;
  font-weight: 500;
}

/* Pulse animation for status dot */
@keyframes ReownWalletButton_pulse__Khy0d {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes ReownWalletButton_pulseWarning__sKLhA {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ReownWalletButton_connectBtn__ByaZF {
    padding: 16px 28px;
    font-size: 16px;
    min-width: 240px;
  }
  
  .ReownWalletButton_connectedState__Gtvhc {
    padding: 24px;
    margin: 0 10px;
  }
  
  .ReownWalletButton_walletAddress__IPqt_ {
    font-size: 18px;
    padding: 10px 16px;
  }
  
  .ReownWalletButton_connectionIndicator__xvP1U {
    font-size: 14px;
  }
  
  .ReownWalletButton_icon___NKs9 {
    font-size: 20px;
  }
}

/* Loading state */
.ReownWalletButton_connectBtn__ByaZF:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

/* Focus states for accessibility */
.ReownWalletButton_connectBtn__ByaZF:focus {
  outline: none;
  ring: 3px solid rgba(59, 130, 246, 0.5);
}

/* Gradient animation on hover */
.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_primary__J1Wy_::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.ReownWalletButton_connectBtn__ByaZF.ReownWalletButton_primary__J1Wy_:hover::before {
  left: 100%;
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/VotingComponent.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/* Voting Component Styles */
.VotingComponent_votingCard__SCCE_ {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 32px;
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.VotingComponent_header__3sSBH {
  text-align: center;
  margin-bottom: 32px;
}

.VotingComponent_header__3sSBH h3 {
  font-size: 24px;
  font-weight: 800;
  margin: 0 0 8px 0;
  color: white;
}

.VotingComponent_header__3sSBH p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 500;
}

.VotingComponent_connectPrompt__BoJGv,
.VotingComponent_paymentPrompt__oj0pC {
  text-align: center;
  padding: 24px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.VotingComponent_connectPrompt__BoJGv p,
.VotingComponent_paymentPrompt__oj0pC p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.VotingComponent_votingForm__VKaXN {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.VotingComponent_scoreSection__dlbmH,
.VotingComponent_commentSection__Pp2LV {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.VotingComponent_scoreSection__dlbmH label,
.VotingComponent_commentSection__Pp2LV label {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.VotingComponent_scoreInput__K_S7p {
  display: flex;
  align-items: center;
  gap: 16px;
}

.VotingComponent_slider__qou9m {
  flex: 1 1;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
}

.VotingComponent_slider__qou9m::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.VotingComponent_slider__qou9m::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.VotingComponent_scoreValue__Wps6P {
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
}

.VotingComponent_commentInput__6pGQE {
  width: 100%;
  min-height: 100px;
  padding: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.VotingComponent_commentInput__6pGQE::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.VotingComponent_commentInput__6pGQE:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.VotingComponent_commentInput__6pGQE:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.VotingComponent_charCount__dV4qi {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: right;
}

.VotingComponent_feeInfo__y60LU {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.VotingComponent_feeDisplay__OYNQu {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.VotingComponent_feeLabel__lSTXI {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.VotingComponent_feeAmount__WM80m {
  font-size: 20px;
  font-weight: 800;
  color: #10b981;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 12px;
}

.VotingComponent_voteBtn__u2r0J {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.VotingComponent_voteBtn__u2r0J.VotingComponent_primary___CJdS {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.VotingComponent_voteBtn__u2r0J.VotingComponent_primary___CJdS:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.VotingComponent_voteBtn__u2r0J:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.VotingComponent_icon__PbXcx {
  font-size: 20px;
}

.VotingComponent_statusDisplay__OBoh0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  text-align: center;
}

.VotingComponent_spinner__2hq7j {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(16, 185, 129, 0.2);
  border-left: 4px solid #10b981;
  border-radius: 50%;
  animation: VotingComponent_spin__4xmlJ 1s linear infinite;
}

.VotingComponent_successIcon__hZDyZ {
  font-size: 48px;
  color: #10b981;
}

.VotingComponent_errorIcon__RVwQX {
  font-size: 48px;
  color: #ef4444;
}

.VotingComponent_statusDisplay__OBoh0 p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

/* Animations */
@keyframes VotingComponent_spin__4xmlJ {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .VotingComponent_votingCard__SCCE_ {
    padding: 24px;
    margin: 0 10px;
  }
  
  .VotingComponent_header__3sSBH h3 {
    font-size: 20px;
  }
  
  .VotingComponent_voteBtn__u2r0J {
    padding: 16px 24px;
    font-size: 16px;
  }
  
  .VotingComponent_scoreInput__K_S7p {
    flex-direction: column;
    gap: 12px;
  }
  
  .VotingComponent_feeDisplay__OYNQu {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/DebugPanel.module.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/* Debug Panel Styles */
.DebugPanel_debugPanel__BaT3w {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 16px;
  border-radius: 12px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  max-width: 300px;
  z-index: 1000;
}

.DebugPanel_debugPanel__BaT3w h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #3b82f6;
}

.DebugPanel_debugInfo__ZwRsM {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.DebugPanel_debugRow__Qn1l_ {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.DebugPanel_debugRow__Qn1l_ span:first-child {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.DebugPanel_success__e4oiE {
  color: #10b981;
}

.DebugPanel_error__uagFB {
  color: #ef4444;
}

.DebugPanel_loading__8A9Mp {
  color: #f59e0b;
}

.DebugPanel_debugActions__JuTGU {
  margin-bottom: 16px;
}

.DebugPanel_resetBtn__SudXW {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.DebugPanel_resetBtn__SudXW:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.DebugPanel_contractInfo__KBG78 {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.DebugPanel_contractInfo__KBG78 p {
  margin: 4px 0;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

/* Hide on mobile */
@media (max-width: 768px) {
  .DebugPanel_debugPanel__BaT3w {
    display: none;
  }
}

