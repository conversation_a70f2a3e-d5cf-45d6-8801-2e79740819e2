/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* Global Styles for Next.js App */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

a {
  color: inherit;
  text-decoration: none;
}

/* App Container */
.app {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
}

/* Header Styles */
.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 3.5rem;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Fee Info */
.fee-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fee-info h3 {
  color: #ff6b6b;
  margin-bottom: 16px;
  font-size: 1.4rem;
}

.fee-info ul {
  list-style: none;
}

.fee-info li {
  margin-bottom: 8px;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Wallet Section */
.wallet-section {
  text-align: center;
  margin: 50px 0;
}

/* Modern Gradient Buttons */
.gradient-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.gradient-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
}

.gradient-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(59, 130, 246, 0.35);
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.gradient-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.gradient-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Connected Wallet */
.connected-wallet {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 30px;
  max-width: 400px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.wallet-info {
  margin-bottom: 20px;
}

.connection-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #4ade80;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse 2s infinite;
}

.wallet-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.wallet-network {
  font-size: 14px;
  opacity: 0.8;
}

/* Features Grid */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: 30px;
  gap: 30px;
  margin: 60px 0;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 1.4rem;
  margin-bottom: 12px;
  color: #4ecdc4;
}

.feature-card p {
  opacity: 0.9;
  line-height: 1.6;
}

/* Stats Grid */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  grid-gap: 30px;
  gap: 30px;
  margin-top: 60px;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ff6b6b;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.8;
}

.icon {
  font-size: 20px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 20px;
    margin: 10px;
  }
  
  .header h1 {
    font-size: 2.5rem;
  }
  
  .features {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .gradient-btn {
    padding: 16px 28px;
    font-size: 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/PaymentModal.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/* Payment Modal Styles */
.PaymentModal_overlay__hYE9X {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: PaymentModal_fadeIn__Erzv1 0.3s ease-out;
}

.PaymentModal_modal__tiAWD {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 24px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: PaymentModal_slideUp__ZFKP7 0.4s ease-out;
}

.PaymentModal_header__A_P5v {
  text-align: center;
  margin-bottom: 32px;
}

.PaymentModal_header__A_P5v h2 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.PaymentModal_header__A_P5v p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.PaymentModal_content__u3win {
  text-align: center;
}

.PaymentModal_paymentInfo__GuuUM {
  margin-bottom: 24px;
}

.PaymentModal_amountDisplay__QKt95 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  color: white;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.PaymentModal_amount__vDaMn {
  display: block;
  font-size: 48px;
  font-weight: 900;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.PaymentModal_currency__0hNXT {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 600;
}

.PaymentModal_benefits__EOwzO {
  text-align: left;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.PaymentModal_benefits__EOwzO h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.PaymentModal_benefits__EOwzO ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.PaymentModal_benefits__EOwzO li {
  padding: 8px 0;
  font-size: 15px;
  color: #475569;
  font-weight: 500;
}

.PaymentModal_actions__PC_fo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.PaymentModal_payBtn__IB1fD {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.PaymentModal_payBtn__IB1fD.PaymentModal_primary__egBwr {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.PaymentModal_payBtn__IB1fD.PaymentModal_primary__egBwr:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.PaymentModal_payBtn__IB1fD.PaymentModal_primary__egBwr:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.PaymentModal_payBtn__IB1fD.PaymentModal_secondary__muG9E {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
  border: 2px solid rgba(100, 116, 139, 0.2);
}

.PaymentModal_payBtn__IB1fD.PaymentModal_secondary__muG9E:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: translateY(-1px);
}

.PaymentModal_icon__Cdb9R {
  font-size: 20px;
}

.PaymentModal_statusDisplay__9CSsj {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
}

.PaymentModal_spinner__67rhm {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.2);
  border-left: 4px solid #3b82f6;
  border-radius: 50%;
  animation: PaymentModal_spin__qXEXW 1s linear infinite;
}

.PaymentModal_successIcon__oEOn_ {
  font-size: 48px;
  color: #10b981;
}

.PaymentModal_errorIcon__0dl9t {
  font-size: 48px;
  color: #ef4444;
}

.PaymentModal_statusDisplay__9CSsj p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.PaymentModal_closeBtn__w1HlK {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(100, 116, 139, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.PaymentModal_closeBtn__w1HlK:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(1.1);
}

/* Animations */
@keyframes PaymentModal_fadeIn__Erzv1 {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes PaymentModal_slideUp__ZFKP7 {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes PaymentModal_spin__qXEXW {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .PaymentModal_modal__tiAWD {
    padding: 24px;
    margin: 20px;
  }
  
  .PaymentModal_amount__vDaMn {
    font-size: 36px;
  }
  
  .PaymentModal_payBtn__IB1fD {
    padding: 16px 24px;
    font-size: 16px;
  }
  
  .PaymentModal_benefits__EOwzO {
    padding: 20px;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/EthereumVerificationModal.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/* Ethereum Verification Modal Styles */
.EthereumVerificationModal_overlay__1SCfN {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: EthereumVerificationModal_fadeIn__krKp_ 0.3s ease-out;
}

.EthereumVerificationModal_modal__vPfzY {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 24px;
  padding: 40px;
  max-width: 550px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: EthereumVerificationModal_slideUp__plUWY 0.4s ease-out;
}

.EthereumVerificationModal_header__g9ues {
  text-align: center;
  margin-bottom: 32px;
}

.EthereumVerificationModal_header__g9ues h2 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.EthereumVerificationModal_header__g9ues p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.EthereumVerificationModal_content__J1_wn {
  text-align: center;
}

.EthereumVerificationModal_requirementInfo__j073g {
  margin-bottom: 32px;
}

.EthereumVerificationModal_requirementCard__8GUvj {
  display: flex;
  align-items: center;
  gap: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20px;
  padding: 24px;
  color: white;
  text-align: left;
}

.EthereumVerificationModal_requirementIcon__4WQvq {
  font-size: 32px;
  flex-shrink: 0;
}

.EthereumVerificationModal_requirementText__nxKl0 h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.EthereumVerificationModal_requirementText__nxKl0 p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

.EthereumVerificationModal_walletInfo__MhHh8 {
  margin-bottom: 24px;
}

.EthereumVerificationModal_walletDisplay__r8zla {
  background: rgba(99, 102, 241, 0.1);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.EthereumVerificationModal_walletLabel__wx75a {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
}

.EthereumVerificationModal_walletAddress___jRN9 {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 700;
  color: #6366f1;
}

.EthereumVerificationModal_statusDisplay__A_m0t {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  margin-bottom: 24px;
}

.EthereumVerificationModal_spinner__CWdYj {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(99, 102, 241, 0.2);
  border-left: 4px solid #6366f1;
  border-radius: 50%;
  animation: EthereumVerificationModal_spin__wzGYI 1s linear infinite;
}

.EthereumVerificationModal_successIcon__ytU3G {
  font-size: 48px;
  color: #10b981;
}

.EthereumVerificationModal_warningIcon__YvlAQ {
  font-size: 48px;
  color: #f59e0b;
}

.EthereumVerificationModal_errorIcon__tkzol {
  font-size: 48px;
  color: #ef4444;
}

.EthereumVerificationModal_statusDisplay__A_m0t p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.EthereumVerificationModal_subText__t_u_t {
  font-size: 14px !important;
  color: #64748b !important;
  font-weight: 400 !important;
}

.EthereumVerificationModal_errorText__a4knO {
  font-size: 14px !important;
  color: #ef4444 !important;
  font-weight: 400 !important;
}

.EthereumVerificationModal_transactionInfo___aGxW {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin: 24px 0;
}

.EthereumVerificationModal_txCount__Y_v2D,
.EthereumVerificationModal_txRequirement__MpGtG {
  text-align: center;
}

.EthereumVerificationModal_txNumber__zkA4t,
.EthereumVerificationModal_reqNumber__g_za1 {
  display: block;
  font-size: 32px;
  font-weight: 900;
  color: #6366f1;
}

.EthereumVerificationModal_txLabel__UTTUx,
.EthereumVerificationModal_reqLabel__kjapQ {
  display: block;
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.EthereumVerificationModal_detailsBtn__AiIcJ {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.EthereumVerificationModal_detailsBtn__AiIcJ:hover {
  background: rgba(99, 102, 241, 0.2);
}

.EthereumVerificationModal_details__McF3X {
  background: rgba(99, 102, 241, 0.05);
  border-radius: 16px;
  padding: 20px;
  margin-top: 16px;
  text-align: left;
}

.EthereumVerificationModal_details__McF3X h4 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.EthereumVerificationModal_detailRow__ZIFhb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
  font-size: 14px;
}

.EthereumVerificationModal_detailRow__ZIFhb span:first-child {
  font-weight: 600;
  color: #64748b;
}

.EthereumVerificationModal_detailRow__ZIFhb span:last-child {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1e293b;
}

.EthereumVerificationModal_eligible__zwObi {
  color: #10b981 !important;
  font-weight: 700 !important;
}

.EthereumVerificationModal_notEligible__tbZuh {
  color: #ef4444 !important;
  font-weight: 700 !important;
}

.EthereumVerificationModal_actions___Ub72 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.EthereumVerificationModal_notEligibleActions__1SZOG {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.EthereumVerificationModal_helpText__Nb0So {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  padding: 16px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.EthereumVerificationModal_actionBtn__A7IK_ {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 32px;
  font-size: 16px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-decoration: none;
  width: 100%;
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_primary___sHSu {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_primary___sHSu:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_secondary__Og2lQ {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 2px solid rgba(99, 102, 241, 0.2);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_secondary__Og2lQ:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_demo__fXXFe {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 2px solid rgba(245, 158, 11, 0.2);
}

.EthereumVerificationModal_actionBtn__A7IK_.EthereumVerificationModal_demo__fXXFe:hover {
  background: rgba(245, 158, 11, 0.2);
  transform: translateY(-1px);
}

.EthereumVerificationModal_icon__s43s5 {
  font-size: 18px;
}

.EthereumVerificationModal_closeBtn__8N_4V {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(100, 116, 139, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.EthereumVerificationModal_closeBtn__8N_4V:hover {
  background: rgba(100, 116, 139, 0.2);
  transform: scale(1.1);
}

/* Animations */
@keyframes EthereumVerificationModal_fadeIn__krKp_ {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes EthereumVerificationModal_slideUp__plUWY {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes EthereumVerificationModal_spin__wzGYI {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .EthereumVerificationModal_modal__vPfzY {
    padding: 24px;
    margin: 20px;
  }
  
  .EthereumVerificationModal_requirementCard__8GUvj {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .EthereumVerificationModal_transactionInfo___aGxW {
    gap: 24px;
  }
  
  .EthereumVerificationModal_actionBtn__A7IK_ {
    padding: 16px 24px;
    font-size: 15px;
  }
}

/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/components/SiteGatekeeper.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/* Site Gatekeeper Styles */
.SiteGatekeeper_gatekeeper__73y3O {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.SiteGatekeeper_gatekeeper__73y3O::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  animation: SiteGatekeeper_float__toIdQ 20s ease-in-out infinite;
}

.SiteGatekeeper_gatekeeperContent__AagSx {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 48px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 32px 80px rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

.SiteGatekeeper_logo__EYRp8 {
  text-align: center;
  margin-bottom: 40px;
}

.SiteGatekeeper_logo__EYRp8 h1 {
  font-size: 48px;
  font-weight: 900;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.SiteGatekeeper_logo__EYRp8 p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.SiteGatekeeper_accessCard__h_FR5 {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  margin-bottom: 32px;
}

.SiteGatekeeper_accessHeader__GZI_M {
  text-align: center;
  margin-bottom: 32px;
}

.SiteGatekeeper_accessIcon__Br_Jc {
  font-size: 64px;
  margin-bottom: 16px;
}

.SiteGatekeeper_accessHeader__GZI_M h2 {
  font-size: 24px;
  font-weight: 800;
  margin: 0 0 8px 0;
  color: #1e293b;
}

.SiteGatekeeper_accessHeader__GZI_M p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.SiteGatekeeper_loadingState__clFLi {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
}

.SiteGatekeeper_spinner__hMIQW {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-left: 4px solid #667eea;
  border-radius: 50%;
  animation: SiteGatekeeper_spin__IyVIr 1s linear infinite;
}

.SiteGatekeeper_requirementsList__npgQQ {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.SiteGatekeeper_requirementsList__npgQQ h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1e293b;
}

.SiteGatekeeper_requirement__N5psf {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.SiteGatekeeper_requirement__N5psf:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.SiteGatekeeper_requirementStatus__9FKSE {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.SiteGatekeeper_requirementStatus__9FKSE.SiteGatekeeper_completed__cweBz {
  background: rgba(16, 185, 129, 0.1);
  border: 2px solid #10b981;
}

.SiteGatekeeper_requirementStatus__9FKSE.SiteGatekeeper_pending__HPY_j {
  background: rgba(245, 158, 11, 0.1);
  border: 2px solid #f59e0b;
  animation: SiteGatekeeper_pulse__R8746 2s infinite;
}

.SiteGatekeeper_requirementStatus__9FKSE.SiteGatekeeper_disabled__J9mHN {
  background: rgba(148, 163, 184, 0.1);
  border: 2px solid #94a3b8;
  opacity: 0.5;
}

.SiteGatekeeper_requirementText__ZDDjL {
  flex: 1 1;
}

.SiteGatekeeper_requirementText__ZDDjL h4 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #1e293b;
}

.SiteGatekeeper_requirementText__ZDDjL p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.SiteGatekeeper_actionBtn__ldUfM {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.SiteGatekeeper_actionBtn__ldUfM.SiteGatekeeper_primary__O6x4D {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.SiteGatekeeper_actionBtn__ldUfM.SiteGatekeeper_primary__O6x4D:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.SiteGatekeeper_actionBtn__ldUfM.SiteGatekeeper_payment__HJFTz {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.SiteGatekeeper_actionBtn__ldUfM.SiteGatekeeper_payment__HJFTz:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

.SiteGatekeeper_actionBtn__ldUfM.SiteGatekeeper_verification__OTmjb {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.SiteGatekeeper_actionBtn__ldUfM.SiteGatekeeper_verification__OTmjb:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

.SiteGatekeeper_successState__mJCi0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  text-align: center;
}

.SiteGatekeeper_successIcon__VBj0I {
  font-size: 64px;
  animation: SiteGatekeeper_bounce__zanWP 1s ease-in-out infinite;
}

.SiteGatekeeper_successState__mJCi0 h3 {
  font-size: 24px;
  font-weight: 800;
  margin: 0;
  color: #10b981;
}

.SiteGatekeeper_successState__mJCi0 p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.SiteGatekeeper_footer__9dvLe {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

.SiteGatekeeper_footer__9dvLe p {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.SiteGatekeeper_networkInfo__R_3fp {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 14px;
  opacity: 0.8;
}

/* Animations */
@keyframes SiteGatekeeper_float__toIdQ {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes SiteGatekeeper_spin__IyVIr {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes SiteGatekeeper_pulse__R8746 {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes SiteGatekeeper_bounce__zanWP {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .SiteGatekeeper_gatekeeper__73y3O {
    padding: 10px;
  }
  
  .SiteGatekeeper_gatekeeperContent__AagSx {
    padding: 32px 24px;
  }
  
  .SiteGatekeeper_logo__EYRp8 h1 {
    font-size: 36px;
  }
  
  .SiteGatekeeper_requirement__N5psf {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .SiteGatekeeper_requirementText__ZDDjL {
    order: -1;
  }
  
  .SiteGatekeeper_actionBtn__ldUfM {
    width: 100%;
  }
}

