"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CustomWalletModal.tsx":
/*!**********************************************!*\
  !*** ./src/components/CustomWalletModal.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit/react */ \"(app-pages-browser)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useDisconnect.js\");\n/* harmony import */ var _CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CustomWalletModal.module.css */ \"(app-pages-browser)/./src/components/CustomWalletModal.module.css\");\n/* harmony import */ var _CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CustomWalletModal = (param)=>{\n    let { isOpen, onClose } = param;\n    _s();\n    const { open } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKit)();\n    const { address, isConnected } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitAccount)();\n    const { disconnect } = (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.useDisconnect)();\n    const [hasMetaMask, setHasMetaMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasTrustWallet, setHasTrustWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _window_ethereum, _window_ethereum1;\n        // Check for MetaMask\n        setHasMetaMask( true && typeof window.ethereum !== \"undefined\" && !!((_window_ethereum = window.ethereum) === null || _window_ethereum === void 0 ? void 0 : _window_ethereum.isMetaMask));\n        // Check for Trust Wallet\n        setHasTrustWallet( true && typeof window.ethereum !== \"undefined\" && !!((_window_ethereum1 = window.ethereum) === null || _window_ethereum1 === void 0 ? void 0 : _window_ethereum1.isTrust));\n    }, []);\n    // Debug connection state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD04 Wallet connection state changed:\");\n        console.log(\"\\uD83D\\uDCCD Address:\", address);\n        console.log(\"\\uD83D\\uDD17 Connected:\", isConnected);\n        console.log(\"\\uD83E\\uDD8A MetaMask available:\", hasMetaMask);\n        console.log(\"\\uD83D\\uDEE1️ Trust Wallet available:\", hasTrustWallet);\n    }, [\n        address,\n        isConnected,\n        hasMetaMask,\n        hasTrustWallet\n    ]);\n    const walletOptions = [\n        {\n            id: \"reown-appkit\",\n            name: \"All Wallets\",\n            description: \"600+ wallets, social logins & email\",\n            icon: \"\\uD83C\\uDF10\",\n            iconBg: \"linear-gradient(135deg, #3b82f6, #1d4ed8)\",\n            tag: \"600+\",\n            tagColor: \"#3b82f6\",\n            available: true,\n            action: ()=>openReownModal()\n        },\n        {\n            id: \"metamask\",\n            name: \"MetaMask\",\n            description: \"Connect using browser extension\",\n            icon: \"\\uD83E\\uDD8A\",\n            iconBg: \"linear-gradient(135deg, #f6851b, #e2761b)\",\n            tag: hasMetaMask ? \"POPULAR\" : \"INSTALL\",\n            tagColor: hasMetaMask ? \"#10b981\" : \"#f59e0b\",\n            available: hasMetaMask,\n            action: ()=>openReownModal()\n        },\n        {\n            id: \"walletconnect\",\n            name: \"WalletConnect\",\n            description: \"Scan with WalletConnect to connect\",\n            icon: \"\\uD83D\\uDCF1\",\n            iconBg: \"linear-gradient(135deg, #3b99fc, #1a73e8)\",\n            tag: \"QR CODE\",\n            tagColor: \"#3b82f6\",\n            available: true,\n            action: ()=>openReownModal()\n        },\n        {\n            id: \"social\",\n            name: \"Social Login\",\n            description: \"Google, X, GitHub, Discord, Apple\",\n            icon: \"\\uD83D\\uDC64\",\n            iconBg: \"linear-gradient(135deg, #8b5cf6, #7c3aed)\",\n            tag: \"SOCIAL\",\n            tagColor: \"#8b5cf6\",\n            available: true,\n            action: ()=>openReownModal()\n        }\n    ];\n    const openReownModal = async ()=>{\n        try {\n            setIsLoading(true);\n            console.log(\"\\uD83D\\uDD04 Opening Reown AppKit modal...\");\n            // Add a small delay to ensure proper initialization\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            await open();\n            console.log(\"✅ Reown modal opened successfully\");\n            onClose();\n        } catch (error) {\n            console.error(\"❌ Failed to open Reown modal:\", error);\n            console.log(\"\\uD83D\\uDD04 Trying fallback connection method...\");\n            // Fallback: Try direct MetaMask connection if available\n            if (hasMetaMask && \"object\" !== \"undefined\" && window.ethereum) {\n                try {\n                    await window.ethereum.request({\n                        method: \"eth_requestAccounts\"\n                    });\n                    console.log(\"✅ Fallback MetaMask connection successful\");\n                    onClose();\n                } catch (fallbackError) {\n                    console.error(\"❌ Fallback connection also failed:\", fallbackError);\n                }\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleConnect = async (action)=>{\n        try {\n            await action();\n        } catch (error) {\n            console.error(\"Connection failed:\", error);\n        }\n    };\n    const handleDisconnect = ()=>{\n        disconnect();\n        onClose();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletModalOverlay),\n        onClick: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletModal),\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletModalHeader),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Connect a Wallet\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                            onClick: onClose,\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().connectedState),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().connectedInfo),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().connectionStatus),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDot)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Connected\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletAddress),\n                                    children: [\n                                        address === null || address === void 0 ? void 0 : address.slice(0, 6),\n                                        \"...\",\n                                        address === null || address === void 0 ? void 0 : address.slice(-4)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().disconnectButton),\n                            onClick: handleDisconnect,\n                            children: \"Disconnect\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletOptions),\n                    children: walletOptions.map((wallet)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat((_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletOption), \" \").concat(!wallet.available ? (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().disabled) : \"\"),\n                            onClick: ()=>wallet.available && handleConnect(wallet.action),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletInfo),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletIcon),\n                                            style: {\n                                                background: wallet.iconBg\n                                            },\n                                            children: wallet.icon\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletName),\n                                                    children: wallet.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletDescription),\n                                                    children: wallet.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().walletTag),\n                                    style: {\n                                        backgroundColor: wallet.tagColor\n                                    },\n                                    children: wallet.tag\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, wallet.id, true, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, undefined),\n                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().loadingState),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Opening Reown AppKit...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalFooter),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().termsText),\n                        children: [\n                            \"By connecting, I accept Werant's\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().termsLink),\n                                children: \"Terms of Service\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, undefined),\n                            \" and\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: (_CustomWalletModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().termsLink),\n                                children: \"Privacy Policy\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Werank/src/components/CustomWalletModal.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomWalletModal, \"xswXArcNAU2tR8TvALjHsB+jG8Q=\", false, function() {\n    return [\n        _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKit,\n        _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitAccount,\n        wagmi__WEBPACK_IMPORTED_MODULE_4__.useDisconnect\n    ];\n});\n_c = CustomWalletModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomWalletModal);\nvar _c;\n$RefreshReg$(_c, \"CustomWalletModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CustomWalletModal.tsx\n"));

/***/ })

});