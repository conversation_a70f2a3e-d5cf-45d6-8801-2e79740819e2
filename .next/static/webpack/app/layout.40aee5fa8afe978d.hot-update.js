"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/PaymentModal.tsx":
/*!*****************************************!*\
  !*** ./src/components/PaymentModal.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit/react */ \"(app-pages-browser)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useWriteContract.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/./node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js\");\n/* harmony import */ var _PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PaymentModal.module.css */ \"(app-pages-browser)/./src/components/PaymentModal.module.css\");\n/* harmony import */ var _PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract constants (defined outside component to avoid re-declaration)\nconst MONAD_TESTNET_ID = 10143;\nconst PAYMENT_AMOUNT = \"100000000000000000\" // 0.1 MON in wei (updated from 0.25 MON)\n;\nconst WALLET_CONNECT_CONTRACT = \"******************************************\";\n// Simplified Contract ABI for WerantWalletConnect - only essential functions\nconst WALLET_CONNECT_ABI = [\n    {\n        \"inputs\": [],\n        \"name\": \"connectWallet\",\n        \"outputs\": [],\n        \"stateMutability\": \"payable\",\n        \"type\": \"function\"\n    },\n    {\n        \"inputs\": [\n            {\n                \"internalType\": \"address\",\n                \"name\": \"wallet\",\n                \"type\": \"address\"\n            }\n        ],\n        \"name\": \"isWalletConnected\",\n        \"outputs\": [\n            {\n                \"internalType\": \"bool\",\n                \"name\": \"\",\n                \"type\": \"bool\"\n            }\n        ],\n        \"stateMutability\": \"view\",\n        \"type\": \"function\"\n    }\n];\nconst PaymentModal = (param)=>{\n    let { isOpen, onClose, onPaymentSuccess } = param;\n    _s();\n    const { address, isConnected } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitAccount)();\n    const { chainId } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitNetwork)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentStatus, setPaymentStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [txHash, setTxHash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { writeContract, data: hash, error } = (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.useWriteContract)();\n    const { isLoading: isConfirming, isSuccess: isConfirmed } = (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.useWaitForTransactionReceipt)({\n        hash\n    });\n    // Note: Contract verification removed to avoid initialization issues\n    // We'll verify the contract works when we make the actual payment\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConfirmed && hash) {\n            console.log(\"✅ Payment confirmed! Transaction hash:\", hash);\n            setPaymentStatus(\"success\");\n            setIsProcessing(false);\n            setTxHash(hash);\n            // Store payment status in localStorage\n            if (address) {\n                localStorage.setItem(\"werant_paid_\".concat(address.toLowerCase()), \"true\");\n                console.log(\"\\uD83D\\uDCBE Payment status saved to localStorage\");\n            }\n            // Call success callback after a short delay to show success message\n            setTimeout(()=>{\n                console.log(\"\\uD83C\\uDF89 Payment process complete, calling success callback\");\n                onPaymentSuccess();\n                onClose();\n            }, 3000) // Increased delay to show success message longer\n            ;\n        }\n    }, [\n        isConfirmed,\n        hash,\n        address,\n        onPaymentSuccess,\n        onClose\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error) {\n            console.error(\"❌ Transaction error:\", error);\n            console.error(\"Error details:\", {\n                message: error.message,\n                name: error.name,\n                stack: error.stack\n            });\n            setPaymentStatus(\"error\");\n            setIsProcessing(false);\n        }\n    }, [\n        error\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (hash && !isConfirmed && !isConfirming) {\n            console.log(\"\\uD83D\\uDCE1 Transaction submitted, hash:\", hash);\n            console.log(\"⏳ Waiting for confirmation...\");\n        }\n    }, [\n        hash,\n        isConfirmed,\n        isConfirming\n    ]);\n    // Log payment amount for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDCB0 PAYMENT_AMOUNT:\", PAYMENT_AMOUNT, \"wei (0.1 MON)\");\n        console.log(\"\\uD83D\\uDCCD Contract Address:\", WALLET_CONNECT_CONTRACT);\n    }, []);\n    const handlePayment = async ()=>{\n        if (!isConnected || !address) {\n            alert(\"Please connect your wallet first\");\n            return;\n        }\n        // Check if user is on correct network\n        if (chainId !== MONAD_TESTNET_ID) {\n            alert(\"Please switch to Monad Testnet (Chain ID: 10143) to make payment\");\n            return;\n        }\n        try {\n            setIsProcessing(true);\n            setPaymentStatus(\"pending\");\n            console.log(\"\\uD83D\\uDD04 Starting payment process...\");\n            console.log(\"\\uD83D\\uDCCD Contract Address:\", WALLET_CONNECT_CONTRACT);\n            console.log(\"\\uD83D\\uDCB0 Payment Amount:\", PAYMENT_AMOUNT, \"wei (0.1 MON)\");\n            console.log(\"\\uD83D\\uDC64 User Address:\", address);\n            console.log(\"\\uD83C\\uDF10 Network Chain ID:\", chainId);\n            // Call connectWallet function on WerantWalletConnect contract\n            writeContract({\n                address: WALLET_CONNECT_CONTRACT,\n                abi: WALLET_CONNECT_ABI,\n                functionName: \"connectWallet\",\n                value: BigInt(PAYMENT_AMOUNT),\n                gas: BigInt(500000)\n            });\n            console.log(\"✅ Transaction submitted\");\n        } catch (err) {\n            console.error(\"❌ Payment failed:\", err);\n            console.error(\"Error details:\", {\n                message: err instanceof Error ? err.message : \"Unknown error\",\n                code: err === null || err === void 0 ? void 0 : err.code,\n                reason: err === null || err === void 0 ? void 0 : err.reason,\n                data: err === null || err === void 0 ? void 0 : err.data\n            });\n            setPaymentStatus(\"error\");\n            setIsProcessing(false);\n        }\n    };\n    const handleSkip = ()=>{\n        // For demo purposes, allow skipping payment\n        if (address) {\n            localStorage.setItem(\"werant_paid_\".concat(address.toLowerCase()), \"true\");\n            console.log(\"⏭️ Payment skipped for demo purposes\");\n        }\n        onPaymentSuccess();\n        onClose();\n    };\n    const handleTestPayment = async ()=>{\n        // Alternative payment method - direct transfer to contract\n        if (!isConnected || !address) {\n            alert(\"Please connect your wallet first\");\n            return;\n        }\n        if (chainId !== MONAD_TESTNET_ID) {\n            alert(\"Please switch to Monad Testnet (Chain ID: 10143) to make payment\");\n            return;\n        }\n        try {\n            setIsProcessing(true);\n            setPaymentStatus(\"pending\");\n            console.log(\"\\uD83E\\uDDEA Testing direct transfer payment...\");\n            // Use the same connectWallet function but with different gas limit\n            writeContract({\n                address: WALLET_CONNECT_CONTRACT,\n                abi: WALLET_CONNECT_ABI,\n                functionName: \"connectWallet\",\n                value: BigInt(PAYMENT_AMOUNT),\n                gas: BigInt(300000)\n            });\n            console.log(\"✅ Test payment submitted\");\n        } catch (err) {\n            console.error(\"❌ Test payment failed:\", err);\n            setPaymentStatus(\"error\");\n            setIsProcessing(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().overlay),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"\\uD83C\\uDF89 Welcome to Werant!\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"One-time activation fee for new users\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().content),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().paymentInfo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().amountDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().amount),\n                                        children: \"0.25 MON\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().currency),\n                                        children: \"Monad Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().benefits),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"What you get:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Full access to Werant voting platform\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Participate in community governance\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Create and vote on proposals\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"✅ Access to leaderboard features\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, undefined),\n                            paymentStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"\".concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn), \" \").concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().primary)),\n                                        onClick: handlePayment,\n                                        disabled: !isConnected || isProcessing,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                                children: \"\\uD83D\\uDCB3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Pay 0.1 MON\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"\".concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn), \" \").concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondary)),\n                                        onClick: handleTestPayment,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                                children: \"\\uD83E\\uDDEA\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Test Payment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"\".concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn), \" \").concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondary)),\n                                        onClick: handleSkip,\n                                        style: {\n                                            fontSize: \"14px\",\n                                            padding: \"12px 20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                                children: \"⏭️\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Skip for Demo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined),\n                            paymentStatus === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Processing payment...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isConfirming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Waiting for confirmation...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 34\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined),\n                            paymentStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().successIcon),\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Payment successful!\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Welcome to Werant! \\uD83C\\uDF89\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined),\n                            paymentStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusDisplay),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorIcon),\n                                        children: \"❌\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Payment failed\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: \"14px\",\n                                            color: \"#ef4444\",\n                                            marginBottom: \"16px\"\n                                        },\n                                        children: (error === null || error === void 0 ? void 0 : error.message) || \"Transaction failed. Please try again.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"12px\",\n                                            width: \"100%\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn), \" \").concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().primary)),\n                                                onClick: ()=>{\n                                                    setPaymentStatus(\"idle\");\n                                                    setIsProcessing(false);\n                                                },\n                                                children: \"Try Again\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().payBtn), \" \").concat((_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondary)),\n                                                onClick: handleTestPayment,\n                                                children: \"\\uD83E\\uDDEA Try Test Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined),\n                paymentStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: (_PaymentModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeBtn),\n                    onClick: onClose,\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Werank/src/components/PaymentModal.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PaymentModal, \"U5d7utDq20MgVq9ZpRG/Z/8ajbQ=\", false, function() {\n    return [\n        _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitAccount,\n        _reown_appkit_react__WEBPACK_IMPORTED_MODULE_2__.useAppKitNetwork,\n        wagmi__WEBPACK_IMPORTED_MODULE_4__.useWriteContract,\n        wagmi__WEBPACK_IMPORTED_MODULE_5__.useWaitForTransactionReceipt\n    ];\n});\n_c = PaymentModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PaymentModal);\nvar _c;\n$RefreshReg$(_c, \"PaymentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PaymentModal.tsx\n"));

/***/ })

});