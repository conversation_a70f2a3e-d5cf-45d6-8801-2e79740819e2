const { ethers } = require("ethers");

async function main() {
  console.log("🔍 Testing the new private key...\n");

  // Use the private key you provided directly
  const privateKey = "0x70c493f4625bf6435ff02cc585602bf6fb18f000f9f767fdaa37e459991c8d0a";
  
  console.log("Private Key Length:", privateKey.length);
  console.log("Private Key:", privateKey);
  
  try {
    // Create wallet from private key
    const wallet = new ethers.Wallet(privateKey);
    console.log("\n📍 Derived Address:", wallet.address);
    
    // Check if this matches your expected address
    const expectedAddress = "******************************************";
    console.log("Expected Address:", expectedAddress);
    console.log("Addresses Match:", wallet.address.toLowerCase() === expectedAddress.toLowerCase());
    
    // Connect to Monad testnet
    const provider = new ethers.providers.JsonRpcProvider("https://testnet-rpc.monad.xyz");
    const connectedWallet = wallet.connect(provider);
    
    // Check balance
    const balance = await connectedWallet.getBalance();
    const balanceInMON = ethers.utils.formatEther(balance);
    
    console.log("\n💰 Balance:", balanceInMON, "MON");
    
    if (balance.gt(ethers.utils.parseEther("0.5"))) {
      console.log("\n✅ This address has sufficient balance for deployment!");
      console.log("🚀 Ready to deploy contracts");
    } else {
      console.log("\n❌ This address needs more MON tokens");
      console.log("📋 Send MON tokens to:", wallet.address);
      console.log("💡 You need at least 1 MON for deployment");
    }
    
  } catch (error) {
    console.error("❌ Error with private key:", error.message);
    
    if (error.message.includes("invalid private key")) {
      console.log("\n💡 The private key appears to be incomplete or invalid");
      console.log("🔧 A valid private key should be:");
      console.log("   - 64 hexadecimal characters (0-9, a-f)");
      console.log("   - Plus '0x' prefix");
      console.log("   - Total length: 66 characters");
      console.log("\n📏 Current length:", privateKey.length);
      console.log("📏 Expected length: 66");
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
