const { ethers } = require("ethers");
require('dotenv').config();

async function main() {
  console.log("🔍 Checking Private Key and Address...\n");

  const privateKey = process.env.DEPLOYER_PRIVATE_KEY;
  
  if (!privateKey) {
    console.log("❌ No DEPLOYER_PRIVATE_KEY found in .env file");
    return;
  }

  console.log("Private Key (first 10 chars):", privateKey.substring(0, 10) + "...");
  
  try {
    // Create wallet from private key
    const wallet = new ethers.Wallet(privateKey);
    console.log("Derived Address:", wallet.address);
    
    // Connect to Monad testnet
    const provider = new ethers.providers.JsonRpcProvider("https://testnet-rpc.monad.xyz");
    const connectedWallet = wallet.connect(provider);
    
    // Check balance
    const balance = await connectedWallet.getBalance();
    const balanceInMON = ethers.utils.formatEther(balance);
    
    console.log("Balance:", balanceInMON, "MON");
    
    if (balance.gt(ethers.utils.parseEther("1"))) {
      console.log("\n✅ Sufficient balance for deployment!");
      console.log("🚀 Ready to deploy contracts");
    } else {
      console.log("\n❌ Insufficient balance for deployment");
      console.log("📋 Need at least 1 MON for deployment");
      console.log("💡 Make sure this address has MON tokens:", wallet.address);
    }
    
  } catch (error) {
    console.error("❌ Error checking address:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
