const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Checking Monad Testnet Balance...\n");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Account Address:", deployer.address);
  
  // Check balance
  const balance = await deployer.getBalance();
  const balanceInMON = ethers.utils.formatEther(balance);
  
  console.log("Current Balance:", balanceInMON, "MON");
  
  if (balance.lt(ethers.utils.parseEther("1"))) {
    console.log("\n❌ Insufficient Balance for Deployment");
    console.log("📋 You need at least 1 MON for deployment");
    console.log("\n🪙 How to get MON tokens:");
    console.log("1. Visit Monad testnet faucet");
    console.log("2. Enter your address:", deployer.address);
    console.log("3. Request testnet MON tokens");
    console.log("4. Wait for confirmation");
    console.log("\n🔗 Resources:");
    console.log("- Monad Discord: Check #faucet channel");
    console.log("- Monad Twitter: Look for faucet announcements");
    console.log("- Search: 'Monad testnet faucet'");
  } else {
    console.log("\n✅ Sufficient Balance for Deployment");
    console.log("🚀 Ready to deploy contracts!");
    console.log("\nRun: npx hardhat run scripts/deploy.js --network monad-testnet");
  }
  
  // Check network
  const network = await ethers.provider.getNetwork();
  console.log("\n🌐 Network Info:");
  console.log("Chain ID:", network.chainId);
  console.log("Network Name:", network.name || "Unknown");
  
  if (network.chainId !== 10143) {
    console.log("⚠️  Warning: Not connected to Monad Testnet (Chain ID: 10143)");
  } else {
    console.log("✅ Connected to Monad Testnet");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Balance check failed:", error);
    process.exit(1);
  });
