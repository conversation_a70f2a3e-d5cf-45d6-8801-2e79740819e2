const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Deploying Werant contracts locally for testing...\n");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  
  // Check balance
  const balance = await deployer.getBalance();
  console.log("Account balance:", ethers.utils.formatEther(balance), "ETH\n");

  // Deploy WerantWalletConnect
  console.log("📝 Deploying WerantWalletConnect...");
  const WerantWalletConnect = await ethers.getContractFactory("WerantWalletConnect");
  const walletConnect = await WerantWalletConnect.deploy();
  await walletConnect.deployed();
  
  console.log("✅ WerantWalletConnect deployed to:", walletConnect.address);
  console.log("   Transaction hash:", walletConnect.deployTransaction.hash);

  // Deploy WerantLeaderboard
  console.log("\n📝 Deploying WerantLeaderboard...");
  const WerantLeaderboard = await ethers.getContractFactory("WerantLeaderboard");
  const leaderboard = await WerantLeaderboard.deploy();
  await leaderboard.deployed();
  
  console.log("✅ WerantLeaderboard deployed to:", leaderboard.address);
  console.log("   Transaction hash:", leaderboard.deployTransaction.hash);

  // Save deployment info
  const deploymentInfo = {
    network: "localhost",
    chainId: 31337,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    contracts: {
      WerantWalletConnect: {
        address: walletConnect.address,
        transactionHash: walletConnect.deployTransaction.hash,
        connectionFee: "0.25", // MON
      },
      WerantLeaderboard: {
        address: leaderboard.address,
        transactionHash: leaderboard.deployTransaction.hash,
        votingFee: "0.1", // MON
      }
    }
  };

  // Save to file
  const deploymentPath = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentPath)) {
    fs.mkdirSync(deploymentPath, { recursive: true });
  }
  
  const filename = `deployment-local-${Date.now()}.json`;
  fs.writeFileSync(
    path.join(deploymentPath, filename),
    JSON.stringify(deploymentInfo, null, 2)
  );

  // Update .env file with contract addresses
  const envPath = path.join(__dirname, "../.env");
  let envContent = "";
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, "utf8");
  }

  // Update or add contract addresses
  const updateEnvVar = (content, key, value) => {
    const regex = new RegExp(`^${key}=.*$`, "m");
    if (regex.test(content)) {
      return content.replace(regex, `${key}=${value}`);
    } else {
      return content + `\n${key}=${value}`;
    }
  };

  envContent = updateEnvVar(envContent, "WALLET_CONNECT_CONTRACT_ADDRESS", walletConnect.address);
  envContent = updateEnvVar(envContent, "LEADERBOARD_CONTRACT_ADDRESS", leaderboard.address);

  fs.writeFileSync(envPath, envContent);

  console.log("\n🎉 Local Deployment Summary:");
  console.log("============================");
  console.log("Network:", "Localhost");
  console.log("Chain ID:", "31337");
  console.log("Deployer:", deployer.address);
  console.log("WerantWalletConnect:", walletConnect.address);
  console.log("WerantLeaderboard:", leaderboard.address);
  console.log("Deployment info saved to:", filename);
  console.log("Environment variables updated in .env");

  console.log("\n📋 For Monad Testnet Deployment:");
  console.log("=================================");
  console.log("1. Get MON tokens from Monad testnet faucet");
  console.log("2. Update DEPLOYER_PRIVATE_KEY in .env with funded account");
  console.log("3. Run: npx hardhat run scripts/deploy.js --network monad-testnet");

  console.log("\n🔗 Mock Contract Addresses for Testing:");
  console.log("=======================================");
  console.log("These addresses work for local testing and frontend development");
  console.log("Replace with real Monad testnet addresses after deployment");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Local deployment failed:", error);
    process.exit(1);
  });
