const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Deploying Werant contracts to Monad Testnet...\n");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  
  // Check balance
  const balance = await deployer.getBalance();
  console.log("Account balance:", ethers.utils.formatEther(balance), "MON\n");

  if (balance.lt(ethers.utils.parseEther("1"))) {
    console.log("⚠️  Warning: Low balance. Make sure you have enough MON for deployment.");
  }

  // Deploy WerantWalletConnect
  console.log("📝 Deploying WerantWalletConnect...");
  const WerantWalletConnect = await ethers.getContractFactory("WerantWalletConnect");
  const walletConnect = await WerantWalletConnect.deploy();
  await walletConnect.deployed();
  
  console.log("✅ WerantWalletConnect deployed to:", walletConnect.address);
  console.log("   Transaction hash:", walletConnect.deployTransaction.hash);

  // Deploy WerantLeaderboard
  console.log("\n📝 Deploying WerantLeaderboard...");
  const WerantLeaderboard = await ethers.getContractFactory("WerantLeaderboard");
  const leaderboard = await WerantLeaderboard.deploy();
  await leaderboard.deployed();

  console.log("✅ WerantLeaderboard deployed to:", leaderboard.address);
  console.log("   Transaction hash:", leaderboard.deployTransaction.hash);

  // Deploy WerantRewards
  console.log("\n📝 Deploying WerantRewards...");
  const WerantRewards = await ethers.getContractFactory("WerantRewards");
  const rewards = await WerantRewards.deploy();
  await rewards.deployed();

  console.log("✅ WerantRewards deployed to:", rewards.address);
  console.log("   Transaction hash:", rewards.deployTransaction.hash);

  // Save deployment info
  const deploymentInfo = {
    network: "monad-testnet",
    chainId: 41454,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    contracts: {
      WerantWalletConnect: {
        address: walletConnect.address,
        transactionHash: walletConnect.deployTransaction.hash,
        gasUsed: walletConnect.deployTransaction.gasLimit?.toString(),
        connectionFee: "0.1", // MON
      },
      WerantLeaderboard: {
        address: leaderboard.address,
        transactionHash: leaderboard.deployTransaction.hash,
        gasUsed: leaderboard.deployTransaction.gasLimit?.toString(),
        votingFee: "0.1", // MON
      },
      WerantRewards: {
        address: rewards.address,
        transactionHash: rewards.deployTransaction.hash,
        gasUsed: rewards.deployTransaction.gasLimit?.toString(),
        purpose: "Batch reward distribution", // Description
      }
    }
  };

  // Save to file
  const deploymentPath = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentPath)) {
    fs.mkdirSync(deploymentPath, { recursive: true });
  }
  
  const filename = `deployment-${Date.now()}.json`;
  fs.writeFileSync(
    path.join(deploymentPath, filename),
    JSON.stringify(deploymentInfo, null, 2)
  );

  // Update .env file with contract addresses
  const envPath = path.join(__dirname, "../.env");
  let envContent = "";
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, "utf8");
  }

  // Update or add contract addresses
  const updateEnvVar = (content, key, value) => {
    const regex = new RegExp(`^${key}=.*$`, "m");
    if (regex.test(content)) {
      return content.replace(regex, `${key}=${value}`);
    } else {
      return content + `\n${key}=${value}`;
    }
  };

  envContent = updateEnvVar(envContent, "WALLET_CONNECT_CONTRACT_ADDRESS", walletConnect.address);
  envContent = updateEnvVar(envContent, "LEADERBOARD_CONTRACT_ADDRESS", leaderboard.address);
  envContent = updateEnvVar(envContent, "REWARDS_CONTRACT_ADDRESS", rewards.address);

  fs.writeFileSync(envPath, envContent);

  console.log("\n🎉 Deployment Summary:");
  console.log("=====================");
  console.log("Network:", "Monad Testnet");
  console.log("Chain ID:", "41454");
  console.log("Deployer:", deployer.address);
  console.log("WerantWalletConnect:", walletConnect.address);
  console.log("WerantLeaderboard:", leaderboard.address);
  console.log("WerantRewards:", rewards.address);
  console.log("Deployment info saved to:", filename);
  console.log("Environment variables updated in .env");

  console.log("\n📋 Next Steps:");
  console.log("==============");
  console.log("1. Verify contracts on Monad Explorer");
  console.log("2. Update frontend with contract addresses");
  console.log("3. Test wallet connection (0.1 MON fee)");
  console.log("4. Test voting functionality (0.1 MON fee)");
  console.log("5. Fund the deployer account for ongoing operations");

  console.log("\n🔗 Useful Links:");
  console.log("================");
  console.log("Monad Testnet Explorer:", "https://testnet-explorer.monad.xyz");
  console.log("WalletConnect Contract:", `https://testnet-explorer.monad.xyz/address/${walletConnect.address}`);
  console.log("Leaderboard Contract:", `https://testnet-explorer.monad.xyz/address/${leaderboard.address}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
