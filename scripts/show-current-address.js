const { ethers } = require("ethers");
const fs = require("fs");

async function main() {
  console.log("🔍 Reading current .env file...\n");

  // Read .env file directly
  const envContent = fs.readFileSync('.env', 'utf8');
  const lines = envContent.split('\n');
  
  let privateKey = null;
  for (const line of lines) {
    if (line.startsWith('DEPLOYER_PRIVATE_KEY=')) {
      privateKey = line.split('=')[1];
      break;
    }
  }
  
  if (!privateKey) {
    console.log("❌ No DEPLOYER_PRIVATE_KEY found in .env file");
    return;
  }

  console.log("Private Key Length:", privateKey.length);
  console.log("Private Key (first 10 chars):", privateKey.substring(0, 10) + "...");
  console.log("Private Key (last 10 chars):", "..." + privateKey.substring(privateKey.length - 10));
  
  try {
    // Create wallet from private key
    const wallet = new ethers.Wallet(privateKey);
    console.log("\n📍 Derived Address:", wallet.address);
    
    // Connect to Monad testnet
    const provider = new ethers.providers.JsonRpcProvider("https://testnet-rpc.monad.xyz");
    const connectedWallet = wallet.connect(provider);
    
    // Check balance
    const balance = await connectedWallet.getBalance();
    const balanceInMON = ethers.utils.formatEther(balance);
    
    console.log("💰 Balance:", balanceInMON, "MON");
    
    if (balance.gt(ethers.utils.parseEther("0.5"))) {
      console.log("\n✅ This address has sufficient balance for deployment!");
      console.log("🚀 You can proceed with deployment");
    } else {
      console.log("\n❌ This address needs more MON tokens");
      console.log("📋 Send MON tokens to:", wallet.address);
      console.log("💡 You need at least 1 MON for deployment");
    }
    
  } catch (error) {
    console.error("❌ Error with private key:", error.message);
    console.log("💡 Make sure the private key is valid and complete");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
