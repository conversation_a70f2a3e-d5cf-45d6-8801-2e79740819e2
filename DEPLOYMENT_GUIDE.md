# 🚀 Werant Deployment Guide

## 📋 Overview

Werant is a blockchain-based voting system with wallet connection functionality built on Monad Testnet. This guide will help you deploy and test the complete system.

## ✅ Current Status

- ✅ **Smart Contracts**: Compiled successfully
- ✅ **Backend API**: Running on port 3002
- ✅ **Database**: MongoDB connected
- ✅ **Network**: Configured for Monad Testnet
- ⏳ **Contracts**: Ready for deployment

## 🔧 Prerequisites

1. **Monad Testnet Setup**:
   - Add Monad Testnet to MetaMask
   - Get testnet MON tokens from faucet
   - Have at least 1 MON for deployment

2. **Environment Setup**:
   - Node.js v16+
   - MongoDB running
   - Private key for deployment

## 📝 Step 1: Configure Environment

Update your `.env` file with your deployment private key:

```bash
# Add your private key (without 0x prefix)
DEPLOYER_PRIVATE_KEY=your_private_key_here
ADMIN_PRIVATE_KEY=your_admin_private_key_here
```

## 🚀 Step 2: Deploy Smart Contracts

Deploy contracts to Monad Testnet:

```bash
npx hardhat run scripts/deploy.js --network monad-testnet
```

Expected output:
```
🚀 Deploying Werant contracts to Monad Testnet...
✅ WerantWalletConnect deployed to: 0x...
✅ WerantLeaderboard deployed to: 0x...
```

The deployment script will automatically update your `.env` file with contract addresses.

## 🧪 Step 3: Test the System

### Test API Endpoints

1. **Health Check**:
```bash
curl http://localhost:3002/health
```

2. **Contract Information**:
```bash
curl http://localhost:3002/api/contracts/info
```

3. **Network Status**:
```bash
curl http://localhost:3002/api/contracts/network
```

### Test Wallet Connection Flow

1. **Connect Wallet** (Frontend Integration):
   - User connects wallet via MetaMask
   - User sends 0.25 MON to wallet contract
   - Frontend calls API to record connection

2. **Record Connection** (API Call):
```bash
curl -X POST http://localhost:3002/api/wallet/connect \
  -H "Content-Type: application/json" \
  -d '{
    "walletAddress": "******************************************",
    "transactionHash": "0x...",
    "connectionId": 1,
    "feePaid": "250000000000000000",
    "blockNumber": 12345,
    "gasUsed": "200000"
  }'
```

3. **Check Connection Status**:
```bash
curl http://localhost:3002/api/wallet/status/******************************************
```

### Test Voting Flow

1. **Create Project** (requires wallet connection):
```bash
curl -X POST http://localhost:3002/api/leaderboard/project \
  -H "Content-Type: application/json" \
  -d '{
    "walletAddress": "******************************************",
    "name": "Test Project",
    "description": "A test project for Werant",
    "transactionHash": "0x...",
    "projectId": 1,
    "blockNumber": 12346,
    "category": "DeFi"
  }'
```

2. **Vote on Project** (requires wallet connection + 0.1 MON fee):
```bash
curl -X POST http://localhost:3002/api/leaderboard/vote \
  -H "Content-Type: application/json" \
  -d '{
    "walletAddress": "******************************************",
    "projectId": 1,
    "score": 4,
    "comment": "Great project!",
    "transactionHash": "0x...",
    "voteId": 1,
    "blockNumber": 12347,
    "feePaid": "100000000000000000",
    "gasUsed": "300000"
  }'
```

3. **View Leaderboard**:
```bash
curl http://localhost:3002/api/leaderboard
```

## 🌐 Frontend Integration

### MetaMask Configuration

Add Monad Testnet to MetaMask:

```javascript
await window.ethereum.request({
  method: 'wallet_addEthereumChain',
  params: [{
    chainId: '0xA1F6', // 41454 in hex
    chainName: 'Monad Testnet',
    rpcUrls: ['https://testnet-rpc.monad.xyz'],
    nativeCurrency: {
      name: 'MON',
      symbol: 'MON',
      decimals: 18
    },
    blockExplorerUrls: ['https://testnet-explorer.monad.xyz']
  }]
});
```

### Contract Interaction

```javascript
// Contract addresses (from deployment)
const WALLET_CONNECT_ADDRESS = "0x..."; // From .env
const LEADERBOARD_ADDRESS = "0x...";    // From .env

// Connect wallet (0.25 MON fee)
const connectTx = await walletContract.connectWallet({
  value: ethers.utils.parseEther("0.25")
});

// Vote on project (0.1 MON fee)
const voteTx = await leaderboardContract.vote(
  projectId, 
  score, 
  comment,
  { value: ethers.utils.parseEther("0.1") }
);
```

## 📊 Gas Fees Summary

| Action | Gas Fee | Description |
|--------|---------|-------------|
| **Wallet Connection** | 0.25 MON | One-time fee to connect wallet |
| **Voting** | 0.1 MON | Fee per vote (requires connected wallet) |
| **Project Creation** | Gas only | No additional fee |

## 🔗 Important URLs

- **API Base**: `http://localhost:3002`
- **Monad Testnet RPC**: `https://testnet-rpc.monad.xyz`
- **Explorer**: `https://testnet-explorer.monad.xyz`
- **Chain ID**: `41454`

## 🛠️ Troubleshooting

### Common Issues

1. **"Insufficient funds"**: Ensure you have enough MON for gas + fees
2. **"Wallet not connected"**: Must connect wallet before voting
3. **"Already voted"**: Each wallet can only vote once per project
4. **"Transaction failed"**: Check gas limits and network connection

### Debug Commands

```bash
# Check contract deployment
npx hardhat verify --network monad-testnet CONTRACT_ADDRESS

# Check transaction status
curl http://localhost:3002/api/contracts/transaction/0x...

# View recent events
curl http://localhost:3002/api/contracts/events
```

## 🎯 Next Steps

1. **Deploy to Production**: Update RPC URLs and private keys
2. **Build Frontend**: Create React/Vue app using the API
3. **Add Features**: Implement additional voting categories
4. **Monitor**: Set up logging and analytics
5. **Scale**: Consider L2 solutions for lower fees

## 📞 Support

- Check the API documentation at `/api/contracts/info`
- View contract events at `/api/contracts/events`
- Monitor health at `/health`

Your Werant backend is now ready for production use! 🎉
