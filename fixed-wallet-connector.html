<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werant - Fixed Wallet Connector</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 3rem;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .gradient-btn {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 8px;
            padding: 18px 36px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.2s ease;
            transform: scale(1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
            border: none;
            cursor: pointer;
            font-size: 18px;
            background: linear-gradient(to right, #9333ea 20%, #ec4899 80%);
            color: white;
            width: 100%;
        }
        
        .gradient-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
            background: linear-gradient(to right, #7c3aed, #db2777);
        }
        
        .wallet-widget {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 24px;
            backdrop-filter: blur(10px);
            color: #1a1b23;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); border: 1px solid rgba(76, 175, 80, 0.5); }
        .error { background: rgba(244, 67, 54, 0.3); border: 1px solid rgba(244, 67, 54, 0.5); }
        .info { background: rgba(33, 150, 243, 0.3); border: 1px solid rgba(33, 150, 243, 0.5); }
        .warning { background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.5); }
        
        .fee-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .fee-info h3 {
            color: #ff6b6b;
            margin-top: 0;
        }

        .wallet-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        }
        
        .modal-content {
            background: white;
            border-radius: 24px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            color: #333;
        }
        
        .wallet-option {
            width: 100%;
            padding: 16px;
            margin: 8px 0;
            border: 2px solid #f3f4f6;
            background: white;
            border-radius: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 16px;
            transition: all 0.2s ease;
        }
        
        .wallet-option:hover {
            border-color: #9333ea;
            background: #f8faff;
            transform: translateY(-1px);
        }
        
        .wallet-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Werant</h1>
            <p>Fixed Wallet Connector - Guaranteed to Work!</p>
        </div>

        <div class="fee-info">
            <h3>💰 Fee Structure</h3>
            <ul>
                <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
                <li><strong>Voting:</strong> 0.1 MON per vote</li>
                <li><strong>Project Creation:</strong> Gas fees only</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <div id="connectSection">
                <button onclick="openWalletModal()" class="gradient-btn">
                    <i class="fas fa-wallet"></i>
                    Connect Wallet
                </button>
            </div>
            
            <div id="walletWidget" class="wallet-widget" style="display: none;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="width: 12px; height: 12px; background: #10b981; border-radius: 50%; display: inline-block;"></span>
                        <span style="font-weight: 600;">Connected</span>
                    </div>
                    <button onclick="disconnectWallet()" style="background: none; border: none; color: #ef4444; cursor: pointer; padding: 6px;">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                
                <div>
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 14px; color: #6b7280;">Address:</span>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span id="walletAddress" style="font-family: monospace; font-size: 14px;"></span>
                            <button onclick="copyAddress()" style="background: none; border: none; color: #9333ea; cursor: pointer; padding: 4px;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="font-size: 14px; color: #6b7280;">Balance:</span>
                        <span id="walletBalance" style="font-size: 14px;">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" class="status success">
            Ready! Click "Connect Wallet" to start.
        </div>

        <!-- Debug Section -->
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testWallets()" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 12px;
                margin-right: 10px;
            ">Test Wallets</button>
            
            <button onclick="forceConnect()" style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 12px;
            ">Force Connect</button>
        </div>
    </div>

    <script>
        // Global variables
        let provider = null;
        let signer = null;
        let userAddress = null;
        let isConnected = false;

        console.log('Script loaded successfully');

        // Utility functions
        function shortenAddr(addr) {
            if (!addr) return '';
            return addr.slice(0, 5) + "..." + addr.slice(-5);
        }

        function updateStatus(message, type = 'info') {
            console.log('Status:', message, type);
            const statusDiv = document.getElementById('status');
            if (statusDiv) {
                statusDiv.textContent = message;
                statusDiv.className = `status ${type}`;
            }
        }

        function updateWalletUI() {
            console.log('Updating wallet UI, connected:', isConnected, 'address:', userAddress);
            const connectSection = document.getElementById('connectSection');
            const walletWidget = document.getElementById('walletWidget');

            if (isConnected && userAddress) {
                connectSection.style.display = 'none';
                walletWidget.style.display = 'block';
                
                document.getElementById('walletAddress').textContent = shortenAddr(userAddress);
                loadWalletBalance();
            } else {
                connectSection.style.display = 'block';
                walletWidget.style.display = 'none';
            }
        }

        async function loadWalletBalance() {
            try {
                if (userAddress) {
                    const rpcProvider = new ethers.providers.JsonRpcProvider('https://testnet-rpc.monad.xyz');
                    const balance = await rpcProvider.getBalance(userAddress);
                    const balanceInMON = ethers.utils.formatEther(balance);
                    document.getElementById('walletBalance').textContent = `${parseFloat(balanceInMON).toFixed(4)} MON`;
                }
            } catch (error) {
                console.error('Error loading balance:', error);
                document.getElementById('walletBalance').textContent = 'Error loading balance';
            }
        }

        // Comprehensive wallet detection
        function detectWallets() {
            console.log('=== WALLET DETECTION ===');
            console.log('window.ethereum exists:', !!window.ethereum);

            let walletInfo = {
                hasEthereum: !!window.ethereum,
                metamask: false,
                coinbase: false,
                trust: false,
                providers: []
            };

            if (window.ethereum) {
                // Check direct properties
                walletInfo.metamask = !!window.ethereum.isMetaMask;
                walletInfo.coinbase = !!window.ethereum.isCoinbaseWallet;
                walletInfo.trust = !!window.ethereum.isTrust;

                console.log('Direct detection:');
                console.log('- MetaMask:', walletInfo.metamask);
                console.log('- Coinbase:', walletInfo.coinbase);
                console.log('- Trust:', walletInfo.trust);

                // Check providers array (for multiple wallets)
                if (window.ethereum.providers && Array.isArray(window.ethereum.providers)) {
                    console.log('Multiple providers detected:', window.ethereum.providers.length);
                    window.ethereum.providers.forEach((provider, index) => {
                        console.log(`Provider ${index}:`, {
                            isMetaMask: provider.isMetaMask,
                            isCoinbaseWallet: provider.isCoinbaseWallet,
                            isTrust: provider.isTrust
                        });

                        if (provider.isMetaMask) walletInfo.metamask = true;
                        if (provider.isCoinbaseWallet) walletInfo.coinbase = true;
                        if (provider.isTrust) walletInfo.trust = true;
                    });
                }

                // Additional MetaMask detection methods
                if (!walletInfo.metamask) {
                    // Check for MetaMask specific properties
                    if (window.ethereum._metamask || window.ethereum.selectedAddress !== undefined) {
                        console.log('MetaMask detected via alternative method');
                        walletInfo.metamask = true;
                    }
                }
            }

            console.log('Final detection results:', walletInfo);
            return walletInfo;
        }

        // Open wallet modal
        function openWalletModal() {
            console.log('Opening wallet modal...');
            updateStatus('Opening wallet selection...', 'info');

            // Detect wallets first
            const wallets = detectWallets();
            
            const modal = document.createElement('div');
            modal.className = 'wallet-modal';
            modal.id = 'walletModal';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            
            modalContent.innerHTML = `
                <div style="text-align: center; margin-bottom: 24px;">
                    <h2 style="margin: 0; font-size: 24px; font-weight: 700;">Connect Wallet</h2>
                    <p style="color: #6b7280; margin: 8px 0 0 0;">Choose your preferred wallet</p>
                </div>
                <div id="wallet-options">
                    <button onclick="connectMetaMask()" class="wallet-option">
                        <div class="wallet-icon" style="background: linear-gradient(45deg, #f6851b, #e2761b);">🦊</div>
                        <div style="flex: 1; text-align: left;">
                            <div style="font-weight: 600;">MetaMask</div>
                            <div style="font-size: 14px; color: #6b7280; margin-top: 2px;">Connect using MetaMask browser extension</div>
                        </div>
                        <div id="metamask-status" style="color: #10b981; font-size: 18px;">
                            ✓
                        </div>
                    </button>
                    
                    <button onclick="connectCoinbase()" class="wallet-option">
                        <div class="wallet-icon" style="background: linear-gradient(45deg, #0052ff, #0041cc);">💼</div>
                        <div style="flex: 1; text-align: left;">
                            <div style="font-weight: 600;">Coinbase Wallet</div>
                            <div style="font-size: 14px; color: #6b7280; margin-top: 2px;">Connect using Coinbase Wallet</div>
                        </div>
                        <div id="coinbase-status" style="color: #10b981; font-size: 18px;">
                            ✓
                        </div>
                    </button>

                    <button onclick="connectAnyWallet()" class="wallet-option">
                        <div class="wallet-icon" style="background: linear-gradient(45deg, #8b5cf6, #7c3aed);">💳</div>
                        <div style="flex: 1; text-align: left;">
                            <div style="font-weight: 600;">Any Wallet</div>
                            <div style="font-size: 14px; color: #6b7280; margin-top: 2px;">Connect using any available wallet</div>
                        </div>
                        <div id="anywallet-status" style="color: #10b981; font-size: 18px;">
                            ✓
                        </div>
                    </button>
                </div>
                <button onclick="closeModal()" style="
                    width: 100%;
                    padding: 12px;
                    margin-top: 20px;
                    border: 2px solid #f3f4f6;
                    background: #f9fafb;
                    border-radius: 12px;
                    cursor: pointer;
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                ">Cancel</button>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Update wallet status indicators after modal is created
            setTimeout(() => {
                updateWalletStatusIndicators(wallets);
            }, 100);

            // Close on backdrop click
            modal.onclick = (e) => {
                if (e.target === modal) closeModal();
            };

            console.log('Modal created and added to DOM');
        }

        function closeModal() {
            const modal = document.getElementById('walletModal');
            if (modal) {
                modal.remove();
                console.log('Modal closed');
            }
        }

        // Update wallet status indicators in modal
        function updateWalletStatusIndicators(wallets) {
            const metamaskStatus = document.getElementById('metamask-status');
            const coinbaseStatus = document.getElementById('coinbase-status');
            const anywalletStatus = document.getElementById('anywallet-status');

            if (metamaskStatus) {
                metamaskStatus.style.color = wallets.metamask ? '#10b981' : '#ef4444';
                metamaskStatus.textContent = wallets.metamask ? '✓' : '✗';
            }

            if (coinbaseStatus) {
                coinbaseStatus.style.color = wallets.coinbase ? '#10b981' : '#ef4444';
                coinbaseStatus.textContent = wallets.coinbase ? '✓' : '✗';
            }

            if (anywalletStatus) {
                anywalletStatus.style.color = wallets.hasEthereum ? '#10b981' : '#ef4444';
                anywalletStatus.textContent = wallets.hasEthereum ? '✓' : '✗';
            }

            console.log('Wallet status indicators updated');
        }

        // Wallet connection functions
        async function connectMetaMask() {
            console.log('Connecting to MetaMask...');
            closeModal();

            try {
                updateStatus('Connecting to MetaMask...', 'info');

                // Enhanced MetaMask detection
                let metamaskProvider = null;

                if (!window.ethereum) {
                    updateStatus('No wallet found! Please install MetaMask extension.', 'error');
                    return;
                }

                // Try to find MetaMask provider
                if (window.ethereum.isMetaMask) {
                    metamaskProvider = window.ethereum;
                    console.log('MetaMask found directly');
                } else if (window.ethereum.providers) {
                    // Look for MetaMask in providers array
                    metamaskProvider = window.ethereum.providers.find(provider => provider.isMetaMask);
                    if (metamaskProvider) {
                        console.log('MetaMask found in providers array');
                    }
                } else {
                    // Fallback: use any available provider
                    metamaskProvider = window.ethereum;
                    console.log('Using fallback provider (might be MetaMask)');
                }

                if (!metamaskProvider) {
                    updateStatus('MetaMask not detected. Trying to connect anyway...', 'warning');
                    metamaskProvider = window.ethereum;
                }

                console.log('Using provider:', metamaskProvider);

                // Request account access
                const accounts = await metamaskProvider.request({ method: 'eth_requestAccounts' });
                console.log('Accounts received:', accounts);

                if (accounts.length === 0) {
                    updateStatus('No accounts found. Please unlock your wallet.', 'error');
                    return;
                }

                provider = new ethers.providers.Web3Provider(metamaskProvider);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                console.log('Connected address:', userAddress);

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('Wallet connected successfully! 🦊', 'success');

            } catch (error) {
                console.error('MetaMask connection error:', error);
                if (error.code === 4001) {
                    updateStatus('Connection rejected by user', 'warning');
                } else if (error.code === -32002) {
                    updateStatus('Connection request already pending. Please check your wallet.', 'warning');
                } else {
                    updateStatus('Connection failed: ' + error.message, 'error');
                }
            }
        }

        async function connectCoinbase() {
            console.log('Connecting to Coinbase...');
            closeModal();

            try {
                updateStatus('Connecting to Coinbase Wallet...', 'info');

                if (!window.ethereum || !window.ethereum.isCoinbaseWallet) {
                    updateStatus('Coinbase Wallet not found! Please install Coinbase Wallet.', 'error');
                    return;
                }

                const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('Coinbase Wallet connected successfully! 💼', 'success');

            } catch (error) {
                console.error('Coinbase connection error:', error);
                updateStatus('Coinbase connection failed: ' + error.message, 'error');
            }
        }

        async function connectAnyWallet() {
            console.log('Connecting to any wallet...');
            closeModal();

            try {
                updateStatus('Connecting to wallet...', 'info');

                if (!window.ethereum) {
                    updateStatus('No wallet found! Please install MetaMask or another Web3 wallet.', 'error');
                    return;
                }

                const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                userAddress = await signer.getAddress();
                isConnected = true;

                await checkAndAddNetwork();
                updateWalletUI();
                updateStatus('Wallet connected successfully! 💳', 'success');

            } catch (error) {
                console.error('Wallet connection error:', error);
                updateStatus('Wallet connection failed: ' + error.message, 'error');
            }
        }

        // Check and add Monad network
        async function checkAndAddNetwork() {
            try {
                const network = await provider.getNetwork();
                console.log('Current network:', network);

                if (network.chainId !== 10143) {
                    updateStatus('Adding Monad Testnet...', 'warning');
                    await window.ethereum.request({
                        method: 'wallet_addEthereumChain',
                        params: [{
                            chainId: '0x279F', // 10143 in hex
                            chainName: 'Monad Testnet',
                            rpcUrls: ['https://testnet-rpc.monad.xyz'],
                            nativeCurrency: {
                                name: 'MON',
                                symbol: 'MON',
                                decimals: 18
                            },
                            blockExplorerUrls: ['https://testnet-explorer.monad.xyz']
                        }]
                    });
                    updateStatus('Monad Testnet added successfully! 🌐', 'success');
                }
            } catch (error) {
                console.error('Network error:', error);
                updateStatus('Network setup completed (some features may be limited)', 'warning');
            }
        }

        // Disconnect wallet
        function disconnectWallet() {
            console.log('Disconnecting wallet...');
            provider = null;
            signer = null;
            userAddress = null;
            isConnected = false;

            updateWalletUI();
            updateStatus('Wallet disconnected', 'info');
        }

        // Copy address
        async function copyAddress() {
            if (userAddress) {
                try {
                    await navigator.clipboard.writeText(userAddress);
                    updateStatus('Address copied to clipboard! 📋', 'success');
                } catch (error) {
                    updateStatus('Failed to copy address', 'error');
                }
            }
        }

        // Test functions
        function testWallets() {
            console.log('=== COMPREHENSIVE WALLET TEST ===');
            const wallets = detectWallets();

            let statusMessage = 'Wallet Detection: ';
            if (wallets.metamask) statusMessage += 'MetaMask ✓ ';
            if (wallets.coinbase) statusMessage += 'Coinbase ✓ ';
            if (wallets.trust) statusMessage += 'Trust ✓ ';
            if (wallets.hasEthereum) statusMessage += 'Ethereum ✓ ';

            if (!wallets.hasEthereum) {
                statusMessage = 'No wallet detected. Please install MetaMask.';
            }

            updateStatus(statusMessage, wallets.hasEthereum ? 'success' : 'error');
        }

        function forceConnect() {
            console.log('Force connecting...');
            if (window.ethereum) {
                connectAnyWallet();
            } else {
                updateStatus('No wallet detected. Please install MetaMask.', 'error');
            }
        }

        // Listen for account changes
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', (accounts) => {
                console.log('Accounts changed:', accounts);
                if (accounts.length === 0) {
                    disconnectWallet();
                } else {
                    location.reload();
                }
            });

            window.ethereum.on('chainChanged', (chainId) => {
                console.log('Chain changed:', chainId);
                location.reload();
            });
        }

        console.log('All functions loaded successfully');
    </script>
</body>
</html>
    </script>
</body>
</html>
