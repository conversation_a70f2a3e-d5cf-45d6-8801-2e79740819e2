<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Werant - Custom Wallet Connect Modal</title>
    <meta name="description" content="Werant - Blockchain voting platform with custom wallet connect modal on Monad testnet" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-content {
        text-align: center;
        color: white;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app loads */
      .loaded #loading-screen {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">🚀 Werant</div>
        <div class="loading-subtext">Loading custom wallet modal...</div>
      </div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <!-- Main Script -->
    <script type="module" src="/main.tsx"></script>
    
    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          document.body.classList.add('loaded');
          setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
              loadingScreen.remove();
            }
          }, 500);
        }, 1000);
      });
    </script>
  </body>
</html>
