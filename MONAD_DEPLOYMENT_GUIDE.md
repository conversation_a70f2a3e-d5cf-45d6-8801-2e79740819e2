# 🚀 Monad Testnet Deployment Guide

## 📋 Current Status

✅ **Smart Contracts**: Compiled and ready for deployment  
✅ **Backend API**: Running on port 3002  
✅ **WalletConnect Integration**: Multiple wallet support added  
✅ **Local Testing**: Contracts deployed locally for development  
⏳ **Monad Testnet**: Ready for deployment (needs MON tokens)  

## 🔧 What's Been Implemented

### 🔗 Multi-Wallet Support
- **MetaMask**: Browser extension wallet
- **WalletConnect**: Mobile wallets (Trust, Rainbow, etc.)
- **Coinbase Wallet**: Coinbase's wallet solution

### 💰 Fee Structure
- **Wallet Connection**: 0.25 MON (one-time)
- **Voting**: 0.1 MON per vote
- **Project Creation**: Gas fees only

### 📱 Frontend Features
- Multi-wallet connection options
- Automatic Monad Testnet network addition
- Real-time transaction tracking
- Connection status verification

## 🌐 Monad Testnet Configuration

### Network Details
- **Chain ID**: 10143 (0x279F in hex)
- **RPC URL**: https://testnet-rpc.monad.xyz
- **Explorer**: https://testnet-explorer.monad.xyz
- **Currency**: MON

### Add to MetaMask
```javascript
{
  "chainId": "0x279F",
  "chainName": "Monad Testnet",
  "rpcUrls": ["https://testnet-rpc.monad.xyz"],
  "nativeCurrency": {
    "name": "MON",
    "symbol": "MON",
    "decimals": 18
  },
  "blockExplorerUrls": ["https://testnet-explorer.monad.xyz"]
}
```

## 💸 Getting MON Tokens

### Option 1: Official Faucet
1. Visit the official Monad testnet faucet
2. Connect your wallet
3. Request testnet MON tokens
4. Wait for confirmation

### Option 2: Community Faucets
- Check Monad Discord for community faucets
- Follow Monad Twitter for faucet announcements
- Join Monad Telegram for faucet links

### Option 3: Bridge from Other Testnets
- Some bridges may support Monad testnet
- Check official Monad documentation

## 🚀 Deployment Steps

### 1. Get MON Tokens
```bash
# You need at least 1 MON for deployment
# Recommended: 5+ MON for testing
```

### 2. Update Private Key
```bash
# Edit .env file
DEPLOYER_PRIVATE_KEY=your_private_key_with_MON_tokens
```

### 3. Deploy Contracts
```bash
npx hardhat run scripts/deploy.js --network monad-testnet
```

### 4. Verify Deployment
```bash
# Check contract addresses in .env
cat .env | grep CONTRACT_ADDRESS

# Test API
curl http://localhost:3002/api/contracts/info
```

## 🧪 Testing the Complete System

### 1. Frontend Testing
1. Open `frontend-example.html`
2. Click "Add Monad Testnet" to add network
3. Choose wallet type (MetaMask, WalletConnect, Coinbase)
4. Connect wallet
5. Test "Connect to Werant" (0.25 MON fee)

### 2. API Testing
```bash
# Health check
curl http://localhost:3002/health

# Contract info
curl http://localhost:3002/api/contracts/info

# Wallet status
curl http://localhost:3002/api/wallet/status/YOUR_ADDRESS

# Network status
curl http://localhost:3002/api/contracts/network
```

### 3. Voting Flow Testing
1. Connect wallet and pay 0.25 MON connection fee
2. Create a project (gas fees only)
3. Vote on project (0.1 MON per vote)
4. Check leaderboard updates

## 📊 Current Contract Addresses (Local Testing)

```
WerantWalletConnect: ******************************************
WerantLeaderboard: ******************************************
```

*These are local addresses for development. Real Monad testnet addresses will be generated after deployment.*

## 🔍 Verification Steps

### After Deployment
1. **Contract Verification**:
   ```bash
   # Verify on Monad Explorer
   npx hardhat verify --network monad-testnet CONTRACT_ADDRESS
   ```

2. **Functionality Testing**:
   - Test wallet connection with real MON
   - Test voting with real transactions
   - Verify leaderboard updates

3. **Frontend Integration**:
   - Update contract addresses in frontend
   - Test all wallet types
   - Verify transaction confirmations

## 🛠️ Troubleshooting

### Common Issues

1. **"Insufficient funds"**:
   - Get more MON from faucet
   - Check gas price settings

2. **"Network not supported"**:
   - Add Monad Testnet to wallet
   - Check chain ID (10143)

3. **"Transaction failed"**:
   - Check gas limits
   - Verify contract addresses

4. **"Wallet not connected"**:
   - Must connect wallet before voting
   - Pay 0.25 MON connection fee first

### Debug Commands
```bash
# Check deployment status
ls deployments/

# View contract ABIs
curl http://localhost:3002/api/contracts/info | jq '.data.abis'

# Check recent events
curl http://localhost:3002/api/contracts/events
```

## 🎯 Next Steps After Deployment

1. **Production Frontend**: Build React/Vue app
2. **Mobile App**: React Native or Flutter
3. **Advanced Features**: Categories, reputation system
4. **Analytics**: User behavior tracking
5. **Governance**: DAO features for platform decisions

## 📞 Support Resources

- **Monad Documentation**: https://docs.monad.xyz
- **Monad Discord**: Official community support
- **GitHub Issues**: Report bugs and feature requests
- **API Documentation**: Available at `/api/contracts/info`

## 🎉 Summary

Your Werant project is **100% ready** for Monad Testnet deployment! 

**What you have**:
- ✅ Multi-wallet support (MetaMask, WalletConnect, Coinbase)
- ✅ Smart contracts with 0.25 MON connection + 0.1 MON voting fees
- ✅ Complete backend API with 12+ endpoints
- ✅ Frontend demo with transaction handling
- ✅ Local testing environment

**What you need**:
- 🔸 MON tokens from testnet faucet
- 🔸 Run deployment command
- 🔸 Test with real transactions

**Ready to launch on Monad Testnet!** 🚀
