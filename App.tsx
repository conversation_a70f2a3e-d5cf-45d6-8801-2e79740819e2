import React, { useState } from 'react';
import { useAccount } from 'wagmi';
import CustomWalletModal from './CustomWalletModal';
import './App.css';

const App: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { address, isConnected } = useAccount();

  const shortenAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  return (
    <div className="app">
      <div className="container">
        <div className="header">
          <h1>🚀 Werant</h1>
          <p>Custom Wallet Connect Modal with React & Wagmi</p>
        </div>

        <div className="fee-info">
          <h3>💰 Fee Structure</h3>
          <ul>
            <li><strong>Wallet Connection:</strong> 0.25 MON (one-time access fee)</li>
            <li><strong>Voting:</strong> 0.1 MON per vote</li>
            <li><strong>Project Creation:</strong> Gas fees only</li>
          </ul>
        </div>

        <div className="wallet-section">
          {isConnected ? (
            <div className="connected-wallet">
              <div className="wallet-info">
                <div className="connection-indicator">
                  <span className="status-dot connected"></span>
                  <span>Connected</span>
                </div>
                <div className="wallet-address">
                  {shortenAddress(address!)}
                </div>
                <div className="wallet-network">
                  Monad Testnet
                </div>
              </div>
              <button 
                className="gradient-btn secondary"
                onClick={() => setIsModalOpen(true)}
              >
                <i className="icon">⚙️</i>
                Manage Wallet
              </button>
            </div>
          ) : (
            <button 
              className="gradient-btn primary"
              onClick={() => setIsModalOpen(true)}
            >
              <i className="icon">🔗</i>
              Connect Wallet
            </button>
          )}
        </div>

        <div className="features">
          <div className="feature-card">
            <div className="feature-icon">🗳️</div>
            <h3>Decentralized Voting</h3>
            <p>Vote on projects and proposals with transparent blockchain technology</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">🏆</div>
            <h3>Leaderboard</h3>
            <p>Track top projects and contributors in the ecosystem</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">💎</div>
            <h3>Rewards</h3>
            <p>Earn MON tokens for participating in governance</p>
          </div>
        </div>

        <div className="stats">
          <div className="stat-item">
            <div className="stat-number">1,234</div>
            <div className="stat-label">Total Votes</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">567</div>
            <div className="stat-label">Active Projects</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">89</div>
            <div className="stat-label">Contributors</div>
          </div>
        </div>

        <CustomWalletModal 
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      </div>
    </div>
  );
};

export default App;
